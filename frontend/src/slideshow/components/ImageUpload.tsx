import React from 'react';
import { useSlideshowContext } from '../context/SlideshowContext';
import { slideshowTheme } from '../../shared/components/Media';
import BaseUpload from '../../shared/components/base/BaseUpload';
import type { MediaItem as MediaItemType, UploadConfig, MediaListConfig } from '../../shared/types/media.types';

const ImageUpload: React.FC = () => {
    const {
        project,
        uploadImages,
        addToTimeline,
        removeImage,
        isUploading
    } = useSlideshowContext();



    // Upload configuration
    const uploadConfig: UploadConfig = {
        accept: ['image/*'],
        multiple: true,
        maxSize: 10 * 1024 * 1024, // 10MB
        autoUpload: false,
        showEmptyState: true
    };

    // Media list configuration
    const listConfig: MediaListConfig = {
        layout: 'list',
        size: 'medium',
        showActions: false, // Hide actions since click adds to timeline
        showMetadata: false, // Clean look without metadata
        showSelection: false,
        sortable: false,
        selectable: false,
    };

    // Convert slideshow image to MediaItem format
    const convertToMediaItem = (image: typeof project.images[0]): MediaItemType => ({
        id: image.id,
        file: image.file,
        name: image.name,
        type: 'image' as const,
        size: image.file.size,
        preview: image.preview,
        uploadedInfo: image.uploadedInfo ? {
            sessionId: '',
            uploadedAt: new Date(),
            ...image.uploadedInfo
        } : undefined,
        createdAt: new Date(),
        updatedAt: new Date(),
    });

    // Handle upload
    const handleUpload = async (files: File[]) => {
        await uploadImages(files);
    };

    // Handle item actions
    const handleItemAction = (action: string, item: typeof project.images[0]) => {
        switch (action) {
            case 'add':
                addToTimeline(item.id);
                break;
            case 'remove':
                removeImage(item.id);
                break;
            default:
                console.log(`Unhandled action: ${action}`);
        }
    };

    // Handle upload errors
    const handleUploadError = (errors: Array<{ file: File; error: string }>) => {
        errors.forEach(error => {
            console.error('Upload validation failed:', error.error, error.file.name);
        });
    };



    return (
        <BaseUpload
            mode="slideshow"
            items={project.images}
            uploadConfig={uploadConfig}
            listConfig={listConfig}
            theme={slideshowTheme}
            onUpload={handleUpload}
            onItemAction={handleItemAction}
            onError={handleUploadError}
            convertToMediaItem={convertToMediaItem}
            isUploading={isUploading}
            customContent={
                <div className="flex flex-col h-full">
                    {/* Scrollable images list - takes remaining space */}
                    <div className="flex-1 min-h-0 overflow-y-auto">
                        <BaseUpload
                            mode="slideshow"
                            items={project.images}
                            uploadConfig={uploadConfig}
                            listConfig={listConfig}
                            theme={slideshowTheme}
                            onUpload={handleUpload}
                            onItemAction={handleItemAction}
                            onError={handleUploadError}
                            convertToMediaItem={convertToMediaItem}
                            isUploading={isUploading}
                            showDropZone={false}
                            className="h-auto"
                        />
                    </div>

                    {/* Fixed space reserved for ALL TO TIMELINE button */}
                    <div className="flex-shrink-0" style={{
                        height: '60px',
                        display: 'flex',
                        alignItems: 'center',
                        paddingTop: '8px'
                    }}>
                        <button
                            onClick={() => {
                                project.images.forEach(image => addToTimeline(image.id));
                            }}
                            disabled={isUploading || project.images.length === 0}
                            style={{
                                width: '100%',
                                padding: '12px',
                                fontSize: '14px',
                                fontWeight: '600',
                                backgroundColor: (isUploading || project.images.length === 0)
                                    ? 'rgba(156, 163, 175, 0.15)'
                                    : 'rgba(236, 72, 153, 0.15)',
                                color: (isUploading || project.images.length === 0)
                                    ? '#9ca3af'
                                    : '#ec4899',
                                border: `1px solid ${(isUploading || project.images.length === 0)
                                    ? '#9ca3af'
                                    : '#ec4899'}`,
                                borderRadius: '3px',
                                cursor: (isUploading || project.images.length === 0)
                                    ? 'not-allowed'
                                    : 'pointer',
                                fontFamily: '"Space Mono", monospace',
                                textTransform: 'uppercase',
                                letterSpacing: '0.5px',
                                transition: 'all 0.2s ease',
                                opacity: (isUploading || project.images.length === 0) ? 0.6 : 1,
                            }}
                            onMouseEnter={(e) => {
                                if (!isUploading && project.images.length > 0) {
                                    e.currentTarget.style.backgroundColor = 'rgba(236, 72, 153, 0.25)';
                                }
                            }}
                            onMouseLeave={(e) => {
                                if (!isUploading && project.images.length > 0) {
                                    e.currentTarget.style.backgroundColor = 'rgba(236, 72, 153, 0.15)';
                                }
                            }}
                        >
                            ALL TO TIMELINE {project.images.length > 0 ? `(${project.images.length})` : ''}
                        </button>
                    </div>
                </div>
            }
        />
    );
};

export default ImageUpload;
