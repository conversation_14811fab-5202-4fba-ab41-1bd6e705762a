import React, { useEffect, useMemo } from 'react';
import { useSlideshowContext } from '../context/SlideshowContext';
import { UnifiedPreviewEmptyState } from '../../shared/components/unified';
import BaseVideoPreview from '../../shared/components/base/BaseVideoPreview';

const Preview: React.FC = () => {
  const { preview, hasTimeline, generatePreview, project } = useSlideshowContext();

  // Auto-generate preview when timeline changes (but not on errors)
  useEffect(() => {
    // Auto-generate when:
    // 1. Timeline has items
    // 2. Not currently generating
    // 3. No preview exists (either initial load or after timeline change)
    // 4. No error state
    if (hasTimeline && !preview.isGenerating && !preview.url && !preview.error) {
      console.log('🎬 Auto-generating preview after timeline change...');
      generatePreview();
    }
  }, [hasTimeline, preview.url, preview.isGenerating, preview.error, generatePreview]);

  // DISABLED: Auto-regeneration on timeline changes to prevent infinite loops
  // Users can manually regenerate preview when needed
  // TODO: Implement smarter change detection that doesn't trigger on internal updates

  if (!hasTimeline) {
    return <UnifiedPreviewEmptyState mode="slideshow" />;
  }

  // Custom loading content for slideshow
  const loadingContent = (
    <div className="flex flex-col items-center gap-3">
      <div className="w-8 h-8 border-3 border-dark-700 border-t-accent-orange rounded-full animate-spin" />
      <div className="text-accent-orange text-sm font-bold font-mono">
        Generating Preview...
      </div>
    </div>
  );

  // Custom error content for slideshow
  const errorContent = preview.error ? (
    <div className="text-center text-accent-red font-mono">
      <div className="text-lg mb-2">Preview Error</div>
      <div className="text-sm mb-3">{preview.error}</div>
      <button
        onClick={generatePreview}
        className="btn-pink text-sm py-2 px-4"
      >
        Retry Preview
      </button>
    </div>
  ) : null;

  // Regenerate button for when preview exists but user wants to update
  const regenerateButton = preview.url && !preview.isGenerating ? (
    <div className="absolute top-4 right-4">
      <button
        onClick={generatePreview}
        className="bg-accent-orange hover:bg-orange-600 text-white text-xs py-1 px-3 rounded font-mono transition-colors"
        title="Regenerate preview with current timeline settings"
      >
        🔄 Update Preview
      </button>
    </div>
  ) : null;

  return (
    <div className="relative h-full">
      <BaseVideoPreview
        videoSrc={preview.url || ''}
        mode="slideshow"
        isLoading={preview.isGenerating}
        error={preview.error}
        loadingContent={loadingContent}
        errorContent={errorContent}
        onVideoLoaded={() => {
          console.log('🎬 Slideshow preview loaded');
        }}
        onVideoError={(e) => {
          console.error('❌ Slideshow preview error:', e);
        }}
        showControls={true}
        autoPlay={true}
        loop={true}
        muted={true}
        containerClassName="h-full"
      />
      {regenerateButton}
    </div>
  );
};

export default Preview;
