import { useCallback } from 'react';
import { TimelineItem, ImageFile, PreviewResponse } from '../types/slideshow.types';

const API_BASE_URL = window.location.hostname === 'localhost' 
  ? 'http://localhost:3001' 
  : window.location.origin;

// API function for master generation (replaces preview)
const generateMasterAPI = async (payload: any): Promise<PreviewResponse> => {
  console.log('🌐 Making API call to:', `${API_BASE_URL}/generate-master`);
  console.log('📤 Request payload:', payload);

  // Create AbortController for timeout
  const controller = new AbortController();
  const timeoutId = setTimeout(() => {
    console.error('⏰ API call timeout after 60 seconds');
    controller.abort();
  }, 60000); // 60 second timeout

  try {
    const response = await fetch(`${API_BASE_URL}/generate-master`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload),
      signal: controller.signal
    });

    clearTimeout(timeoutId);
    console.log('📥 API response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ API error response:', errorText);
      let errorMessage = `Preview API error (${response.status})`;

      try {
        const errorData = JSON.parse(errorText);
        errorMessage = errorData.error || errorData.details || errorMessage;
      } catch {
        errorMessage = errorText || errorMessage;
      }

      throw new Error(errorMessage);
    }

    const result = await response.json();
    console.log('✅ API success response:', result);
    return result;

  } catch (error) {
    clearTimeout(timeoutId);
    if (error.name === 'AbortError') {
      throw new Error('Request timeout - preview generation took too long');
    }
    console.error('❌ API call failed:', error);
    throw error;
  }
};

export interface PreviewState {
  url: string | null;
  isGenerating: boolean;
  error: string | null;
}

export interface PreviewActions {
  generatePreview: () => Promise<void>;
  clearPreview: () => void;
}

export interface UsePreviewGenerationProps {
  timeline: TimelineItem[];
  images: ImageFile[];
  sessionId?: string;
  updatePreviewState: (updates: Partial<PreviewState>) => void;
}

export const usePreviewGeneration = ({
  timeline,
  images,
  sessionId,
  updatePreviewState
}: UsePreviewGenerationProps): PreviewActions => {
  
  const generatePreview = useCallback(async () => {
    console.log('🎬 generatePreview called');
    console.log('🔍 Debug info:', {
      timelineLength: timeline.length,
      imagesLength: images.length,
      sessionId,
      hasSessionId: !!sessionId
    });

    if (timeline.length === 0) {
      console.warn('⚠️ Timeline is empty, cannot generate preview');
      return;
    }

    if (!sessionId) {
      console.error('❌ No sessionId available for preview generation');
      updatePreviewState({
        isGenerating: false,
        error: 'No session ID available'
      });
      return;
    }

    console.log('🚀 Starting preview generation...');
    updatePreviewState({
      isGenerating: true,
      error: null
    });

    try {
      const payload = {
        images: timeline.map(item => {
          const image = images.find(img => img.id === item.imageId);
          const filename = image?.uploadedInfo?.filename || image?.name;
          console.log(`🔍 Timeline item ${item.id} → imageId: ${item.imageId} → found image: ${image?.id} → filename: ${filename}`);
          console.log(`🔍 Available images: ${images.map(img => `${img.id}:${img.name}`).join(', ')}`);
          if (!image) {
            console.error(`❌ No image found for timeline item ${item.id} with imageId ${item.imageId}`);
          }
          return { filename };
        }),
        transitions: timeline.slice(0, -1).map((item, index) => {
          const transition = item.transition || { type: 'fade', duration: 500 };
          console.log(`🎭 Transition ${index}: ${transition.type} (${transition.duration}ms)`);
          return {
            type: transition.type,
            duration: transition.duration
          };
        }),
        frameDurations: timeline.map(item => item.duration),
        sessionId: sessionId,
        // Add default resolution to prevent undefined values
        width: 1920,
        height: 1080,
        fps: 30
      };

      console.log('🎬 Preview payload:', payload);
      console.log(`🎭 Total transitions in payload: ${payload.transitions.length}`);
      console.log(`🎭 Transition details:`, payload.transitions);
      console.log('🔍 State project:', {
        timelineLength: timeline.length,
        imagesLength: images.length,
        sessionId: sessionId
      });
      
      if (!payload.sessionId) {
        throw new Error('No session ID available. Please upload images first.');
      }

      if (payload.images.length === 0) {
        throw new Error('No images in timeline. Please add images to timeline first.');
      }

      console.log('🚀 Calling generateMasterAPI with payload:', payload);
      console.log('🔍 API endpoint will be: /generate-master');

      const result = await generateMasterAPI(payload);
      console.log('✅ generateMasterAPI completed, result:', result);
      
      console.log('🎬 Master generation result:', result);
      
      if (result.success) {
        // Use direct URL for better streaming support and memory efficiency
        const videoUrl = result.previewUrl || result.masterUrl;
        if (!videoUrl) {
          throw new Error('No video URL returned from master generation');
        }
        const directVideoUrl = `${API_BASE_URL}${videoUrl}?t=${Date.now()}`;
        
        console.log('🎬 Master video URL created');
        updatePreviewState({
          url: directVideoUrl,
          isGenerating: false,
          error: null
        });
      } else {
        throw new Error(result.message || 'Master generation failed');
      }
    } catch (error) {
      console.error('❌ Master generation failed:', error);
      updatePreviewState({
        isGenerating: false,
        error: error instanceof Error ? error.message : 'Master generation failed'
      });
    }
  }, [timeline, images, sessionId, updatePreviewState]);

  const clearPreview = useCallback(() => {
    // Clean up blob URL if it exists
    // Note: We can't access the current state here, so we'll just set the new state
    updatePreviewState({ 
      url: null, 
      error: null 
    });
  }, [updatePreviewState]);

  return {
    generatePreview,
    clearPreview
  };
};
