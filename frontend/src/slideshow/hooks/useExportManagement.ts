import { useCallback } from 'react';
import { TimelineItem, ImageFile, ExportSettings } from '../types/slideshow.types';

const API_BASE_URL = window.location.hostname === 'localhost'
  ? 'http://localhost:3001'
  : window.location.origin;








export interface ExportState {
  isExporting: boolean;
  progress: number;
  lastResult: string | null;
  error: string | null;
  currentStep?: string;
  isCompleted: boolean;
  downloadUrl?: string;
}

export interface ExportActions {
  exportSlideshow: () => Promise<void>;
  updateExportSettings: (updates: Partial<ExportSettings>) => void;
}

export interface UseExportManagementProps {
  timeline: TimelineItem[];
  images: ImageFile[];
  sessionId?: string;
  exportSettings: ExportSettings;
  updateExportState: (updates: Partial<ExportState>) => void;
  updateExportSettingsState: (updates: Partial<ExportSettings>) => void;
  previewUrl?: string | null; // Add preview URL to extract masterId
}

export const useExportManagement = ({
  timeline,
  images,
  sessionId,
  exportSettings,
  updateExportState,
  updateExportSettingsState,
  previewUrl
}: UseExportManagementProps): ExportActions => {

  // Helper function to extract masterId from preview URL
  const extractMasterIdFromPreview = useCallback((url: string | null): string | null => {
    if (!url) return null;

    // Extract masterId from URL like "/output/master_1753989825723_82w9fdwbl.mp4"
    const match = url.match(/master_(\d+_[a-z0-9]+)\.mp4/);
    return match ? `master_${match[1]}` : null;
  }, []);



  // NEW: Export using master video as base with enhanced progress tracking
  const exportFromMaster = useCallback(async (masterId: string) => {
    if (!masterId) {
      throw new Error('Master ID is required');
    }

    // Progress starts at 50% (preview already generated)
    updateExportState({
      progress: 60,
      currentStep: `Converting to ${exportSettings.format.toUpperCase()} (${exportSettings.quality} quality)...`
    });

    try {
      // Map quality names to numbers (1-5 scale)
      const qualityMap: { [key: string]: number } = {
        'low': 1,
        'standard': 3,
        'high': 4,
        'maximum': 5
      };

      const qualityNumber = qualityMap[exportSettings.quality] || 3;

      const payload = {
        masterId,
        quality: qualityNumber,
        sessionId: sessionId
      };

      console.log('🔄 Step 2: Converting master to format using new API:', payload);

      // Simulate conversion progress
      updateExportState({
        progress: 70,
        currentStep: `Processing ${exportSettings.format.toUpperCase()} conversion...`
      });

      const response = await fetch(`${API_BASE_URL}/api/convert/${exportSettings.format}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Format conversion failed');
      }

      const result = await response.json();
      console.log('✅ Step 2 completed: Export from master result:', result);

      // Map backend response to frontend expected format
      const downloadUrl = result.exportUrl || result.downloadUrl;
      console.log('🔗 Download URL mapped:', downloadUrl);

      // Validate download URL before setting state
      if (!downloadUrl) {
        console.error('❌ No download URL found in response:', result);
        throw new Error('Export completed but no download URL provided');
      }

      console.log('✅ Setting export state with download URL:', downloadUrl);

      // Final completion state
      updateExportState({
        isExporting: false,
        progress: 100,
        currentStep: `${exportSettings.format.toUpperCase()} export completed!`,
        downloadUrl: downloadUrl,
        isCompleted: true
      });

      // Auto-trigger download
      if (result.downloadUrl) {
        setTimeout(() => {
          const link = document.createElement('a');
          link.href = `${API_BASE_URL}${result.downloadUrl}`;
          link.download = result.filename || `export.${exportSettings.format}`;
          link.style.display = 'none';
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        }, 500);
      }

      return result;
    } catch (error) {
      console.error('❌ Export from master failed:', error);
      updateExportState({
        isExporting: false,
        error: error instanceof Error ? error.message : 'Format conversion failed',
        progress: 0,
        currentStep: 'Export failed',
        isCompleted: false
      });
      throw error;
    }
  }, [exportSettings, sessionId, updateExportState]);

  const exportSlideshow = useCallback(async () => {
    if (timeline.length === 0) return;

    updateExportState({
      isExporting: true,
      error: null,
      progress: 0,
      currentStep: 'Preparing export...',
      isCompleted: false,
      downloadUrl: undefined
    });

    try {
      // Check if we can reuse existing master from preview
      const existingMasterId = extractMasterIdFromPreview(previewUrl || null);

      if (existingMasterId) {
        console.log('🚀 Reusing existing master from preview:', existingMasterId);

        updateExportState({
          progress: 50,
          currentStep: `Converting existing master to ${exportSettings.format.toUpperCase()}...`
        });

        // Skip master generation, go directly to conversion
        const exportResult = await exportFromMaster(existingMasterId);
        console.log('✅ Export completed using existing master:', exportResult);
        return exportResult;
      }

      // Step 1: Generate high-quality master video (up to 4K, CRF 16-18, bitrate 8-15M)
      console.log('🎬 No existing master found, generating new one...');
      updateExportState({
        progress: 10,
        currentStep: 'Generating high-quality master video...'
      });

      const masterPayload = {
        images: timeline.map(item => {
          const image = images.find(img => img.id === item.imageId);
          return { path: image?.uploadedInfo?.path || image?.uploadedInfo?.filename || image?.name };
        }),
        transitions: timeline.slice(0, -1).map(item => ({
          type: item.transition?.type || 'cut',
          duration: item.transition?.duration || 0
        })),
        frameDurations: timeline.map(item => item.duration),
        sessionId: sessionId,
        // Add default resolution to prevent undefined values
        width: 1920,
        height: 1080,
        fps: 30
      };

      console.log('🎬 Step 1: Generating high-quality master for export:', masterPayload);

      const masterResponse = await fetch(`${API_BASE_URL}/generate-master`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(masterPayload)
      });

      if (!masterResponse.ok) {
        const error = await masterResponse.json();
        throw new Error(error.error || 'High-quality master generation failed');
      }

      const masterResult = await masterResponse.json();
      console.log('✅ Step 1 completed: High-quality master generated:', masterResult);

      // Step 2: Convert master to desired format
      updateExportState({
        progress: 50,
        currentStep: `Converting to ${exportSettings.format.toUpperCase()} (${exportSettings.quality})...`
      });

      console.log('🔄 Step 2: Converting master to final format');
      const exportResult = await exportFromMaster(masterResult.masterId);

      console.log('✅ Export completed successfully:', exportResult);
      return exportResult;
    } catch (error) {
      console.error('❌ Export slideshow failed:', error);
      updateExportState({
        isExporting: false,
        error: error instanceof Error ? error.message : 'Export failed',
        progress: 0,
        currentStep: 'Export failed',
        isCompleted: false
      });
      throw error;
    }
  }, [timeline, images, sessionId, exportSettings, updateExportState, exportFromMaster]);

  const updateExportSettings = useCallback((updates: Partial<ExportSettings>) => {
    updateExportSettingsState(updates);
  }, [updateExportSettingsState]);

  return {
    exportSlideshow,
    updateExportSettings
  };
};
