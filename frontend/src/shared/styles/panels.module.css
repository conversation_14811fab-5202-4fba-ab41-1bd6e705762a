/**
 * Panels CSS Module
 * 
 * Centralized styles for panel components across AnimaGen.
 * Eliminates inline style duplication for common panel layouts.
 */

/* Base Panel */
.panel {
  background-color: #0a0a0b;
  border-radius: 8px;
  border: 1px solid #374151;
  padding: 16px;
  color: #f3f4f6;
}

/* Panel Variants */
.panelPrimary {
  border-color: #ec4899;
  box-shadow: 0 0 0 1px rgba(236, 72, 153, 0.1);
}

.panelSecondary {
  border-color: #3b82f6;
  box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.1);
}

.panelSuccess {
  border-color: #10b981;
  box-shadow: 0 0 0 1px rgba(16, 185, 129, 0.1);
}

.panelWarning {
  border-color: #f59e0b;
  box-shadow: 0 0 0 1px rgba(245, 158, 11, 0.1);
}

.panelError {
  border-color: #ef4444;
  box-shadow: 0 0 0 1px rgba(239, 68, 68, 0.1);
}

/* Panel Sizes */
.panelSmall {
  padding: 8px;
  border-radius: 4px;
}

.panelMedium {
  padding: 16px;
  border-radius: 6px;
}

.panelLarge {
  padding: 24px;
  border-radius: 8px;
}

/* Panel Header */
.panelHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #374151;
}

.panelTitle {
  font-family: "Space Mono", monospace;
  font-size: 16px;
  font-weight: bold;
  color: #f3f4f6;
  margin: 0;
}

.panelSubtitle {
  font-family: "Space Mono", monospace;
  font-size: 12px;
  color: #9ca3af;
  margin: 0;
}

/* Panel Content */
.panelContent {
  flex: 1;
}

/* Panel Footer */
.panelFooter {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px solid #374151;
}

/* Panel Actions */
.panelActions {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Panel Grid */
.panelGrid {
  display: grid;
  gap: 16px;
}

.panelGrid2 {
  grid-template-columns: repeat(2, 1fr);
}

.panelGrid3 {
  grid-template-columns: repeat(3, 1fr);
}

.panelGrid4 {
  grid-template-columns: repeat(4, 1fr);
}

/* Panel Flex */
.panelFlex {
  display: flex;
  gap: 16px;
}

.panelFlexColumn {
  flex-direction: column;
}

.panelFlexRow {
  flex-direction: row;
}

/* Panel States */
.panelLoading {
  opacity: 0.6;
  pointer-events: none;
}

.panelDisabled {
  opacity: 0.4;
  pointer-events: none;
}

/* Panel Hover Effects */
.panelHoverable {
  transition: all 0.2s ease;
  cursor: pointer;
}

.panelHoverable:hover {
  border-color: #6b7280;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  transform: translateY(-1px);
}

/* Panel Scrollable */
.panelScrollable {
  overflow-y: auto;
  max-height: 400px;
}

.panelScrollable::-webkit-scrollbar {
  width: 6px;
}

.panelScrollable::-webkit-scrollbar-track {
  background-color: #1f2937;
  border-radius: 3px;
}

.panelScrollable::-webkit-scrollbar-thumb {
  background-color: #4b5563;
  border-radius: 3px;
}

.panelScrollable::-webkit-scrollbar-thumb:hover {
  background-color: #6b7280;
}

/* Panel Collapsible */
.panelCollapsible .panelHeader {
  cursor: pointer;
  user-select: none;
}

.panelCollapsed .panelContent {
  display: none;
}

.panelCollapsed .panelFooter {
  display: none;
}

/* Panel Animation */
.panelEnter {
  animation: panelEnter 0.3s ease-out;
}

.panelExit {
  animation: panelExit 0.2s ease-in;
}

@keyframes panelEnter {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes panelExit {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.95);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .panel {
    padding: 12px;
    border-radius: 6px;
  }
  
  .panelHeader {
    margin-bottom: 12px;
    padding-bottom: 8px;
  }
  
  .panelTitle {
    font-size: 14px;
  }
  
  .panelSubtitle {
    font-size: 11px;
  }
  
  .panelFooter {
    margin-top: 12px;
    padding-top: 8px;
  }
  
  .panelGrid2,
  .panelGrid3,
  .panelGrid4 {
    grid-template-columns: 1fr;
  }
  
  .panelFlex {
    flex-direction: column;
  }
}

/* Dark theme consistency */
.panel {
  background-color: #0a0a0b;
  color: #f3f4f6;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .panel {
    border-width: 2px;
  }
  
  .panelTitle {
    font-weight: 900;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .panelHoverable {
    transition: none;
  }
  
  .panelHoverable:hover {
    transform: none;
  }
  
  .panelEnter,
  .panelExit {
    animation: none;
  }
}
