/**
 * Timeline CSS Module
 * 
 * Centralized styles for timeline components across AnimaGen.
 * Eliminates inline style duplication between Timeline.tsx and VideoTimelineBuilder.tsx.
 */

/* Timeline Container */
.timelineContainer {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  background-color: #0a0a0b;
  border-radius: 8px;
  border: 1px solid #374151;
  height: 100%;
}

/* Timeline Track */
.timelineTrack {
  display: flex;
  align-items: center;
  gap: 12px;
  overflow-x: auto;
  overflow-y: hidden;
  padding: 8px 0;
  min-height: 140px;
  background-color: #1f2937;
  border-radius: 6px;
  border: 1px solid #374151;
}

/* Timeline Item Base */
.timelineItem {
  position: relative;
  flex-shrink: 0;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  overflow: hidden;
}

/* Timeline Item - Image */
.timelineItemImage {
  width: 120px;
  height: 80px;
  background-color: #374151;
  border: 2px solid transparent;
}

.timelineItemImage:hover {
  border-color: #ec4899;
  transform: scale(1.02);
}

.timelineItemImage.selected {
  border-color: #ec4899;
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.3);
}

/* Timeline Item - Video Segment */
.timelineItemVideo {
  width: 160px;
  height: 90px;
  background-color: #374151;
  border: 2px solid transparent;
}

.timelineItemVideo:hover {
  border-color: #3b82f6;
  transform: scale(1.02);
}

.timelineItemVideo.selected {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}

/* Timeline Item Content */
.timelineItemContent {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Timeline Item Image/Video */
.timelineItemMedia {
  width: 100%;
  height: 70%;
  object-fit: cover;
  border-radius: 4px 4px 0 0;
}

/* Timeline Item Info */
.timelineItemInfo {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: 4px 8px;
  font-family: "Space Mono", monospace;
  font-size: 10px;
  color: #f3f4f6;
}

/* Timeline Item Duration */
.timelineItemDuration {
  font-weight: bold;
  color: #10b981;
}

/* Timeline Item Index */
.timelineItemIndex {
  position: absolute;
  top: 4px;
  left: 4px;
  background-color: rgba(0, 0, 0, 0.7);
  color: #f3f4f6;
  font-family: "Space Mono", monospace;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 3px;
  font-weight: bold;
}

/* Drag & Drop States */
.timelineItemDragging {
  opacity: 0.5;
  transform: rotate(5deg);
  z-index: 1000;
}

.timelineItemDropTarget {
  border-color: #10b981;
  background-color: rgba(16, 185, 129, 0.1);
}

/* Empty Timeline */
.timelineEmpty {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: 12px;
  min-height: 140px;
  color: #6b7280;
  font-family: "Space Mono", monospace;
}

.timelineEmptyIcon {
  font-size: 48px;
  opacity: 0.6;
}

.timelineEmptyText {
  font-size: 14px;
  text-align: center;
}

/* Timeline Controls */
.timelineControls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  padding: 8px 0;
}

.timelineControlsLeft {
  display: flex;
  align-items: center;
  gap: 8px;
}

.timelineControlsRight {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Timeline Button */
.timelineButton {
  padding: 8px 12px;
  background-color: #374151;
  border: 1px solid #4b5563;
  border-radius: 4px;
  color: #f3f4f6;
  font-family: "Space Mono", monospace;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.timelineButton:hover {
  background-color: #4b5563;
  border-color: #6b7280;
}

.timelineButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Timeline Button - Primary (Slideshow) */
.timelineButtonPrimary {
  background-color: #ec4899;
  border-color: #ec4899;
  color: #ffffff;
}

.timelineButtonPrimary:hover {
  background-color: #db2777;
  border-color: #db2777;
}

/* Timeline Button - Secondary (Video Editor) */
.timelineButtonSecondary {
  background-color: #3b82f6;
  border-color: #3b82f6;
  color: #ffffff;
}

.timelineButtonSecondary:hover {
  background-color: #2563eb;
  border-color: #2563eb;
}

/* Timeline Info */
.timelineInfo {
  font-family: "Space Mono", monospace;
  font-size: 12px;
  color: #9ca3af;
}

/* Scrollbar Styling */
.timelineTrack::-webkit-scrollbar {
  height: 8px;
}

.timelineTrack::-webkit-scrollbar-track {
  background-color: #1f2937;
  border-radius: 4px;
}

.timelineTrack::-webkit-scrollbar-thumb {
  background-color: #4b5563;
  border-radius: 4px;
}

.timelineTrack::-webkit-scrollbar-thumb:hover {
  background-color: #6b7280;
}

/* Responsive Design */
@media (max-width: 768px) {
  .timelineContainer {
    padding: 12px;
    gap: 12px;
  }
  
  .timelineItemImage {
    width: 100px;
    height: 67px;
  }
  
  .timelineItemVideo {
    width: 140px;
    height: 79px;
  }
  
  .timelineItemInfo {
    font-size: 9px;
  }
  
  .timelineButton {
    padding: 6px 10px;
    font-size: 11px;
  }
}

/* Animation for smooth transitions */
.timelineItemEnter {
  animation: timelineItemEnter 0.3s ease-out;
}

.timelineItemExit {
  animation: timelineItemExit 0.2s ease-in;
}

@keyframes timelineItemEnter {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes timelineItemExit {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.8);
  }
}
