import React, { useState, useCallback, useEffect } from 'react';
import { useVideoEditorContext } from '../../context/VideoEditorContext';
import { VideoExportSettings } from '../../types/video-editor.types';
import { showToast } from '../../../shared/components/Toast';
// Removed ExportStrategyFactory - now using Master MP4 + Conversion system
import {
  UnifiedFormatSelector,
  UnifiedQualitySelector,
  UnifiedEmptyState,
  type ExportFormat,
  type QualityLevel
} from '../../../shared/components/export';
import { ExportResolutionSettings } from './ExportResolutionSettings';
import { ExportGIFSettings } from './ExportGIFSettings';
import { useExportValidation, ExportSettings } from '../../../hooks/useExportValidation';
import { ValidationMessages, ValidationSummary } from '../../../components/ValidationMessages';

/**
 * Video Export Builder - Strategy Pattern
 * Uses export strategies for different formats with specialized UI components
 */
export const VideoExportBuilder: React.FC = () => {
  const { project, hasVideo } = useVideoEditorContext();
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const [exportSettings, setExportSettings] = useState<VideoExportSettings>({
    format: 'mp4',
    quality: 'standard',
    resolution: {
      width: project.video?.width || 1920,
      height: project.video?.height || 1080,
      preset: 'original'
    },
    fps: 30,
    gif: {
      loop: 'infinite',
      colors: 256,
      dither: true
    }
  });

  // Convert VideoExportSettings to ExportSettings for validation
  const currentExportSettings: ExportSettings = {
    format: exportSettings.format as any,
    fps: exportSettings.fps,
    quality: exportSettings.quality as any,
    resolution: exportSettings.resolution,
    gif: exportSettings.gif
  };

  // Real-time validation
  const validation = useExportValidation(currentExportSettings);

  // Update settings when format changes - using new Master MP4 + Conversion system
  const handleFormatChange = useCallback((format: string) => {
    // Default quality mapping for new system
    const defaultQualityMap: { [key: string]: VideoExportSettings['quality'] } = {
      'mp4': 'high',
      'gif': 'standard',
      'webm': 'high',
      'mov': 'high'
    };

    setExportSettings(prev => ({
      ...prev,
      format: format as VideoExportSettings['format'],
      quality: defaultQualityMap[format] || 'standard'
    }));
  }, []);

  // Update resolution when video changes
  useEffect(() => {
    if (hasVideo && project.video) {
      setExportSettings(prev => ({
        ...prev,
        resolution: {
          ...prev.resolution,
          width: project.video!.width,
          height: project.video!.height
        }
      }));
    }
  }, [hasVideo, project.video]);

  const totalDuration = project.segments.reduce((sum, segment) => 
    sum + (segment.endTime - segment.startTime), 0
  );

  const handleExport = async () => {
    setIsExporting(true);
    setExportProgress(0);

    try {
      // Real-time validation check
      if (!validation.canExport) {
        const errorMessages = validation.messages
          .filter(m => m.type === 'error')
          .map(m => m.message)
          .join('\n');

        showToast(`Configuración inválida: ${errorMessages}`, 'error');
        setIsExporting(false);
        return;
      }

      // Validation
      if (project.segments.length === 0) {
        showToast('No segments found. Please create segments first.', 'warning');
        setIsExporting(false);
        return;
      }

      if (!project.video?.uploadedInfo?.path) {
        showToast('Original video path not available. Please re-upload video.', 'warning');
        setIsExporting(false);
        return;
      }

      // Use new Master MP4 + Conversion system
      console.log('🎬 Starting export with Master MP4 + Conversion system:', {
        format: exportSettings.format,
        segments: project.segments,
        settings: exportSettings,
        totalDuration
      });

      // Map quality to 1-5 scale for new system
      const qualityMap: { [key: string]: number } = {
        'low': 1,
        'standard': 3,
        'high': 4,
        'maximum': 5
      };

      const qualityNumber = qualityMap[exportSettings.quality] || 3;

      // Use new unified export system
      const API_BASE_URL = window.location.hostname === 'localhost'
        ? 'http://localhost:3001'
        : window.location.origin;

      // For video editor, we'll use the unified export endpoint
      // This will generate a master MP4 from the video segment and convert to desired format
      const segment = project.segments[0]; // For now, export first segment

      const exportPayload = {
        videoPath: project.video.uploadedInfo.path,
        startTime: segment.startTime,
        endTime: segment.endTime,
        quality: qualityNumber,
        format: exportSettings.format,
        resolution: exportSettings.resolution,
        fps: exportSettings.fps
      };

      console.log('🚀 Calling unified export API:', exportPayload);

      const response = await fetch(`${API_BASE_URL}/api/unified-export/${exportSettings.format}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(exportPayload)
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Export failed');
      }

      const result = await response.json();

      if (result.success && result.exportUrl) {
        // Trigger download
        const link = document.createElement('a');
        link.href = `${API_BASE_URL}${result.exportUrl}`;
        link.download = result.exportId || `exported_video.${exportSettings.format}`;
        link.setAttribute('target', '_blank');
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();

        // Clean up
        setTimeout(() => {
          document.body.removeChild(link);
        }, 100);
        
        console.log('✅ Export completed and downloaded:', result.filename);
        showToast(`Export completed! Downloaded: ${result.filename}`, 'success');
        setExportProgress(100);
      } else {
        throw new Error(result.error || 'Export failed');
      }

      setIsExporting(false);

    } catch (error) {
      console.error('❌ Export failed:', error);
      showToast(`Export failed: ${error instanceof Error ? error.message : 'Unknown error'}`, 'error');
      setIsExporting(false);
      setExportProgress(0);
    }
  };

  // Empty state
  if (!hasVideo) {
    return <UnifiedEmptyState mode="video-editor" />;
  }

  // const currentStrategy = ExportStrategyFactory.getStrategy(exportSettings.format);
  // const estimatedSize = currentStrategy 
  //   ? currentStrategy.estimateFileSize(totalDuration, exportSettings)
  //   : '0.0';

  return (
    <div className="h-full flex flex-col gap-4">
      {/* Export Progress */}
      {isExporting && (
        <div className="p-3 bg-dark-900 rounded border border-dark-650">
          <div className="text-sm font-mono text-accent-green mb-2">
            Exporting... {exportProgress}%
          </div>
          <div className="w-full h-2 bg-dark-700 rounded-full overflow-hidden">
            <div 
              className="h-full bg-accent-green transition-all duration-300 ease-out"
              style={{ width: `${exportProgress}%` }}
            />
          </div>
        </div>
      )}

      {/* Simple Format & Quality Selection */}
      <div className="space-y-3">
        <UnifiedFormatSelector
          currentFormat={exportSettings.format as ExportFormat}
          onFormatChange={(format) => handleFormatChange(format)}
          mode="video-editor"
        />

        <UnifiedQualitySelector
          currentQuality={exportSettings.quality as QualityLevel}
          onQualityChange={(quality) => setExportSettings(prev => ({ ...prev, quality: quality as any }))}
          mode="video-editor"
        />

        <ExportResolutionSettings
          settings={exportSettings}
          onSettingsChange={setExportSettings}
          originalWidth={project.video?.width || 1920}
          originalHeight={project.video?.height || 1080}
        />

        <ExportGIFSettings
          settings={exportSettings}
          onSettingsChange={setExportSettings}
        />

        {/* Validation Messages */}
        {validation.messages.length > 0 && (
          <div className="mt-4">
            <ValidationMessages
              validation={validation}
              className="space-y-2"
              maxMessages={3}
            />
          </div>
        )}
      </div>

      {/* Export Button - Prominent */}
      {/* Validation Summary */}
      <ValidationSummary
        validation={validation}
        className="mb-3"
      />

      <button
        onClick={handleExport}
        disabled={isExporting || project.segments.length === 0 || !validation.canExport}
        className={`mt-auto w-full p-4 rounded-lg font-mono font-bold text-lg uppercase transition-all duration-200 ${
          isExporting || project.segments.length === 0 || !validation.canExport
            ? 'bg-dark-600 cursor-not-allowed opacity-60 text-dark-400'
            : 'bg-accent-green hover:bg-green-600 text-white shadow-lg'
        }`}
        title={!validation.canExport
          ? `Configuración inválida: ${validation.messages.filter(m => m.type === 'error').map(m => m.message).join(', ')}`
          : undefined}
      >
        {isExporting
          ? `⏳ Exporting ${exportSettings.format.toUpperCase()}...`
          : !validation.canExport
            ? `❌ Configuración Inválida`
            : `🚀 Export ${exportSettings.format.toUpperCase()}`}
      </button>
    </div>
  );
};
