{"name": "animagen-backend", "version": "1.0.0", "description": "Backend for AnimaGen - Animated GIF and Video Creator", "main": "dist/main.js", "scripts": {"start": "node dist/main.js", "dev": "ts-node-dev --respawn --transpile-only src/main.ts", "build": "tsc", "stable": "node minimal-server.js", "pm2": "pm2 start ecosystem-stable.config.js", "pm2:start": "./pm2-commands.sh start", "pm2:stop": "./pm2-commands.sh stop", "pm2:restart": "./pm2-commands.sh restart", "pm2:status": "./pm2-commands.sh status", "pm2:logs": "./pm2-commands.sh logs", "pm2:monitor": "./pm2-commands.sh monitor", "pm2:reset": "./pm2-commands.sh reset", "pm2:health": "./pm2-commands.sh health", "worker": "node worker.js", "worker:dev": "nodemon worker.js", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"bullmq": "^4.15.1", "cors": "^2.8.5", "dotenv": "^16.6.1", "express": "^4.19.2", "fluent-ffmpeg": "^2.1.2", "ioredis": "^5.4.1", "multer": "^1.4.5-lts.1", "sharp": "^0.34.3", "socket.io": "^4.8.1", "uuid": "^9.0.0"}, "devDependencies": {"@types/express": "^4.17.0", "@types/fluent-ffmpeg": "^2.1.20", "@types/jest": "^29.5.0", "@types/multer": "^1.4.0", "@types/node": "^20.0.0", "@types/uuid": "^9.0.0", "jest": "^30.0.3", "nodemon": "^3.1.0", "supertest": "^7.1.1", "ts-node": "^10.0.0", "ts-node-dev": "^2.0.0", "typescript": "^5.0.0"}, "keywords": ["gif", "video", "animation", "ffmpeg", "express"], "author": "AnimaGen Team", "license": "MIT"}