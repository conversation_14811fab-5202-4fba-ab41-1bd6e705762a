# Railway Environment Configuration
# This file documents the required environment variables for Railway deployment

# Server Configuration
NODE_ENV=production
PORT=3001

# Railway Redis Configuration (automatically provided)
# REDISHOST=your-railway-redis-host
# REDISPORT=your-railway-redis-port
# REDISUSER=your-railway-redis-user (optional)
# REDISPASSWORD=your-railway-redis-password

# Alternative: Redis URL (Railway also provides this)
# REDIS_URL=redis://user:password@host:port

# Directory Configuration
OUTPUT_DIR=./output
TEMP_DIR=./uploads

# FFmpeg Configuration (Railway-specific)
# Railway Hobby plan has 512MB RAM, so we need to limit resources
FFMPEG_THREADS=1
MAX_CONCURRENT_JOBS=1

# File Upload Limits (adjust for Railway limits)
MAX_FILE_SIZE=52428800  # 50MB (half of Railway's 100MB limit)
MAX_FILES=20

# CORS Configuration
CORS_ORIGINS=https://anima-production-3dad.up.railway.app,https://your-frontend-url.com

# Queue System (enable for Railway)
USE_QUEUE_SYSTEM=true
QUEUE_ENABLED_FORMATS=webm,mov,mp4,gif

# Memory Monitoring (recommended for Railway)
MEMORY_MONITORING=true
MEMORY_WARNING_THRESHOLD=400  # 400MB warning threshold

# Worker Configuration
WORKER_COUNT=1
WORKER_CONCURRENCY=1
QUEUE_CONCURRENCY=1
QUEUE_RATE_LIMIT_MAX=2
QUEUE_RATE_LIMIT_DURATION=60000