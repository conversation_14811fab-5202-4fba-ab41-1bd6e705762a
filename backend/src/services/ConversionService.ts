/**
 * AnimaGen Conversion Service
 * 
 * This service converts master MP4 files to different export formats (GIF, WebM, MOV).
 * It provides format-specific optimizations and quality settings.
 * 
 * Key Benefits:
 * - Fast conversions from master MP4 (vs generation from images)
 * - Format-specific optimizations for each output type
 * - Unified quality scale (1-5) across all formats
 * - Consistent results since all formats derive from same master
 */

import ffmpeg from 'fluent-ffmpeg';
import path from 'path';
import fs from 'fs';
import { 
  ExportFormat,
  QualityPreset 
} from '../types';
import { 
  createProgressCallback,
  emitExportProgress
} from '../utils';
import config from '../config';

export interface ConversionOptions {
  masterPath: string;
  outputFormat: ExportFormat;
  quality: number; // 1-5 scale (1=lowest, 5=highest)
  sessionId: string;
  customSettings?: {
    width?: number;
    height?: number;
    fps?: number;
    bitrate?: string;
  };
}

export interface ConversionResult {
  success: boolean;
  exportId: string;
  exportPath: string;
  exportUrl: string;
  fileSize: number;
  format: ExportFormat;
  quality: number;
  duration: number;
  message: string;
}

export class ConversionService {
  private static instance: ConversionService;
  
  public static getInstance(): ConversionService {
    if (!ConversionService.instance) {
      ConversionService.instance = new ConversionService();
    }
    return ConversionService.instance;
  }

  /**
   * Convert master MP4 to specified format
   */
  async convertMaster(options: ConversionOptions): Promise<ConversionResult> {
    const {
      masterPath,
      outputFormat,
      quality,
      sessionId,
      customSettings = {}
    } = options;

    console.log('🔄 Starting format conversion');
    console.log(`📊 Input: ${masterPath} → ${outputFormat} (quality: ${quality})`);

    // Validate master file exists
    if (!fs.existsSync(masterPath)) {
      throw new Error(`Master file not found: ${masterPath}`);
    }

    // Generate unique export ID
    const exportId = `export_${outputFormat}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const exportFilename = `${exportId}.${outputFormat.toLowerCase()}`;
    const exportPath = path.join(config.app.outputDir, exportFilename);
    const exportUrl = `/output/${exportFilename}`;

    console.log(`🎯 Export ID: ${exportId}`);
    console.log(`📁 Export path: ${exportPath}`);

    try {
      // Create progress callback
      const progressCallback = createProgressCallback(exportId, 'export');

      // Start progress tracking
      emitExportProgress(
        'export',
        'processing',
        0,
        `Converting master to ${outputFormat}`,
        { exportId, exportPath, format: outputFormat }
      );

      // Convert based on format
      await this.performConversion(
        masterPath,
        exportPath,
        outputFormat,
        quality,
        customSettings,
        progressCallback
      );

      // Verify output file
      if (!fs.existsSync(exportPath)) {
        throw new Error(`Export file not generated: ${exportPath}`);
      }

      const fileStats = fs.statSync(exportPath);
      const fileSize = fileStats.size;

      // Get duration (approximate from master)
      const duration = await this.getVideoDuration(masterPath);

      console.log(`✅ Conversion completed successfully`);
      console.log(`📊 File size: ${(fileSize / 1024 / 1024).toFixed(2)}MB`);

      // Complete progress tracking
      emitExportProgress(
        'export',
        'completed',
        100,
        `${outputFormat} export completed successfully`,
        { exportId, exportPath, fileSize, format: outputFormat }
      );

      return {
        success: true,
        exportId,
        exportPath,
        exportUrl,
        fileSize,
        format: outputFormat,
        quality,
        duration,
        message: `${outputFormat} export completed successfully`
      };

    } catch (error) {
      console.error('❌ Conversion failed:', error);
      
      // Clean up partial file
      if (fs.existsSync(exportPath)) {
        fs.unlinkSync(exportPath);
      }

      // Report error
      emitExportProgress(
        'export',
        'error',
        0,
        `Conversion failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        { exportId, error: error instanceof Error ? error.message : 'Unknown error', format: outputFormat }
      );

      throw error;
    }
  }

  /**
   * Perform the actual conversion using FFmpeg
   */
  private async performConversion(
    inputPath: string,
    outputPath: string,
    format: ExportFormat,
    quality: number,
    customSettings: any,
    progressCallback?: (progress: number) => void
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        console.log(`🎬 Starting FFmpeg conversion to ${format}`);
        
        const command = ffmpeg(inputPath);

        // Add progress tracking
        if (progressCallback) {
          command.on('progress', (progress) => {
            if (progress && progress.percent !== undefined) {
              progressCallback(Math.min(Math.round(progress.percent), 100));
            }
          });
        }

        // Configure output based on format
        switch (format) {
          case ExportFormat.GIF:
            this.configureGIFOutput(command, quality, customSettings);
            break;
          case ExportFormat.WEBM:
            this.configureWebMOutput(command, quality, customSettings);
            break;
          case ExportFormat.MOV:
            this.configureMOVOutput(command, quality, customSettings);
            break;
          case ExportFormat.MP4:
            this.configureMP4Output(command, quality, customSettings);
            break;
          default:
            throw new Error(`Unsupported format: ${format}`);
        }

        // Set output path
        command.output(outputPath);

        // Event handlers
        command.on('start', (commandLine) => {
          console.log(`🚀 FFmpeg conversion started: ${commandLine}`);
        });

        command.on('end', () => {
          console.log(`✅ FFmpeg conversion completed: ${outputPath}`);
          resolve();
        });

        command.on('error', (error) => {
          console.error(`❌ FFmpeg conversion error:`, error);
          reject(error);
        });

        // Start conversion
        command.run();

      } catch (error) {
        console.error('❌ Conversion setup error:', error);
        reject(error);
      }
    });
  }

  /**
   * Configure GIF output settings
   */
  private configureGIFOutput(command: any, quality: number, customSettings: any): void {
    const fps = customSettings.fps || this.getQualityFPS(quality, 'gif');
    const scale = this.getQualityScale(quality, 'gif');
    
    console.log(`🎛️ GIF settings: fps=${fps}, scale=${scale}`);
    
    command.outputOptions([
      '-vf', `fps=${fps},scale=${scale}:-1:flags=lanczos,split[s0][s1];[s0]palettegen=max_colors=256:reserve_transparent=0[p];[s1][p]paletteuse=dither=bayer:bayer_scale=3`,
      '-loop', '0'
    ]);
  }

  /**
   * Configure WebM output settings
   */
  private configureWebMOutput(command: any, quality: number, customSettings: any): void {
    const crf = this.getQualityCRF(quality, 'webm');
    const bitrate = customSettings.bitrate || this.getQualityBitrate(quality, 'webm');
    
    console.log(`🎛️ WebM settings: crf=${crf}, bitrate=${bitrate}`);
    
    command.outputOptions([
      '-c:v', 'libvpx-vp9',
      '-crf', crf.toString(),
      '-b:v', bitrate,
      '-maxrate', bitrate,
      '-bufsize', bitrate,
      '-cpu-used', '2',
      '-deadline', 'good',
      '-pix_fmt', 'yuv420p'
    ]);
  }

  /**
   * Configure MOV output settings
   */
  private configureMOVOutput(command: any, quality: number, customSettings: any): void {
    const crf = this.getQualityCRF(quality, 'mov');
    
    console.log(`🎛️ MOV settings: crf=${crf}`);
    
    command.outputOptions([
      '-c:v', 'libx264',
      '-preset', 'medium',
      '-crf', crf.toString(),
      '-pix_fmt', 'yuv420p',
      '-movflags', '+faststart'
    ]);
  }

  /**
   * Configure MP4 output settings
   */
  private configureMP4Output(command: any, quality: number, customSettings: any): void {
    const crf = this.getQualityCRF(quality, 'mp4');
    
    console.log(`🎛️ MP4 settings: crf=${crf}`);
    
    command.outputOptions([
      '-c:v', 'libx264',
      '-preset', 'medium',
      '-crf', crf.toString(),
      '-pix_fmt', 'yuv420p',
      '-movflags', '+faststart'
    ]);
  }

  /**
   * Get quality-based CRF value
   */
  private getQualityCRF(quality: number, format: string): number {
    const crfMap = {
      1: 35, // Lowest quality
      2: 28,
      3: 23, // Medium quality
      4: 18,
      5: 15  // Highest quality
    };
    return crfMap[quality as keyof typeof crfMap] || 23;
  }

  /**
   * Get quality-based bitrate
   */
  private getQualityBitrate(quality: number, format: string): string {
    const bitrateMap = {
      gif: { 1: '500k', 2: '1M', 3: '2M', 4: '3M', 5: '5M' },
      webm: { 1: '1M', 2: '2M', 3: '4M', 4: '6M', 5: '10M' },
      mp4: { 1: '1M', 2: '2M', 3: '4M', 4: '6M', 5: '10M' },
      mov: { 1: '2M', 2: '4M', 3: '6M', 4: '10M', 5: '15M' }
    };
    return bitrateMap[format]?.[quality] || '4M';
  }

  /**
   * Get quality-based FPS
   */
  private getQualityFPS(quality: number, format: string): number {
    if (format === 'gif') {
      const fpsMap = { 1: 10, 2: 15, 3: 20, 4: 24, 5: 30 };
      return fpsMap[quality] || 20;
    }
    return 30; // Default for video formats
  }

  /**
   * Get quality-based scale
   */
  private getQualityScale(quality: number, format: string): string {
    if (format === 'gif') {
      const scaleMap = { 1: '480', 2: '640', 3: '720', 4: '1080', 5: '1080' };
      return scaleMap[quality] || '720';
    }
    return '-1'; // Preserve original scale for video formats
  }

  /**
   * Get video duration in seconds
   */
  private async getVideoDuration(videoPath: string): Promise<number> {
    return new Promise((resolve) => {
      ffmpeg.ffprobe(videoPath, (err, metadata) => {
        if (err || !metadata?.format?.duration) {
          resolve(0);
        } else {
          resolve(metadata.format.duration);
        }
      });
    });
  }
}

export default ConversionService;
