/**
 * AnimaGen Master Generation Service
 * 
 * This service generates a single high-quality master MP4 file from images and transitions.
 * The master MP4 serves as both the preview and the source for all export format conversions.
 * 
 * Key Benefits:
 * - Single source of truth for preview and exports
 * - Guaranteed consistency between preview and final exports
 * - Simplified debugging and maintenance
 * - Better performance for multiple format exports
 */

import ffmpeg from 'fluent-ffmpeg';
import path from 'path';
import fs from 'fs';
import fs from 'fs';
import { 
  CompositionImage, 
  Transition, 
  QualityPreset 
} from '../types';
import { 
  calculatePreservedDimensions,
  buildUnifiedTransitionChain,
  calculateInputDurations,
  createProgressCallback,
  emitExportProgress
} from '../utils';
import config from '../config';

export interface MasterGenerationOptions {
  images: CompositionImage[];
  transitions?: Transition[];
  frameDurations?: number[];
  sessionId: string;
  quality?: QualityPreset;
  fps?: number;
  defaultFrameDuration?: number;
}

export interface MasterGenerationResult {
  success: boolean;
  masterId: string;
  masterPath: string;
  masterUrl: string;
  dimensions: {
    width: number;
    height: number;
    aspectRatio: number;
  };
  duration: number;
  fileSize: number;
  message: string;
}

export class MasterGenerationService {
  private static instance: MasterGenerationService;
  
  public static getInstance(): MasterGenerationService {
    if (!MasterGenerationService.instance) {
      MasterGenerationService.instance = new MasterGenerationService();
    }
    return MasterGenerationService.instance;
  }

  /**
   * Generate master MP4 from images and transitions
   */
  async generateMaster(options: MasterGenerationOptions): Promise<MasterGenerationResult> {
    const {
      images,
      transitions = [],
      frameDurations = [],
      sessionId,
      quality = QualityPreset.HIGH,
      fps = config.app.defaultFps,
      defaultFrameDuration = 3000
    } = options;

    console.log('🎬 Starting master MP4 generation');
    console.log(`📊 Input: ${images.length} images, ${transitions.length} transitions`);

    // Validate inputs
    if (!images || images.length === 0) {
      throw new Error('No images provided for master generation');
    }

    // Generate unique master ID
    const masterId = `master_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const masterFilename = `${masterId}.mp4`;
    const masterPath = path.join(config.app.outputDir, masterFilename);
    const masterUrl = `/output/${masterFilename}`;

    console.log(`🎯 Master ID: ${masterId}`);
    console.log(`📁 Master path: ${masterPath}`);

    try {
      // Calculate preserved dimensions
      console.log('📐 Calculating preserved dimensions...');
      const qualitySettings = config.getQualityPreset(quality);
      const dimensions = await calculatePreservedDimensions(
        images,
        qualitySettings.width,
        qualitySettings.height
      );

      console.log(`📐 Master dimensions: ${dimensions.width}x${dimensions.height} (ratio: ${dimensions.aspectRatio.toFixed(3)})`);

      // Calculate durations
      const durationInfo = calculateInputDurations(
        images,
        transitions,
        frameDurations,
        defaultFrameDuration
      );

      console.log(`⏱️ Total duration: ${durationInfo.totalDuration}s`);

      // Create progress callback
      const progressCallback = createProgressCallback(masterId, 'master');

      // Start progress tracking
      emitExportProgress(
        'master',
        'processing',
        0,
        `Generating master MP4 for ${images.length} images`,
        { masterId, masterPath }
      );

      // Generate master MP4
      await this.generateMasterMP4(
        images,
        transitions,
        frameDurations,
        masterPath,
        {
          width: dimensions.width,
          height: dimensions.height,
          fps,
          quality,
          defaultFrameDuration
        },
        progressCallback
      );

      // Verify output file
      if (!fs.existsSync(masterPath)) {
        throw new Error(`Master MP4 not generated: ${masterPath}`);
      }

      const fileStats = fs.statSync(masterPath);
      const fileSize = fileStats.size;

      console.log(`✅ Master MP4 generated successfully`);
      console.log(`📊 File size: ${(fileSize / 1024 / 1024).toFixed(2)}MB`);

      // Complete progress tracking
      emitExportProgress(
        'master',
        'completed',
        100,
        'Master MP4 generated successfully',
        { masterId, masterPath, fileSize }
      );

      return {
        success: true,
        masterId,
        masterPath,
        masterUrl,
        dimensions,
        duration: durationInfo.totalDuration,
        fileSize,
        message: 'Master MP4 generated successfully'
      };

    } catch (error) {
      console.error('❌ Master generation failed:', error);
      
      // Clean up partial file
      if (fs.existsSync(masterPath)) {
        fs.unlinkSync(masterPath);
      }

      // Report error
      emitExportProgress(
        'master',
        'error',
        0,
        `Master generation failed: ${error.message}`,
        { masterId, error: error.message }
      );

      throw error;
    }
  }

  /**
   * Generate master MP4 using FFmpeg
   */
  private async generateMasterMP4(
    images: CompositionImage[],
    transitions: Transition[],
    frameDurations: number[],
    outputPath: string,
    options: {
      width: number;
      height: number;
      fps: number;
      quality: QualityPreset;
      defaultFrameDuration: number;
    },
    progressCallback?: (progress: number) => void
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        console.log('🚀 Starting FFmpeg master generation');
        
        const command = ffmpeg();

        // RESTORED TRANSITION SYSTEM: Use the proper transition chain builder
        console.log(`🎭 Processing ${transitions.length} transitions for ${images.length} images`);

        const complexFilter: string[] = [];

        // Calculate optimal input durations
        const durationInfo = calculateInputDurations(
          images,
          transitions,
          frameDurations,
          options.defaultFrameDuration
        );

        console.log(`⏱️ Duration calculation:`, {
          totalDuration: durationInfo.totalDuration,
          maxTransitionDuration: durationInfo.maxTransitionDuration,
          inputDurations: durationInfo.inputDurations.map(d => `${d.toFixed(2)}s`)
        });

        // Add inputs with optimized durations
        images.forEach((image, index) => {
          console.log(`📥 Adding input #${index}: ${image.path}`);
          command.addInput(image.path);

          const inputDuration = durationInfo.inputDurations[index];
          console.log(`⏱️ Input ${index} duration: ${inputDuration.toFixed(2)}s`);

          // Create video input with proper duration and scaling
          const frameCount = Math.ceil(inputDuration * options.fps);
          complexFilter.push(
            `[${index}:v]scale=${options.width}:${options.height}:force_original_aspect_ratio=disable,setsar=1,fps=${options.fps},loop=loop=${frameCount-1}:size=1:start=0[v${index}]`
          );
        });

        // Build transition chain using the existing system
        const transitionChain = buildUnifiedTransitionChain(
          images,
          transitions,
          frameDurations,
          options.defaultFrameDuration,
          complexFilter
        );

        console.log(`🎭 Transition chain output: ${transitionChain}`);
        console.log(`🔧 Complex filter parts: ${complexFilter.length}`);

        // Get quality settings for master (high quality)
        const qualitySettings = config.getQualityPreset(options.quality);

        // Apply the filter and output options
        command
          .complexFilter(complexFilter)
          .outputOptions([
            '-map', transitionChain,
            '-c:v', 'libx264',
            '-preset', 'slow',        // High quality preset for master
            '-crf', '18',             // High quality CRF for master
            '-pix_fmt', 'yuv420p',
            '-r', options.fps.toString(),
            '-movflags', '+faststart'
          ])
          .output(outputPath);

        // Use the original command
        const finalCommand = command;

        // Add progress tracking
        if (progressCallback) {
          finalCommand.on('progress', (progress) => {
            if (progress && progress.percent !== undefined) {
              progressCallback(Math.min(Math.round(progress.percent), 100));
            }
          });
        }

        // Event handlers
        finalCommand.on('start', (commandLine) => {
          console.log('🎬 FFmpeg slideshow generation started:', commandLine);
        });

        finalCommand.on('end', () => {
          console.log('✅ Slideshow MP4 generation completed');
          resolve();
        });

        finalCommand.on('error', (error) => {
          console.error('❌ FFmpeg slideshow generation error:', error);
          reject(error);
        });

        // Start generation
        finalCommand.run();

      } catch (error) {
        console.error('❌ Master generation setup error:', error);
        reject(error);
      }
    });
  }
}

export default MasterGenerationService;
