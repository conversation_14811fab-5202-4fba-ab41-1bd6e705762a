/**
 * AnimaGen Unified Export Routes
 *
 * This module provides a unified export system that:
 * 1. Generates a master MP4 if it doesn't exist
 * 2. Converts the master to the requested format
 *
 * This ensures consistency and performance across all export formats.
 */

import express, { Request, Response } from 'express';
import path from 'path';
import fs from 'fs';
import {
  RequestWithSessionId,
  CompositionImage,
  Transition,
  QualityPreset,
  ExportFormat
} from '../types';
import {
  asyncHandler,
  createError,
  validateImages
} from '../utils';
import MasterGenerationService from '../services/MasterGenerationService';
import ConversionService from '../services/ConversionService';
import config from '../config';

// Create router
const router = express.Router();

/**
 * Unified export endpoint - generates master if needed, then converts to format
 * POST /unified-export/:format
 */
router.post('/:format', asyncHandler(async (req: RequestWithSessionId, res: Response) => {
  try {
    const { format } = req.params;
    const {
      sessionId,
      images,
      transitions = [],
      frameDurations = [],
      quality = 3, // 1-5 scale for export quality
      masterQuality = QualityPreset.HIGH, // Quality for master generation
      fps = 30,
      defaultFrameDuration = 3000,
      masterId, // Optional: use existing master
      customSettings = {}
    } = req.body;

    console.log('🚀 Unified export requested');
    console.log(`📝 Export request: ${format}, quality: ${quality}`);

    // Validate format
    const validFormats = Object.values(ExportFormat).map(f => f.toLowerCase());
    if (!validFormats.includes(format.toLowerCase())) {
      throw createError(`Invalid format: ${format}. Valid formats: ${validFormats.join(', ')}`, 400);
    }

    const outputFormat = format.toLowerCase() as ExportFormat;

    // Validate required fields for new master generation
    if (!masterId && (!sessionId || !images || !Array.isArray(images) || images.length === 0)) {
      throw createError('Either masterId or (sessionId + images) is required', 400);
    }

    let masterPath: string;
    let masterResult: any = null;

    // Step 1: Get or generate master MP4
    if (masterId) {
      // Use existing master
      masterPath = path.join(config.app.outputDir, `${masterId}.mp4`);

      if (!fs.existsSync(masterPath)) {
        throw createError(`Master file not found: ${masterId}`, 404);
      }

      console.log(`📁 Using existing master: ${masterPath}`);
    } else {
      // Generate new master
      console.log('🎬 Generating new master MP4...');

      // Validate images
      const validImages = await validateImages(images as CompositionImage[], sessionId);

      // Get master generation service
      const masterService = MasterGenerationService.getInstance();

      // Generate master MP4
      masterResult = await masterService.generateMaster({
        images: validImages,
        transitions: transitions as Transition[],
        frameDurations: frameDurations as number[],
        sessionId,
        quality: masterQuality as QualityPreset,
        fps,
        defaultFrameDuration
      });

      masterPath = masterResult.masterPath;
      console.log(`✅ Master generated: ${masterPath}`);
    }

    // Step 2: Convert master to requested format
    console.log(`🔄 Converting master to ${outputFormat}...`);

    const conversionService = ConversionService.getInstance();

    const conversionResult = await conversionService.convertMaster({
      masterPath,
      outputFormat,
      quality: parseInt(quality.toString()),
      sessionId: sessionId || 'unknown',
      customSettings
    });

    console.log('✅ Unified export completed successfully');
    console.log(`🔗 Export URL: ${conversionResult.exportUrl}`);

    // Return comprehensive response
    res.json({
      success: true,
      exportId: conversionResult.exportId,
      exportUrl: conversionResult.exportUrl,
      format: conversionResult.format,
      quality: conversionResult.quality,
      fileSize: conversionResult.fileSize,
      duration: conversionResult.duration,
      message: conversionResult.message,
      master: masterResult ? {
        masterId: masterResult.masterId,
        masterUrl: masterResult.masterUrl,
        dimensions: masterResult.dimensions
      } : {
        masterId: masterId,
        masterUrl: `/output/${masterId}.mp4`
      },
      settings: {
        format: conversionResult.format,
        quality: conversionResult.quality,
        fileSize: `${(conversionResult.fileSize / 1024 / 1024).toFixed(2)}MB`,
        duration: `${conversionResult.duration.toFixed(1)}s`,
        masterGenerated: !masterId
      }
    });

  } catch (error) {
    console.error('❌ Unified export failed:', error);

    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    const statusCode = (error as any).statusCode || 500;

    res.status(statusCode).json({
      success: false,
      error: errorMessage,
      message: 'Unified export failed'
    });
  }
}));

export default router;
