/**
 * AnimaGen Backend - Master Generation Routes Module
 * 
 * This module provides endpoints for generating high-quality master videos
 * that can be used for further processing and format conversions.
 */

import express, { Request, Response } from 'express';
import path from 'path';
import fs from 'fs';
import { 
  RequestWithSessionId,
  CompositionImage,
  Transition,
  QualityPreset,
  ExportFormat
} from '../types';
import config from '../config';
import {
  asyncHandler,
  createError,
  validateImages
} from '../utils';
import MasterGenerationService from '../services/MasterGenerationService';

// Create router
const router = express.Router();

/**
 * Generate a high-quality master video from images
 * POST /generate-master
 */
router.post('/', asyncHandler(async (req: RequestWithSessionId, res: Response) => {
  try {
    console.log('🎬 Master MP4 generation requested');
    console.log('📝 Master request payload:', JSON.stringify(req.body, null, 2));

    const {
      sessionId,
      images,
      transitions = [],
      frameDurations = [],
      quality = QualityPreset.HIGH,
      fps = 30,
      defaultFrameDuration = 3000
    } = req.body;
    
    // Validate required fields
    if (!sessionId) {
      throw createError('Session ID is required', 400);
    }

    if (!images || !Array.isArray(images) || images.length === 0) {
      throw createError('Images array is required and must not be empty', 400);
    }

    console.log(`📊 Processing ${images.length} images for master generation`);
    // Validate images exist (pass sessionId to construct paths)
    const validImages = await validateImages(images as CompositionImage[], sessionId);
    console.log(`✅ All ${validImages.length} images validated`);

    // Get master generation service
    const masterService = MasterGenerationService.getInstance();

    // Generate master MP4
    console.log('🚀 Starting master MP4 generation');
    const result = await masterService.generateMaster({
      images: validImages,
      transitions: transitions as Transition[],
      frameDurations: frameDurations as number[],
      sessionId,
      quality: quality as QualityPreset,
      fps,
      defaultFrameDuration
    });

    console.log('✅ Master generation completed successfully');
    console.log(`🔗 Master URL: ${result.masterUrl}`);
    // Return success response
    res.json({
      success: true,
      masterId: result.masterId,
      masterUrl: result.masterUrl,
      previewUrl: result.masterUrl, // Master MP4 IS the preview
      dimensions: result.dimensions,
      duration: result.duration,
      fileSize: result.fileSize,
      message: result.message,
      settings: {
        width: result.dimensions.width,
        height: result.dimensions.height,
        aspectRatio: result.dimensions.aspectRatio,
        fps,
        quality,
        format: 'mp4'
      }
    });

  } catch (error) {
    console.error('❌ Master generation failed:', error);

    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    const statusCode = (error as any).statusCode || 500;

    res.status(statusCode).json({
      success: false,
      error: errorMessage,
      message: 'Master generation failed'
    });
  }
}));

export default router;
