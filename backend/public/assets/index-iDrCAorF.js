var Op=Object.defineProperty;var Ap=(o,l,i)=>l in o?Op(o,l,{enumerable:!0,configurable:!0,writable:!0,value:i}):o[l]=i;var gt=(o,l,i)=>Ap(o,typeof l!="symbol"?l+"":l,i);(function(){const l=document.createElement("link").relList;if(l&&l.supports&&l.supports("modulepreload"))return;for(const d of document.querySelectorAll('link[rel="modulepreload"]'))c(d);new MutationObserver(d=>{for(const f of d)if(f.type==="childList")for(const p of f.addedNodes)p.tagName==="LINK"&&p.rel==="modulepreload"&&c(p)}).observe(document,{childList:!0,subtree:!0});function i(d){const f={};return d.integrity&&(f.integrity=d.integrity),d.referrerPolicy&&(f.referrerPolicy=d.referrerPolicy),d.crossOrigin==="use-credentials"?f.credentials="include":d.crossOrigin==="anonymous"?f.credentials="omit":f.credentials="same-origin",f}function c(d){if(d.ep)return;d.ep=!0;const f=i(d);fetch(d.href,f)}})();function $p(o){return o&&o.__esModule&&Object.prototype.hasOwnProperty.call(o,"default")?o.default:o}var ua={exports:{}},$r={},ca={exports:{}},me={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ic;function Up(){if(Ic)return me;Ic=1;var o=Symbol.for("react.element"),l=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),c=Symbol.for("react.strict_mode"),d=Symbol.for("react.profiler"),f=Symbol.for("react.provider"),p=Symbol.for("react.context"),g=Symbol.for("react.forward_ref"),x=Symbol.for("react.suspense"),y=Symbol.for("react.memo"),C=Symbol.for("react.lazy"),v=Symbol.iterator;function w(E){return E===null||typeof E!="object"?null:(E=v&&E[v]||E["@@iterator"],typeof E=="function"?E:null)}var D={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},T=Object.assign,j={};function R(E,U,re){this.props=E,this.context=U,this.refs=j,this.updater=re||D}R.prototype.isReactComponent={},R.prototype.setState=function(E,U){if(typeof E!="object"&&typeof E!="function"&&E!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,E,U,"setState")},R.prototype.forceUpdate=function(E){this.updater.enqueueForceUpdate(this,E,"forceUpdate")};function b(){}b.prototype=R.prototype;function _(E,U,re){this.props=E,this.context=U,this.refs=j,this.updater=re||D}var W=_.prototype=new b;W.constructor=_,T(W,R.prototype),W.isPureReactComponent=!0;var V=Array.isArray,P=Object.prototype.hasOwnProperty,S={current:null},M={key:!0,ref:!0,__self:!0,__source:!0};function F(E,U,re){var pe,he={},ge=null,ke=null;if(U!=null)for(pe in U.ref!==void 0&&(ke=U.ref),U.key!==void 0&&(ge=""+U.key),U)P.call(U,pe)&&!M.hasOwnProperty(pe)&&(he[pe]=U[pe]);var we=arguments.length-2;if(we===1)he.children=re;else if(1<we){for(var Ee=Array(we),lt=0;lt<we;lt++)Ee[lt]=arguments[lt+2];he.children=Ee}if(E&&E.defaultProps)for(pe in we=E.defaultProps,we)he[pe]===void 0&&(he[pe]=we[pe]);return{$$typeof:o,type:E,key:ge,ref:ke,props:he,_owner:S.current}}function H(E,U){return{$$typeof:o,type:E.type,key:U,ref:E.ref,props:E.props,_owner:E._owner}}function J(E){return typeof E=="object"&&E!==null&&E.$$typeof===o}function te(E){var U={"=":"=0",":":"=2"};return"$"+E.replace(/[=:]/g,function(re){return U[re]})}var X=/\/+/g;function ae(E,U){return typeof E=="object"&&E!==null&&E.key!=null?te(""+E.key):U.toString(36)}function Z(E,U,re,pe,he){var ge=typeof E;(ge==="undefined"||ge==="boolean")&&(E=null);var ke=!1;if(E===null)ke=!0;else switch(ge){case"string":case"number":ke=!0;break;case"object":switch(E.$$typeof){case o:case l:ke=!0}}if(ke)return ke=E,he=he(ke),E=pe===""?"."+ae(ke,0):pe,V(he)?(re="",E!=null&&(re=E.replace(X,"$&/")+"/"),Z(he,U,re,"",function(lt){return lt})):he!=null&&(J(he)&&(he=H(he,re+(!he.key||ke&&ke.key===he.key?"":(""+he.key).replace(X,"$&/")+"/")+E)),U.push(he)),1;if(ke=0,pe=pe===""?".":pe+":",V(E))for(var we=0;we<E.length;we++){ge=E[we];var Ee=pe+ae(ge,we);ke+=Z(ge,U,re,Ee,he)}else if(Ee=w(E),typeof Ee=="function")for(E=Ee.call(E),we=0;!(ge=E.next()).done;)ge=ge.value,Ee=pe+ae(ge,we++),ke+=Z(ge,U,re,Ee,he);else if(ge==="object")throw U=String(E),Error("Objects are not valid as a React child (found: "+(U==="[object Object]"?"object with keys {"+Object.keys(E).join(", ")+"}":U)+"). If you meant to render a collection of children, use an array instead.");return ke}function K(E,U,re){if(E==null)return E;var pe=[],he=0;return Z(E,pe,"","",function(ge){return U.call(re,ge,he++)}),pe}function fe(E){if(E._status===-1){var U=E._result;U=U(),U.then(function(re){(E._status===0||E._status===-1)&&(E._status=1,E._result=re)},function(re){(E._status===0||E._status===-1)&&(E._status=2,E._result=re)}),E._status===-1&&(E._status=0,E._result=U)}if(E._status===1)return E._result.default;throw E._result}var se={current:null},A={transition:null},O={ReactCurrentDispatcher:se,ReactCurrentBatchConfig:A,ReactCurrentOwner:S};function B(){throw Error("act(...) is not supported in production builds of React.")}return me.Children={map:K,forEach:function(E,U,re){K(E,function(){U.apply(this,arguments)},re)},count:function(E){var U=0;return K(E,function(){U++}),U},toArray:function(E){return K(E,function(U){return U})||[]},only:function(E){if(!J(E))throw Error("React.Children.only expected to receive a single React element child.");return E}},me.Component=R,me.Fragment=i,me.Profiler=d,me.PureComponent=_,me.StrictMode=c,me.Suspense=x,me.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=O,me.act=B,me.cloneElement=function(E,U,re){if(E==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+E+".");var pe=T({},E.props),he=E.key,ge=E.ref,ke=E._owner;if(U!=null){if(U.ref!==void 0&&(ge=U.ref,ke=S.current),U.key!==void 0&&(he=""+U.key),E.type&&E.type.defaultProps)var we=E.type.defaultProps;for(Ee in U)P.call(U,Ee)&&!M.hasOwnProperty(Ee)&&(pe[Ee]=U[Ee]===void 0&&we!==void 0?we[Ee]:U[Ee])}var Ee=arguments.length-2;if(Ee===1)pe.children=re;else if(1<Ee){we=Array(Ee);for(var lt=0;lt<Ee;lt++)we[lt]=arguments[lt+2];pe.children=we}return{$$typeof:o,type:E.type,key:he,ref:ge,props:pe,_owner:ke}},me.createContext=function(E){return E={$$typeof:p,_currentValue:E,_currentValue2:E,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},E.Provider={$$typeof:f,_context:E},E.Consumer=E},me.createElement=F,me.createFactory=function(E){var U=F.bind(null,E);return U.type=E,U},me.createRef=function(){return{current:null}},me.forwardRef=function(E){return{$$typeof:g,render:E}},me.isValidElement=J,me.lazy=function(E){return{$$typeof:C,_payload:{_status:-1,_result:E},_init:fe}},me.memo=function(E,U){return{$$typeof:y,type:E,compare:U===void 0?null:U}},me.startTransition=function(E){var U=A.transition;A.transition={};try{E()}finally{A.transition=U}},me.unstable_act=B,me.useCallback=function(E,U){return se.current.useCallback(E,U)},me.useContext=function(E){return se.current.useContext(E)},me.useDebugValue=function(){},me.useDeferredValue=function(E){return se.current.useDeferredValue(E)},me.useEffect=function(E,U){return se.current.useEffect(E,U)},me.useId=function(){return se.current.useId()},me.useImperativeHandle=function(E,U,re){return se.current.useImperativeHandle(E,U,re)},me.useInsertionEffect=function(E,U){return se.current.useInsertionEffect(E,U)},me.useLayoutEffect=function(E,U){return se.current.useLayoutEffect(E,U)},me.useMemo=function(E,U){return se.current.useMemo(E,U)},me.useReducer=function(E,U,re){return se.current.useReducer(E,U,re)},me.useRef=function(E){return se.current.useRef(E)},me.useState=function(E){return se.current.useState(E)},me.useSyncExternalStore=function(E,U,re){return se.current.useSyncExternalStore(E,U,re)},me.useTransition=function(){return se.current.useTransition()},me.version="18.3.1",me}var Mc;function ja(){return Mc||(Mc=1,ca.exports=Up()),ca.exports}/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Pc;function Bp(){if(Pc)return $r;Pc=1;var o=ja(),l=Symbol.for("react.element"),i=Symbol.for("react.fragment"),c=Object.prototype.hasOwnProperty,d=o.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,f={key:!0,ref:!0,__self:!0,__source:!0};function p(g,x,y){var C,v={},w=null,D=null;y!==void 0&&(w=""+y),x.key!==void 0&&(w=""+x.key),x.ref!==void 0&&(D=x.ref);for(C in x)c.call(x,C)&&!f.hasOwnProperty(C)&&(v[C]=x[C]);if(g&&g.defaultProps)for(C in x=g.defaultProps,x)v[C]===void 0&&(v[C]=x[C]);return{$$typeof:l,type:g,key:w,ref:D,props:v,_owner:d.current}}return $r.Fragment=i,$r.jsx=p,$r.jsxs=p,$r}var Dc;function Vp(){return Dc||(Dc=1,ua.exports=Bp()),ua.exports}var s=Vp(),h=ja();const We=$p(h);var al={},da={exports:{}},ot={},fa={exports:{}},pa={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var _c;function Wp(){return _c||(_c=1,function(o){function l(A,O){var B=A.length;A.push(O);e:for(;0<B;){var E=B-1>>>1,U=A[E];if(0<d(U,O))A[E]=O,A[B]=U,B=E;else break e}}function i(A){return A.length===0?null:A[0]}function c(A){if(A.length===0)return null;var O=A[0],B=A.pop();if(B!==O){A[0]=B;e:for(var E=0,U=A.length,re=U>>>1;E<re;){var pe=2*(E+1)-1,he=A[pe],ge=pe+1,ke=A[ge];if(0>d(he,B))ge<U&&0>d(ke,he)?(A[E]=ke,A[ge]=B,E=ge):(A[E]=he,A[pe]=B,E=pe);else if(ge<U&&0>d(ke,B))A[E]=ke,A[ge]=B,E=ge;else break e}}return O}function d(A,O){var B=A.sortIndex-O.sortIndex;return B!==0?B:A.id-O.id}if(typeof performance=="object"&&typeof performance.now=="function"){var f=performance;o.unstable_now=function(){return f.now()}}else{var p=Date,g=p.now();o.unstable_now=function(){return p.now()-g}}var x=[],y=[],C=1,v=null,w=3,D=!1,T=!1,j=!1,R=typeof setTimeout=="function"?setTimeout:null,b=typeof clearTimeout=="function"?clearTimeout:null,_=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function W(A){for(var O=i(y);O!==null;){if(O.callback===null)c(y);else if(O.startTime<=A)c(y),O.sortIndex=O.expirationTime,l(x,O);else break;O=i(y)}}function V(A){if(j=!1,W(A),!T)if(i(x)!==null)T=!0,fe(P);else{var O=i(y);O!==null&&se(V,O.startTime-A)}}function P(A,O){T=!1,j&&(j=!1,b(F),F=-1),D=!0;var B=w;try{for(W(O),v=i(x);v!==null&&(!(v.expirationTime>O)||A&&!te());){var E=v.callback;if(typeof E=="function"){v.callback=null,w=v.priorityLevel;var U=E(v.expirationTime<=O);O=o.unstable_now(),typeof U=="function"?v.callback=U:v===i(x)&&c(x),W(O)}else c(x);v=i(x)}if(v!==null)var re=!0;else{var pe=i(y);pe!==null&&se(V,pe.startTime-O),re=!1}return re}finally{v=null,w=B,D=!1}}var S=!1,M=null,F=-1,H=5,J=-1;function te(){return!(o.unstable_now()-J<H)}function X(){if(M!==null){var A=o.unstable_now();J=A;var O=!0;try{O=M(!0,A)}finally{O?ae():(S=!1,M=null)}}else S=!1}var ae;if(typeof _=="function")ae=function(){_(X)};else if(typeof MessageChannel<"u"){var Z=new MessageChannel,K=Z.port2;Z.port1.onmessage=X,ae=function(){K.postMessage(null)}}else ae=function(){R(X,0)};function fe(A){M=A,S||(S=!0,ae())}function se(A,O){F=R(function(){A(o.unstable_now())},O)}o.unstable_IdlePriority=5,o.unstable_ImmediatePriority=1,o.unstable_LowPriority=4,o.unstable_NormalPriority=3,o.unstable_Profiling=null,o.unstable_UserBlockingPriority=2,o.unstable_cancelCallback=function(A){A.callback=null},o.unstable_continueExecution=function(){T||D||(T=!0,fe(P))},o.unstable_forceFrameRate=function(A){0>A||125<A?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):H=0<A?Math.floor(1e3/A):5},o.unstable_getCurrentPriorityLevel=function(){return w},o.unstable_getFirstCallbackNode=function(){return i(x)},o.unstable_next=function(A){switch(w){case 1:case 2:case 3:var O=3;break;default:O=w}var B=w;w=O;try{return A()}finally{w=B}},o.unstable_pauseExecution=function(){},o.unstable_requestPaint=function(){},o.unstable_runWithPriority=function(A,O){switch(A){case 1:case 2:case 3:case 4:case 5:break;default:A=3}var B=w;w=A;try{return O()}finally{w=B}},o.unstable_scheduleCallback=function(A,O,B){var E=o.unstable_now();switch(typeof B=="object"&&B!==null?(B=B.delay,B=typeof B=="number"&&0<B?E+B:E):B=E,A){case 1:var U=-1;break;case 2:U=250;break;case 5:U=**********;break;case 4:U=1e4;break;default:U=5e3}return U=B+U,A={id:C++,callback:O,priorityLevel:A,startTime:B,expirationTime:U,sortIndex:-1},B>E?(A.sortIndex=B,l(y,A),i(x)===null&&A===i(y)&&(j?(b(F),F=-1):j=!0,se(V,B-E))):(A.sortIndex=U,l(x,A),T||D||(T=!0,fe(P))),A},o.unstable_shouldYield=te,o.unstable_wrapCallback=function(A){var O=w;return function(){var B=w;w=O;try{return A.apply(this,arguments)}finally{w=B}}}}(pa)),pa}var Lc;function Hp(){return Lc||(Lc=1,fa.exports=Wp()),fa.exports}/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var zc;function Qp(){if(zc)return ot;zc=1;var o=ja(),l=Hp();function i(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var c=new Set,d={};function f(e,t){p(e,t),p(e+"Capture",t)}function p(e,t){for(d[e]=t,e=0;e<t.length;e++)c.add(t[e])}var g=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),x=Object.prototype.hasOwnProperty,y=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,C={},v={};function w(e){return x.call(v,e)?!0:x.call(C,e)?!1:y.test(e)?v[e]=!0:(C[e]=!0,!1)}function D(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function T(e,t,n,r){if(t===null||typeof t>"u"||D(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function j(e,t,n,r,a,u,m){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=a,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=u,this.removeEmptyString=m}var R={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){R[e]=new j(e,0,!1,e,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];R[t]=new j(t,1,!1,e[1],null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(e){R[e]=new j(e,2,!1,e.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){R[e]=new j(e,2,!1,e,null,!1,!1)}),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){R[e]=new j(e,3,!1,e.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(e){R[e]=new j(e,3,!0,e,null,!1,!1)}),["capture","download"].forEach(function(e){R[e]=new j(e,4,!1,e,null,!1,!1)}),["cols","rows","size","span"].forEach(function(e){R[e]=new j(e,6,!1,e,null,!1,!1)}),["rowSpan","start"].forEach(function(e){R[e]=new j(e,5,!1,e.toLowerCase(),null,!1,!1)});var b=/[\-:]([a-z])/g;function _(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(b,_);R[t]=new j(t,1,!1,e,null,!1,!1)}),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(b,_);R[t]=new j(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(b,_);R[t]=new j(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(e){R[e]=new j(e,1,!1,e.toLowerCase(),null,!1,!1)}),R.xlinkHref=new j("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(e){R[e]=new j(e,1,!1,e.toLowerCase(),null,!0,!0)});function W(e,t,n,r){var a=R.hasOwnProperty(t)?R[t]:null;(a!==null?a.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(T(t,n,a,r)&&(n=null),r||a===null?w(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):a.mustUseProperty?e[a.propertyName]=n===null?a.type===3?!1:"":n:(t=a.attributeName,r=a.attributeNamespace,n===null?e.removeAttribute(t):(a=a.type,n=a===3||a===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var V=o.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,P=Symbol.for("react.element"),S=Symbol.for("react.portal"),M=Symbol.for("react.fragment"),F=Symbol.for("react.strict_mode"),H=Symbol.for("react.profiler"),J=Symbol.for("react.provider"),te=Symbol.for("react.context"),X=Symbol.for("react.forward_ref"),ae=Symbol.for("react.suspense"),Z=Symbol.for("react.suspense_list"),K=Symbol.for("react.memo"),fe=Symbol.for("react.lazy"),se=Symbol.for("react.offscreen"),A=Symbol.iterator;function O(e){return e===null||typeof e!="object"?null:(e=A&&e[A]||e["@@iterator"],typeof e=="function"?e:null)}var B=Object.assign,E;function U(e){if(E===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);E=t&&t[1]||""}return`
`+E+e}var re=!1;function pe(e,t){if(!e||re)return"";re=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch($){var r=$}Reflect.construct(e,[],t)}else{try{t.call()}catch($){r=$}e.call(t.prototype)}else{try{throw Error()}catch($){r=$}e()}}catch($){if($&&r&&typeof $.stack=="string"){for(var a=$.stack.split(`
`),u=r.stack.split(`
`),m=a.length-1,k=u.length-1;1<=m&&0<=k&&a[m]!==u[k];)k--;for(;1<=m&&0<=k;m--,k--)if(a[m]!==u[k]){if(m!==1||k!==1)do if(m--,k--,0>k||a[m]!==u[k]){var N=`
`+a[m].replace(" at new "," at ");return e.displayName&&N.includes("<anonymous>")&&(N=N.replace("<anonymous>",e.displayName)),N}while(1<=m&&0<=k);break}}}finally{re=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?U(e):""}function he(e){switch(e.tag){case 5:return U(e.type);case 16:return U("Lazy");case 13:return U("Suspense");case 19:return U("SuspenseList");case 0:case 2:case 15:return e=pe(e.type,!1),e;case 11:return e=pe(e.type.render,!1),e;case 1:return e=pe(e.type,!0),e;default:return""}}function ge(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case M:return"Fragment";case S:return"Portal";case H:return"Profiler";case F:return"StrictMode";case ae:return"Suspense";case Z:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case te:return(e.displayName||"Context")+".Consumer";case J:return(e._context.displayName||"Context")+".Provider";case X:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case K:return t=e.displayName||null,t!==null?t:ge(e.type)||"Memo";case fe:t=e._payload,e=e._init;try{return ge(e(t))}catch{}}return null}function ke(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return ge(t);case 8:return t===F?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function we(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Ee(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function lt(e){var t=Ee(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var a=n.get,u=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return a.call(this)},set:function(m){r=""+m,u.call(this,m)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(m){r=""+m},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Yr(e){e._valueTracker||(e._valueTracker=lt(e))}function za(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Ee(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Xr(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function gl(e,t){var n=t.checked;return B({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Fa(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=we(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Oa(e,t){t=t.checked,t!=null&&W(e,"checked",t,!1)}function xl(e,t){Oa(e,t);var n=we(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?vl(e,t.type,n):t.hasOwnProperty("defaultValue")&&vl(e,t.type,we(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Aa(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function vl(e,t,n){(t!=="number"||Xr(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var tr=Array.isArray;function Nn(e,t,n,r){if(e=e.options,t){t={};for(var a=0;a<n.length;a++)t["$"+n[a]]=!0;for(n=0;n<e.length;n++)a=t.hasOwnProperty("$"+e[n].value),e[n].selected!==a&&(e[n].selected=a),a&&r&&(e[n].defaultSelected=!0)}else{for(n=""+we(n),t=null,a=0;a<e.length;a++){if(e[a].value===n){e[a].selected=!0,r&&(e[a].defaultSelected=!0);return}t!==null||e[a].disabled||(t=e[a])}t!==null&&(t.selected=!0)}}function yl(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(i(91));return B({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function $a(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(i(92));if(tr(n)){if(1<n.length)throw Error(i(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:we(n)}}function Ua(e,t){var n=we(t.value),r=we(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Ba(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Va(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function wl(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Va(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Jr,Wa=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,a){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,a)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Jr=Jr||document.createElement("div"),Jr.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Jr.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function nr(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var rr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Ud=["Webkit","ms","Moz","O"];Object.keys(rr).forEach(function(e){Ud.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),rr[t]=rr[e]})});function Ha(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||rr.hasOwnProperty(e)&&rr[e]?(""+t).trim():t+"px"}function Qa(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,a=Ha(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,a):e[n]=a}}var Bd=B({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Sl(e,t){if(t){if(Bd[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(i(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(i(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(i(61))}if(t.style!=null&&typeof t.style!="object")throw Error(i(62))}}function kl(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Cl=null;function bl(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var jl=null,Tn=null,Rn=null;function Ga(e){if(e=jr(e)){if(typeof jl!="function")throw Error(i(280));var t=e.stateNode;t&&(t=ko(t),jl(e.stateNode,e.type,t))}}function Ka(e){Tn?Rn?Rn.push(e):Rn=[e]:Tn=e}function qa(){if(Tn){var e=Tn,t=Rn;if(Rn=Tn=null,Ga(e),t)for(e=0;e<t.length;e++)Ga(t[e])}}function Ya(e,t){return e(t)}function Xa(){}var El=!1;function Ja(e,t,n){if(El)return e(t,n);El=!0;try{return Ya(e,t,n)}finally{El=!1,(Tn!==null||Rn!==null)&&(Xa(),qa())}}function or(e,t){var n=e.stateNode;if(n===null)return null;var r=ko(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(i(231,t,typeof n));return n}var Nl=!1;if(g)try{var lr={};Object.defineProperty(lr,"passive",{get:function(){Nl=!0}}),window.addEventListener("test",lr,lr),window.removeEventListener("test",lr,lr)}catch{Nl=!1}function Vd(e,t,n,r,a,u,m,k,N){var $=Array.prototype.slice.call(arguments,3);try{t.apply(n,$)}catch(G){this.onError(G)}}var ir=!1,Zr=null,eo=!1,Tl=null,Wd={onError:function(e){ir=!0,Zr=e}};function Hd(e,t,n,r,a,u,m,k,N){ir=!1,Zr=null,Vd.apply(Wd,arguments)}function Qd(e,t,n,r,a,u,m,k,N){if(Hd.apply(this,arguments),ir){if(ir){var $=Zr;ir=!1,Zr=null}else throw Error(i(198));eo||(eo=!0,Tl=$)}}function mn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Za(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function es(e){if(mn(e)!==e)throw Error(i(188))}function Gd(e){var t=e.alternate;if(!t){if(t=mn(e),t===null)throw Error(i(188));return t!==e?null:e}for(var n=e,r=t;;){var a=n.return;if(a===null)break;var u=a.alternate;if(u===null){if(r=a.return,r!==null){n=r;continue}break}if(a.child===u.child){for(u=a.child;u;){if(u===n)return es(a),e;if(u===r)return es(a),t;u=u.sibling}throw Error(i(188))}if(n.return!==r.return)n=a,r=u;else{for(var m=!1,k=a.child;k;){if(k===n){m=!0,n=a,r=u;break}if(k===r){m=!0,r=a,n=u;break}k=k.sibling}if(!m){for(k=u.child;k;){if(k===n){m=!0,n=u,r=a;break}if(k===r){m=!0,r=u,n=a;break}k=k.sibling}if(!m)throw Error(i(189))}}if(n.alternate!==r)throw Error(i(190))}if(n.tag!==3)throw Error(i(188));return n.stateNode.current===n?e:t}function ts(e){return e=Gd(e),e!==null?ns(e):null}function ns(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=ns(e);if(t!==null)return t;e=e.sibling}return null}var rs=l.unstable_scheduleCallback,os=l.unstable_cancelCallback,Kd=l.unstable_shouldYield,qd=l.unstable_requestPaint,_e=l.unstable_now,Yd=l.unstable_getCurrentPriorityLevel,Rl=l.unstable_ImmediatePriority,ls=l.unstable_UserBlockingPriority,to=l.unstable_NormalPriority,Xd=l.unstable_LowPriority,is=l.unstable_IdlePriority,no=null,jt=null;function Jd(e){if(jt&&typeof jt.onCommitFiberRoot=="function")try{jt.onCommitFiberRoot(no,e,void 0,(e.current.flags&128)===128)}catch{}}var vt=Math.clz32?Math.clz32:tf,Zd=Math.log,ef=Math.LN2;function tf(e){return e>>>=0,e===0?32:31-(Zd(e)/ef|0)|0}var ro=64,oo=4194304;function ar(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function lo(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,a=e.suspendedLanes,u=e.pingedLanes,m=n&268435455;if(m!==0){var k=m&~a;k!==0?r=ar(k):(u&=m,u!==0&&(r=ar(u)))}else m=n&~a,m!==0?r=ar(m):u!==0&&(r=ar(u));if(r===0)return 0;if(t!==0&&t!==r&&(t&a)===0&&(a=r&-r,u=t&-t,a>=u||a===16&&(u&4194240)!==0))return t;if((r&4)!==0&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-vt(t),a=1<<n,r|=e[n],t&=~a;return r}function nf(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function rf(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,a=e.expirationTimes,u=e.pendingLanes;0<u;){var m=31-vt(u),k=1<<m,N=a[m];N===-1?((k&n)===0||(k&r)!==0)&&(a[m]=nf(k,t)):N<=t&&(e.expiredLanes|=k),u&=~k}}function Il(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function as(){var e=ro;return ro<<=1,(ro&4194240)===0&&(ro=64),e}function Ml(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function sr(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-vt(t),e[t]=n}function of(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var a=31-vt(n),u=1<<a;t[a]=0,r[a]=-1,e[a]=-1,n&=~u}}function Pl(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-vt(n),a=1<<r;a&t|e[r]&t&&(e[r]|=t),n&=~a}}var Se=0;function ss(e){return e&=-e,1<e?4<e?(e&268435455)!==0?16:536870912:4:1}var us,Dl,cs,ds,fs,_l=!1,io=[],Qt=null,Gt=null,Kt=null,ur=new Map,cr=new Map,qt=[],lf="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function ps(e,t){switch(e){case"focusin":case"focusout":Qt=null;break;case"dragenter":case"dragleave":Gt=null;break;case"mouseover":case"mouseout":Kt=null;break;case"pointerover":case"pointerout":ur.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":cr.delete(t.pointerId)}}function dr(e,t,n,r,a,u){return e===null||e.nativeEvent!==u?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:u,targetContainers:[a]},t!==null&&(t=jr(t),t!==null&&Dl(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,a!==null&&t.indexOf(a)===-1&&t.push(a),e)}function af(e,t,n,r,a){switch(t){case"focusin":return Qt=dr(Qt,e,t,n,r,a),!0;case"dragenter":return Gt=dr(Gt,e,t,n,r,a),!0;case"mouseover":return Kt=dr(Kt,e,t,n,r,a),!0;case"pointerover":var u=a.pointerId;return ur.set(u,dr(ur.get(u)||null,e,t,n,r,a)),!0;case"gotpointercapture":return u=a.pointerId,cr.set(u,dr(cr.get(u)||null,e,t,n,r,a)),!0}return!1}function ms(e){var t=hn(e.target);if(t!==null){var n=mn(t);if(n!==null){if(t=n.tag,t===13){if(t=Za(n),t!==null){e.blockedOn=t,fs(e.priority,function(){cs(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function ao(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=zl(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Cl=r,n.target.dispatchEvent(r),Cl=null}else return t=jr(n),t!==null&&Dl(t),e.blockedOn=n,!1;t.shift()}return!0}function hs(e,t,n){ao(e)&&n.delete(t)}function sf(){_l=!1,Qt!==null&&ao(Qt)&&(Qt=null),Gt!==null&&ao(Gt)&&(Gt=null),Kt!==null&&ao(Kt)&&(Kt=null),ur.forEach(hs),cr.forEach(hs)}function fr(e,t){e.blockedOn===t&&(e.blockedOn=null,_l||(_l=!0,l.unstable_scheduleCallback(l.unstable_NormalPriority,sf)))}function pr(e){function t(a){return fr(a,e)}if(0<io.length){fr(io[0],e);for(var n=1;n<io.length;n++){var r=io[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Qt!==null&&fr(Qt,e),Gt!==null&&fr(Gt,e),Kt!==null&&fr(Kt,e),ur.forEach(t),cr.forEach(t),n=0;n<qt.length;n++)r=qt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<qt.length&&(n=qt[0],n.blockedOn===null);)ms(n),n.blockedOn===null&&qt.shift()}var In=V.ReactCurrentBatchConfig,so=!0;function uf(e,t,n,r){var a=Se,u=In.transition;In.transition=null;try{Se=1,Ll(e,t,n,r)}finally{Se=a,In.transition=u}}function cf(e,t,n,r){var a=Se,u=In.transition;In.transition=null;try{Se=4,Ll(e,t,n,r)}finally{Se=a,In.transition=u}}function Ll(e,t,n,r){if(so){var a=zl(e,t,n,r);if(a===null)Zl(e,t,r,uo,n),ps(e,r);else if(af(a,e,t,n,r))r.stopPropagation();else if(ps(e,r),t&4&&-1<lf.indexOf(e)){for(;a!==null;){var u=jr(a);if(u!==null&&us(u),u=zl(e,t,n,r),u===null&&Zl(e,t,r,uo,n),u===a)break;a=u}a!==null&&r.stopPropagation()}else Zl(e,t,r,null,n)}}var uo=null;function zl(e,t,n,r){if(uo=null,e=bl(r),e=hn(e),e!==null)if(t=mn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Za(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return uo=e,null}function gs(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Yd()){case Rl:return 1;case ls:return 4;case to:case Xd:return 16;case is:return 536870912;default:return 16}default:return 16}}var Yt=null,Fl=null,co=null;function xs(){if(co)return co;var e,t=Fl,n=t.length,r,a="value"in Yt?Yt.value:Yt.textContent,u=a.length;for(e=0;e<n&&t[e]===a[e];e++);var m=n-e;for(r=1;r<=m&&t[n-r]===a[u-r];r++);return co=a.slice(e,1<r?1-r:void 0)}function fo(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function po(){return!0}function vs(){return!1}function it(e){function t(n,r,a,u,m){this._reactName=n,this._targetInst=a,this.type=r,this.nativeEvent=u,this.target=m,this.currentTarget=null;for(var k in e)e.hasOwnProperty(k)&&(n=e[k],this[k]=n?n(u):u[k]);return this.isDefaultPrevented=(u.defaultPrevented!=null?u.defaultPrevented:u.returnValue===!1)?po:vs,this.isPropagationStopped=vs,this}return B(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=po)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=po)},persist:function(){},isPersistent:po}),t}var Mn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Ol=it(Mn),mr=B({},Mn,{view:0,detail:0}),df=it(mr),Al,$l,hr,mo=B({},mr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Bl,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==hr&&(hr&&e.type==="mousemove"?(Al=e.screenX-hr.screenX,$l=e.screenY-hr.screenY):$l=Al=0,hr=e),Al)},movementY:function(e){return"movementY"in e?e.movementY:$l}}),ys=it(mo),ff=B({},mo,{dataTransfer:0}),pf=it(ff),mf=B({},mr,{relatedTarget:0}),Ul=it(mf),hf=B({},Mn,{animationName:0,elapsedTime:0,pseudoElement:0}),gf=it(hf),xf=B({},Mn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),vf=it(xf),yf=B({},Mn,{data:0}),ws=it(yf),wf={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Sf={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},kf={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Cf(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=kf[e])?!!t[e]:!1}function Bl(){return Cf}var bf=B({},mr,{key:function(e){if(e.key){var t=wf[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=fo(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Sf[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Bl,charCode:function(e){return e.type==="keypress"?fo(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?fo(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),jf=it(bf),Ef=B({},mo,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Ss=it(Ef),Nf=B({},mr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Bl}),Tf=it(Nf),Rf=B({},Mn,{propertyName:0,elapsedTime:0,pseudoElement:0}),If=it(Rf),Mf=B({},mo,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Pf=it(Mf),Df=[9,13,27,32],Vl=g&&"CompositionEvent"in window,gr=null;g&&"documentMode"in document&&(gr=document.documentMode);var _f=g&&"TextEvent"in window&&!gr,ks=g&&(!Vl||gr&&8<gr&&11>=gr),Cs=" ",bs=!1;function js(e,t){switch(e){case"keyup":return Df.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Es(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Pn=!1;function Lf(e,t){switch(e){case"compositionend":return Es(t);case"keypress":return t.which!==32?null:(bs=!0,Cs);case"textInput":return e=t.data,e===Cs&&bs?null:e;default:return null}}function zf(e,t){if(Pn)return e==="compositionend"||!Vl&&js(e,t)?(e=xs(),co=Fl=Yt=null,Pn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return ks&&t.locale!=="ko"?null:t.data;default:return null}}var Ff={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Ns(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Ff[e.type]:t==="textarea"}function Ts(e,t,n,r){Ka(r),t=yo(t,"onChange"),0<t.length&&(n=new Ol("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var xr=null,vr=null;function Of(e){Qs(e,0)}function ho(e){var t=Fn(e);if(za(t))return e}function Af(e,t){if(e==="change")return t}var Rs=!1;if(g){var Wl;if(g){var Hl="oninput"in document;if(!Hl){var Is=document.createElement("div");Is.setAttribute("oninput","return;"),Hl=typeof Is.oninput=="function"}Wl=Hl}else Wl=!1;Rs=Wl&&(!document.documentMode||9<document.documentMode)}function Ms(){xr&&(xr.detachEvent("onpropertychange",Ps),vr=xr=null)}function Ps(e){if(e.propertyName==="value"&&ho(vr)){var t=[];Ts(t,vr,e,bl(e)),Ja(Of,t)}}function $f(e,t,n){e==="focusin"?(Ms(),xr=t,vr=n,xr.attachEvent("onpropertychange",Ps)):e==="focusout"&&Ms()}function Uf(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return ho(vr)}function Bf(e,t){if(e==="click")return ho(t)}function Vf(e,t){if(e==="input"||e==="change")return ho(t)}function Wf(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var yt=typeof Object.is=="function"?Object.is:Wf;function yr(e,t){if(yt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var a=n[r];if(!x.call(t,a)||!yt(e[a],t[a]))return!1}return!0}function Ds(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function _s(e,t){var n=Ds(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Ds(n)}}function Ls(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Ls(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function zs(){for(var e=window,t=Xr();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Xr(e.document)}return t}function Ql(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Hf(e){var t=zs(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Ls(n.ownerDocument.documentElement,n)){if(r!==null&&Ql(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var a=n.textContent.length,u=Math.min(r.start,a);r=r.end===void 0?u:Math.min(r.end,a),!e.extend&&u>r&&(a=r,r=u,u=a),a=_s(n,u);var m=_s(n,r);a&&m&&(e.rangeCount!==1||e.anchorNode!==a.node||e.anchorOffset!==a.offset||e.focusNode!==m.node||e.focusOffset!==m.offset)&&(t=t.createRange(),t.setStart(a.node,a.offset),e.removeAllRanges(),u>r?(e.addRange(t),e.extend(m.node,m.offset)):(t.setEnd(m.node,m.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Qf=g&&"documentMode"in document&&11>=document.documentMode,Dn=null,Gl=null,wr=null,Kl=!1;function Fs(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Kl||Dn==null||Dn!==Xr(r)||(r=Dn,"selectionStart"in r&&Ql(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),wr&&yr(wr,r)||(wr=r,r=yo(Gl,"onSelect"),0<r.length&&(t=new Ol("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Dn)))}function go(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var _n={animationend:go("Animation","AnimationEnd"),animationiteration:go("Animation","AnimationIteration"),animationstart:go("Animation","AnimationStart"),transitionend:go("Transition","TransitionEnd")},ql={},Os={};g&&(Os=document.createElement("div").style,"AnimationEvent"in window||(delete _n.animationend.animation,delete _n.animationiteration.animation,delete _n.animationstart.animation),"TransitionEvent"in window||delete _n.transitionend.transition);function xo(e){if(ql[e])return ql[e];if(!_n[e])return e;var t=_n[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Os)return ql[e]=t[n];return e}var As=xo("animationend"),$s=xo("animationiteration"),Us=xo("animationstart"),Bs=xo("transitionend"),Vs=new Map,Ws="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Xt(e,t){Vs.set(e,t),f(t,[e])}for(var Yl=0;Yl<Ws.length;Yl++){var Xl=Ws[Yl],Gf=Xl.toLowerCase(),Kf=Xl[0].toUpperCase()+Xl.slice(1);Xt(Gf,"on"+Kf)}Xt(As,"onAnimationEnd"),Xt($s,"onAnimationIteration"),Xt(Us,"onAnimationStart"),Xt("dblclick","onDoubleClick"),Xt("focusin","onFocus"),Xt("focusout","onBlur"),Xt(Bs,"onTransitionEnd"),p("onMouseEnter",["mouseout","mouseover"]),p("onMouseLeave",["mouseout","mouseover"]),p("onPointerEnter",["pointerout","pointerover"]),p("onPointerLeave",["pointerout","pointerover"]),f("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),f("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),f("onBeforeInput",["compositionend","keypress","textInput","paste"]),f("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),f("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),f("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Sr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),qf=new Set("cancel close invalid load scroll toggle".split(" ").concat(Sr));function Hs(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Qd(r,t,void 0,e),e.currentTarget=null}function Qs(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],a=r.event;r=r.listeners;e:{var u=void 0;if(t)for(var m=r.length-1;0<=m;m--){var k=r[m],N=k.instance,$=k.currentTarget;if(k=k.listener,N!==u&&a.isPropagationStopped())break e;Hs(a,k,$),u=N}else for(m=0;m<r.length;m++){if(k=r[m],N=k.instance,$=k.currentTarget,k=k.listener,N!==u&&a.isPropagationStopped())break e;Hs(a,k,$),u=N}}}if(eo)throw e=Tl,eo=!1,Tl=null,e}function be(e,t){var n=t[li];n===void 0&&(n=t[li]=new Set);var r=e+"__bubble";n.has(r)||(Gs(t,e,2,!1),n.add(r))}function Jl(e,t,n){var r=0;t&&(r|=4),Gs(n,e,r,t)}var vo="_reactListening"+Math.random().toString(36).slice(2);function kr(e){if(!e[vo]){e[vo]=!0,c.forEach(function(n){n!=="selectionchange"&&(qf.has(n)||Jl(n,!1,e),Jl(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[vo]||(t[vo]=!0,Jl("selectionchange",!1,t))}}function Gs(e,t,n,r){switch(gs(t)){case 1:var a=uf;break;case 4:a=cf;break;default:a=Ll}n=a.bind(null,t,n,e),a=void 0,!Nl||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(a=!0),r?a!==void 0?e.addEventListener(t,n,{capture:!0,passive:a}):e.addEventListener(t,n,!0):a!==void 0?e.addEventListener(t,n,{passive:a}):e.addEventListener(t,n,!1)}function Zl(e,t,n,r,a){var u=r;if((t&1)===0&&(t&2)===0&&r!==null)e:for(;;){if(r===null)return;var m=r.tag;if(m===3||m===4){var k=r.stateNode.containerInfo;if(k===a||k.nodeType===8&&k.parentNode===a)break;if(m===4)for(m=r.return;m!==null;){var N=m.tag;if((N===3||N===4)&&(N=m.stateNode.containerInfo,N===a||N.nodeType===8&&N.parentNode===a))return;m=m.return}for(;k!==null;){if(m=hn(k),m===null)return;if(N=m.tag,N===5||N===6){r=u=m;continue e}k=k.parentNode}}r=r.return}Ja(function(){var $=u,G=bl(n),q=[];e:{var Q=Vs.get(e);if(Q!==void 0){var ee=Ol,oe=e;switch(e){case"keypress":if(fo(n)===0)break e;case"keydown":case"keyup":ee=jf;break;case"focusin":oe="focus",ee=Ul;break;case"focusout":oe="blur",ee=Ul;break;case"beforeblur":case"afterblur":ee=Ul;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":ee=ys;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":ee=pf;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":ee=Tf;break;case As:case $s:case Us:ee=gf;break;case Bs:ee=If;break;case"scroll":ee=df;break;case"wheel":ee=Pf;break;case"copy":case"cut":case"paste":ee=vf;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":ee=Ss}var le=(t&4)!==0,Le=!le&&e==="scroll",L=le?Q!==null?Q+"Capture":null:Q;le=[];for(var I=$,z;I!==null;){z=I;var Y=z.stateNode;if(z.tag===5&&Y!==null&&(z=Y,L!==null&&(Y=or(I,L),Y!=null&&le.push(Cr(I,Y,z)))),Le)break;I=I.return}0<le.length&&(Q=new ee(Q,oe,null,n,G),q.push({event:Q,listeners:le}))}}if((t&7)===0){e:{if(Q=e==="mouseover"||e==="pointerover",ee=e==="mouseout"||e==="pointerout",Q&&n!==Cl&&(oe=n.relatedTarget||n.fromElement)&&(hn(oe)||oe[Dt]))break e;if((ee||Q)&&(Q=G.window===G?G:(Q=G.ownerDocument)?Q.defaultView||Q.parentWindow:window,ee?(oe=n.relatedTarget||n.toElement,ee=$,oe=oe?hn(oe):null,oe!==null&&(Le=mn(oe),oe!==Le||oe.tag!==5&&oe.tag!==6)&&(oe=null)):(ee=null,oe=$),ee!==oe)){if(le=ys,Y="onMouseLeave",L="onMouseEnter",I="mouse",(e==="pointerout"||e==="pointerover")&&(le=Ss,Y="onPointerLeave",L="onPointerEnter",I="pointer"),Le=ee==null?Q:Fn(ee),z=oe==null?Q:Fn(oe),Q=new le(Y,I+"leave",ee,n,G),Q.target=Le,Q.relatedTarget=z,Y=null,hn(G)===$&&(le=new le(L,I+"enter",oe,n,G),le.target=z,le.relatedTarget=Le,Y=le),Le=Y,ee&&oe)t:{for(le=ee,L=oe,I=0,z=le;z;z=Ln(z))I++;for(z=0,Y=L;Y;Y=Ln(Y))z++;for(;0<I-z;)le=Ln(le),I--;for(;0<z-I;)L=Ln(L),z--;for(;I--;){if(le===L||L!==null&&le===L.alternate)break t;le=Ln(le),L=Ln(L)}le=null}else le=null;ee!==null&&Ks(q,Q,ee,le,!1),oe!==null&&Le!==null&&Ks(q,Le,oe,le,!0)}}e:{if(Q=$?Fn($):window,ee=Q.nodeName&&Q.nodeName.toLowerCase(),ee==="select"||ee==="input"&&Q.type==="file")var ie=Af;else if(Ns(Q))if(Rs)ie=Vf;else{ie=Uf;var ue=$f}else(ee=Q.nodeName)&&ee.toLowerCase()==="input"&&(Q.type==="checkbox"||Q.type==="radio")&&(ie=Bf);if(ie&&(ie=ie(e,$))){Ts(q,ie,n,G);break e}ue&&ue(e,Q,$),e==="focusout"&&(ue=Q._wrapperState)&&ue.controlled&&Q.type==="number"&&vl(Q,"number",Q.value)}switch(ue=$?Fn($):window,e){case"focusin":(Ns(ue)||ue.contentEditable==="true")&&(Dn=ue,Gl=$,wr=null);break;case"focusout":wr=Gl=Dn=null;break;case"mousedown":Kl=!0;break;case"contextmenu":case"mouseup":case"dragend":Kl=!1,Fs(q,n,G);break;case"selectionchange":if(Qf)break;case"keydown":case"keyup":Fs(q,n,G)}var ce;if(Vl)e:{switch(e){case"compositionstart":var de="onCompositionStart";break e;case"compositionend":de="onCompositionEnd";break e;case"compositionupdate":de="onCompositionUpdate";break e}de=void 0}else Pn?js(e,n)&&(de="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(de="onCompositionStart");de&&(ks&&n.locale!=="ko"&&(Pn||de!=="onCompositionStart"?de==="onCompositionEnd"&&Pn&&(ce=xs()):(Yt=G,Fl="value"in Yt?Yt.value:Yt.textContent,Pn=!0)),ue=yo($,de),0<ue.length&&(de=new ws(de,e,null,n,G),q.push({event:de,listeners:ue}),ce?de.data=ce:(ce=Es(n),ce!==null&&(de.data=ce)))),(ce=_f?Lf(e,n):zf(e,n))&&($=yo($,"onBeforeInput"),0<$.length&&(G=new ws("onBeforeInput","beforeinput",null,n,G),q.push({event:G,listeners:$}),G.data=ce))}Qs(q,t)})}function Cr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function yo(e,t){for(var n=t+"Capture",r=[];e!==null;){var a=e,u=a.stateNode;a.tag===5&&u!==null&&(a=u,u=or(e,n),u!=null&&r.unshift(Cr(e,u,a)),u=or(e,t),u!=null&&r.push(Cr(e,u,a))),e=e.return}return r}function Ln(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Ks(e,t,n,r,a){for(var u=t._reactName,m=[];n!==null&&n!==r;){var k=n,N=k.alternate,$=k.stateNode;if(N!==null&&N===r)break;k.tag===5&&$!==null&&(k=$,a?(N=or(n,u),N!=null&&m.unshift(Cr(n,N,k))):a||(N=or(n,u),N!=null&&m.push(Cr(n,N,k)))),n=n.return}m.length!==0&&e.push({event:t,listeners:m})}var Yf=/\r\n?/g,Xf=/\u0000|\uFFFD/g;function qs(e){return(typeof e=="string"?e:""+e).replace(Yf,`
`).replace(Xf,"")}function wo(e,t,n){if(t=qs(t),qs(e)!==t&&n)throw Error(i(425))}function So(){}var ei=null,ti=null;function ni(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var ri=typeof setTimeout=="function"?setTimeout:void 0,Jf=typeof clearTimeout=="function"?clearTimeout:void 0,Ys=typeof Promise=="function"?Promise:void 0,Zf=typeof queueMicrotask=="function"?queueMicrotask:typeof Ys<"u"?function(e){return Ys.resolve(null).then(e).catch(ep)}:ri;function ep(e){setTimeout(function(){throw e})}function oi(e,t){var n=t,r=0;do{var a=n.nextSibling;if(e.removeChild(n),a&&a.nodeType===8)if(n=a.data,n==="/$"){if(r===0){e.removeChild(a),pr(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=a}while(n);pr(t)}function Jt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Xs(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var zn=Math.random().toString(36).slice(2),Et="__reactFiber$"+zn,br="__reactProps$"+zn,Dt="__reactContainer$"+zn,li="__reactEvents$"+zn,tp="__reactListeners$"+zn,np="__reactHandles$"+zn;function hn(e){var t=e[Et];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Dt]||n[Et]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Xs(e);e!==null;){if(n=e[Et])return n;e=Xs(e)}return t}e=n,n=e.parentNode}return null}function jr(e){return e=e[Et]||e[Dt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Fn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(i(33))}function ko(e){return e[br]||null}var ii=[],On=-1;function Zt(e){return{current:e}}function je(e){0>On||(e.current=ii[On],ii[On]=null,On--)}function Ce(e,t){On++,ii[On]=e.current,e.current=t}var en={},He=Zt(en),Ze=Zt(!1),gn=en;function An(e,t){var n=e.type.contextTypes;if(!n)return en;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var a={},u;for(u in n)a[u]=t[u];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=a),a}function et(e){return e=e.childContextTypes,e!=null}function Co(){je(Ze),je(He)}function Js(e,t,n){if(He.current!==en)throw Error(i(168));Ce(He,t),Ce(Ze,n)}function Zs(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var a in r)if(!(a in t))throw Error(i(108,ke(e)||"Unknown",a));return B({},n,r)}function bo(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||en,gn=He.current,Ce(He,e),Ce(Ze,Ze.current),!0}function eu(e,t,n){var r=e.stateNode;if(!r)throw Error(i(169));n?(e=Zs(e,t,gn),r.__reactInternalMemoizedMergedChildContext=e,je(Ze),je(He),Ce(He,e)):je(Ze),Ce(Ze,n)}var _t=null,jo=!1,ai=!1;function tu(e){_t===null?_t=[e]:_t.push(e)}function rp(e){jo=!0,tu(e)}function tn(){if(!ai&&_t!==null){ai=!0;var e=0,t=Se;try{var n=_t;for(Se=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}_t=null,jo=!1}catch(a){throw _t!==null&&(_t=_t.slice(e+1)),rs(Rl,tn),a}finally{Se=t,ai=!1}}return null}var $n=[],Un=0,Eo=null,No=0,ct=[],dt=0,xn=null,Lt=1,zt="";function vn(e,t){$n[Un++]=No,$n[Un++]=Eo,Eo=e,No=t}function nu(e,t,n){ct[dt++]=Lt,ct[dt++]=zt,ct[dt++]=xn,xn=e;var r=Lt;e=zt;var a=32-vt(r)-1;r&=~(1<<a),n+=1;var u=32-vt(t)+a;if(30<u){var m=a-a%5;u=(r&(1<<m)-1).toString(32),r>>=m,a-=m,Lt=1<<32-vt(t)+a|n<<a|r,zt=u+e}else Lt=1<<u|n<<a|r,zt=e}function si(e){e.return!==null&&(vn(e,1),nu(e,1,0))}function ui(e){for(;e===Eo;)Eo=$n[--Un],$n[Un]=null,No=$n[--Un],$n[Un]=null;for(;e===xn;)xn=ct[--dt],ct[dt]=null,zt=ct[--dt],ct[dt]=null,Lt=ct[--dt],ct[dt]=null}var at=null,st=null,Ne=!1,wt=null;function ru(e,t){var n=ht(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function ou(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,at=e,st=Jt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,at=e,st=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=xn!==null?{id:Lt,overflow:zt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=ht(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,at=e,st=null,!0):!1;default:return!1}}function ci(e){return(e.mode&1)!==0&&(e.flags&128)===0}function di(e){if(Ne){var t=st;if(t){var n=t;if(!ou(e,t)){if(ci(e))throw Error(i(418));t=Jt(n.nextSibling);var r=at;t&&ou(e,t)?ru(r,n):(e.flags=e.flags&-4097|2,Ne=!1,at=e)}}else{if(ci(e))throw Error(i(418));e.flags=e.flags&-4097|2,Ne=!1,at=e}}}function lu(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;at=e}function To(e){if(e!==at)return!1;if(!Ne)return lu(e),Ne=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!ni(e.type,e.memoizedProps)),t&&(t=st)){if(ci(e))throw iu(),Error(i(418));for(;t;)ru(e,t),t=Jt(t.nextSibling)}if(lu(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(i(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){st=Jt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}st=null}}else st=at?Jt(e.stateNode.nextSibling):null;return!0}function iu(){for(var e=st;e;)e=Jt(e.nextSibling)}function Bn(){st=at=null,Ne=!1}function fi(e){wt===null?wt=[e]:wt.push(e)}var op=V.ReactCurrentBatchConfig;function Er(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(i(309));var r=n.stateNode}if(!r)throw Error(i(147,e));var a=r,u=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===u?t.ref:(t=function(m){var k=a.refs;m===null?delete k[u]:k[u]=m},t._stringRef=u,t)}if(typeof e!="string")throw Error(i(284));if(!n._owner)throw Error(i(290,e))}return e}function Ro(e,t){throw e=Object.prototype.toString.call(t),Error(i(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function au(e){var t=e._init;return t(e._payload)}function su(e){function t(L,I){if(e){var z=L.deletions;z===null?(L.deletions=[I],L.flags|=16):z.push(I)}}function n(L,I){if(!e)return null;for(;I!==null;)t(L,I),I=I.sibling;return null}function r(L,I){for(L=new Map;I!==null;)I.key!==null?L.set(I.key,I):L.set(I.index,I),I=I.sibling;return L}function a(L,I){return L=cn(L,I),L.index=0,L.sibling=null,L}function u(L,I,z){return L.index=z,e?(z=L.alternate,z!==null?(z=z.index,z<I?(L.flags|=2,I):z):(L.flags|=2,I)):(L.flags|=1048576,I)}function m(L){return e&&L.alternate===null&&(L.flags|=2),L}function k(L,I,z,Y){return I===null||I.tag!==6?(I=ra(z,L.mode,Y),I.return=L,I):(I=a(I,z),I.return=L,I)}function N(L,I,z,Y){var ie=z.type;return ie===M?G(L,I,z.props.children,Y,z.key):I!==null&&(I.elementType===ie||typeof ie=="object"&&ie!==null&&ie.$$typeof===fe&&au(ie)===I.type)?(Y=a(I,z.props),Y.ref=Er(L,I,z),Y.return=L,Y):(Y=Zo(z.type,z.key,z.props,null,L.mode,Y),Y.ref=Er(L,I,z),Y.return=L,Y)}function $(L,I,z,Y){return I===null||I.tag!==4||I.stateNode.containerInfo!==z.containerInfo||I.stateNode.implementation!==z.implementation?(I=oa(z,L.mode,Y),I.return=L,I):(I=a(I,z.children||[]),I.return=L,I)}function G(L,I,z,Y,ie){return I===null||I.tag!==7?(I=En(z,L.mode,Y,ie),I.return=L,I):(I=a(I,z),I.return=L,I)}function q(L,I,z){if(typeof I=="string"&&I!==""||typeof I=="number")return I=ra(""+I,L.mode,z),I.return=L,I;if(typeof I=="object"&&I!==null){switch(I.$$typeof){case P:return z=Zo(I.type,I.key,I.props,null,L.mode,z),z.ref=Er(L,null,I),z.return=L,z;case S:return I=oa(I,L.mode,z),I.return=L,I;case fe:var Y=I._init;return q(L,Y(I._payload),z)}if(tr(I)||O(I))return I=En(I,L.mode,z,null),I.return=L,I;Ro(L,I)}return null}function Q(L,I,z,Y){var ie=I!==null?I.key:null;if(typeof z=="string"&&z!==""||typeof z=="number")return ie!==null?null:k(L,I,""+z,Y);if(typeof z=="object"&&z!==null){switch(z.$$typeof){case P:return z.key===ie?N(L,I,z,Y):null;case S:return z.key===ie?$(L,I,z,Y):null;case fe:return ie=z._init,Q(L,I,ie(z._payload),Y)}if(tr(z)||O(z))return ie!==null?null:G(L,I,z,Y,null);Ro(L,z)}return null}function ee(L,I,z,Y,ie){if(typeof Y=="string"&&Y!==""||typeof Y=="number")return L=L.get(z)||null,k(I,L,""+Y,ie);if(typeof Y=="object"&&Y!==null){switch(Y.$$typeof){case P:return L=L.get(Y.key===null?z:Y.key)||null,N(I,L,Y,ie);case S:return L=L.get(Y.key===null?z:Y.key)||null,$(I,L,Y,ie);case fe:var ue=Y._init;return ee(L,I,z,ue(Y._payload),ie)}if(tr(Y)||O(Y))return L=L.get(z)||null,G(I,L,Y,ie,null);Ro(I,Y)}return null}function oe(L,I,z,Y){for(var ie=null,ue=null,ce=I,de=I=0,Ue=null;ce!==null&&de<z.length;de++){ce.index>de?(Ue=ce,ce=null):Ue=ce.sibling;var ve=Q(L,ce,z[de],Y);if(ve===null){ce===null&&(ce=Ue);break}e&&ce&&ve.alternate===null&&t(L,ce),I=u(ve,I,de),ue===null?ie=ve:ue.sibling=ve,ue=ve,ce=Ue}if(de===z.length)return n(L,ce),Ne&&vn(L,de),ie;if(ce===null){for(;de<z.length;de++)ce=q(L,z[de],Y),ce!==null&&(I=u(ce,I,de),ue===null?ie=ce:ue.sibling=ce,ue=ce);return Ne&&vn(L,de),ie}for(ce=r(L,ce);de<z.length;de++)Ue=ee(ce,L,de,z[de],Y),Ue!==null&&(e&&Ue.alternate!==null&&ce.delete(Ue.key===null?de:Ue.key),I=u(Ue,I,de),ue===null?ie=Ue:ue.sibling=Ue,ue=Ue);return e&&ce.forEach(function(dn){return t(L,dn)}),Ne&&vn(L,de),ie}function le(L,I,z,Y){var ie=O(z);if(typeof ie!="function")throw Error(i(150));if(z=ie.call(z),z==null)throw Error(i(151));for(var ue=ie=null,ce=I,de=I=0,Ue=null,ve=z.next();ce!==null&&!ve.done;de++,ve=z.next()){ce.index>de?(Ue=ce,ce=null):Ue=ce.sibling;var dn=Q(L,ce,ve.value,Y);if(dn===null){ce===null&&(ce=Ue);break}e&&ce&&dn.alternate===null&&t(L,ce),I=u(dn,I,de),ue===null?ie=dn:ue.sibling=dn,ue=dn,ce=Ue}if(ve.done)return n(L,ce),Ne&&vn(L,de),ie;if(ce===null){for(;!ve.done;de++,ve=z.next())ve=q(L,ve.value,Y),ve!==null&&(I=u(ve,I,de),ue===null?ie=ve:ue.sibling=ve,ue=ve);return Ne&&vn(L,de),ie}for(ce=r(L,ce);!ve.done;de++,ve=z.next())ve=ee(ce,L,de,ve.value,Y),ve!==null&&(e&&ve.alternate!==null&&ce.delete(ve.key===null?de:ve.key),I=u(ve,I,de),ue===null?ie=ve:ue.sibling=ve,ue=ve);return e&&ce.forEach(function(Fp){return t(L,Fp)}),Ne&&vn(L,de),ie}function Le(L,I,z,Y){if(typeof z=="object"&&z!==null&&z.type===M&&z.key===null&&(z=z.props.children),typeof z=="object"&&z!==null){switch(z.$$typeof){case P:e:{for(var ie=z.key,ue=I;ue!==null;){if(ue.key===ie){if(ie=z.type,ie===M){if(ue.tag===7){n(L,ue.sibling),I=a(ue,z.props.children),I.return=L,L=I;break e}}else if(ue.elementType===ie||typeof ie=="object"&&ie!==null&&ie.$$typeof===fe&&au(ie)===ue.type){n(L,ue.sibling),I=a(ue,z.props),I.ref=Er(L,ue,z),I.return=L,L=I;break e}n(L,ue);break}else t(L,ue);ue=ue.sibling}z.type===M?(I=En(z.props.children,L.mode,Y,z.key),I.return=L,L=I):(Y=Zo(z.type,z.key,z.props,null,L.mode,Y),Y.ref=Er(L,I,z),Y.return=L,L=Y)}return m(L);case S:e:{for(ue=z.key;I!==null;){if(I.key===ue)if(I.tag===4&&I.stateNode.containerInfo===z.containerInfo&&I.stateNode.implementation===z.implementation){n(L,I.sibling),I=a(I,z.children||[]),I.return=L,L=I;break e}else{n(L,I);break}else t(L,I);I=I.sibling}I=oa(z,L.mode,Y),I.return=L,L=I}return m(L);case fe:return ue=z._init,Le(L,I,ue(z._payload),Y)}if(tr(z))return oe(L,I,z,Y);if(O(z))return le(L,I,z,Y);Ro(L,z)}return typeof z=="string"&&z!==""||typeof z=="number"?(z=""+z,I!==null&&I.tag===6?(n(L,I.sibling),I=a(I,z),I.return=L,L=I):(n(L,I),I=ra(z,L.mode,Y),I.return=L,L=I),m(L)):n(L,I)}return Le}var Vn=su(!0),uu=su(!1),Io=Zt(null),Mo=null,Wn=null,pi=null;function mi(){pi=Wn=Mo=null}function hi(e){var t=Io.current;je(Io),e._currentValue=t}function gi(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Hn(e,t){Mo=e,pi=Wn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&((e.lanes&t)!==0&&(tt=!0),e.firstContext=null)}function ft(e){var t=e._currentValue;if(pi!==e)if(e={context:e,memoizedValue:t,next:null},Wn===null){if(Mo===null)throw Error(i(308));Wn=e,Mo.dependencies={lanes:0,firstContext:e}}else Wn=Wn.next=e;return t}var yn=null;function xi(e){yn===null?yn=[e]:yn.push(e)}function cu(e,t,n,r){var a=t.interleaved;return a===null?(n.next=n,xi(t)):(n.next=a.next,a.next=n),t.interleaved=n,Ft(e,r)}function Ft(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var nn=!1;function vi(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function du(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Ot(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function rn(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,(xe&2)!==0){var a=r.pending;return a===null?t.next=t:(t.next=a.next,a.next=t),r.pending=t,Ft(e,n)}return a=r.interleaved,a===null?(t.next=t,xi(r)):(t.next=a.next,a.next=t),r.interleaved=t,Ft(e,n)}function Po(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Pl(e,n)}}function fu(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var a=null,u=null;if(n=n.firstBaseUpdate,n!==null){do{var m={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};u===null?a=u=m:u=u.next=m,n=n.next}while(n!==null);u===null?a=u=t:u=u.next=t}else a=u=t;n={baseState:r.baseState,firstBaseUpdate:a,lastBaseUpdate:u,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Do(e,t,n,r){var a=e.updateQueue;nn=!1;var u=a.firstBaseUpdate,m=a.lastBaseUpdate,k=a.shared.pending;if(k!==null){a.shared.pending=null;var N=k,$=N.next;N.next=null,m===null?u=$:m.next=$,m=N;var G=e.alternate;G!==null&&(G=G.updateQueue,k=G.lastBaseUpdate,k!==m&&(k===null?G.firstBaseUpdate=$:k.next=$,G.lastBaseUpdate=N))}if(u!==null){var q=a.baseState;m=0,G=$=N=null,k=u;do{var Q=k.lane,ee=k.eventTime;if((r&Q)===Q){G!==null&&(G=G.next={eventTime:ee,lane:0,tag:k.tag,payload:k.payload,callback:k.callback,next:null});e:{var oe=e,le=k;switch(Q=t,ee=n,le.tag){case 1:if(oe=le.payload,typeof oe=="function"){q=oe.call(ee,q,Q);break e}q=oe;break e;case 3:oe.flags=oe.flags&-65537|128;case 0:if(oe=le.payload,Q=typeof oe=="function"?oe.call(ee,q,Q):oe,Q==null)break e;q=B({},q,Q);break e;case 2:nn=!0}}k.callback!==null&&k.lane!==0&&(e.flags|=64,Q=a.effects,Q===null?a.effects=[k]:Q.push(k))}else ee={eventTime:ee,lane:Q,tag:k.tag,payload:k.payload,callback:k.callback,next:null},G===null?($=G=ee,N=q):G=G.next=ee,m|=Q;if(k=k.next,k===null){if(k=a.shared.pending,k===null)break;Q=k,k=Q.next,Q.next=null,a.lastBaseUpdate=Q,a.shared.pending=null}}while(!0);if(G===null&&(N=q),a.baseState=N,a.firstBaseUpdate=$,a.lastBaseUpdate=G,t=a.shared.interleaved,t!==null){a=t;do m|=a.lane,a=a.next;while(a!==t)}else u===null&&(a.shared.lanes=0);kn|=m,e.lanes=m,e.memoizedState=q}}function pu(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],a=r.callback;if(a!==null){if(r.callback=null,r=n,typeof a!="function")throw Error(i(191,a));a.call(r)}}}var Nr={},Nt=Zt(Nr),Tr=Zt(Nr),Rr=Zt(Nr);function wn(e){if(e===Nr)throw Error(i(174));return e}function yi(e,t){switch(Ce(Rr,t),Ce(Tr,e),Ce(Nt,Nr),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:wl(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=wl(t,e)}je(Nt),Ce(Nt,t)}function Qn(){je(Nt),je(Tr),je(Rr)}function mu(e){wn(Rr.current);var t=wn(Nt.current),n=wl(t,e.type);t!==n&&(Ce(Tr,e),Ce(Nt,n))}function wi(e){Tr.current===e&&(je(Nt),je(Tr))}var Re=Zt(0);function _o(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Si=[];function ki(){for(var e=0;e<Si.length;e++)Si[e]._workInProgressVersionPrimary=null;Si.length=0}var Lo=V.ReactCurrentDispatcher,Ci=V.ReactCurrentBatchConfig,Sn=0,Ie=null,Fe=null,Ae=null,zo=!1,Ir=!1,Mr=0,lp=0;function Qe(){throw Error(i(321))}function bi(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!yt(e[n],t[n]))return!1;return!0}function ji(e,t,n,r,a,u){if(Sn=u,Ie=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Lo.current=e===null||e.memoizedState===null?up:cp,e=n(r,a),Ir){u=0;do{if(Ir=!1,Mr=0,25<=u)throw Error(i(301));u+=1,Ae=Fe=null,t.updateQueue=null,Lo.current=dp,e=n(r,a)}while(Ir)}if(Lo.current=Ao,t=Fe!==null&&Fe.next!==null,Sn=0,Ae=Fe=Ie=null,zo=!1,t)throw Error(i(300));return e}function Ei(){var e=Mr!==0;return Mr=0,e}function Tt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ae===null?Ie.memoizedState=Ae=e:Ae=Ae.next=e,Ae}function pt(){if(Fe===null){var e=Ie.alternate;e=e!==null?e.memoizedState:null}else e=Fe.next;var t=Ae===null?Ie.memoizedState:Ae.next;if(t!==null)Ae=t,Fe=e;else{if(e===null)throw Error(i(310));Fe=e,e={memoizedState:Fe.memoizedState,baseState:Fe.baseState,baseQueue:Fe.baseQueue,queue:Fe.queue,next:null},Ae===null?Ie.memoizedState=Ae=e:Ae=Ae.next=e}return Ae}function Pr(e,t){return typeof t=="function"?t(e):t}function Ni(e){var t=pt(),n=t.queue;if(n===null)throw Error(i(311));n.lastRenderedReducer=e;var r=Fe,a=r.baseQueue,u=n.pending;if(u!==null){if(a!==null){var m=a.next;a.next=u.next,u.next=m}r.baseQueue=a=u,n.pending=null}if(a!==null){u=a.next,r=r.baseState;var k=m=null,N=null,$=u;do{var G=$.lane;if((Sn&G)===G)N!==null&&(N=N.next={lane:0,action:$.action,hasEagerState:$.hasEagerState,eagerState:$.eagerState,next:null}),r=$.hasEagerState?$.eagerState:e(r,$.action);else{var q={lane:G,action:$.action,hasEagerState:$.hasEagerState,eagerState:$.eagerState,next:null};N===null?(k=N=q,m=r):N=N.next=q,Ie.lanes|=G,kn|=G}$=$.next}while($!==null&&$!==u);N===null?m=r:N.next=k,yt(r,t.memoizedState)||(tt=!0),t.memoizedState=r,t.baseState=m,t.baseQueue=N,n.lastRenderedState=r}if(e=n.interleaved,e!==null){a=e;do u=a.lane,Ie.lanes|=u,kn|=u,a=a.next;while(a!==e)}else a===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Ti(e){var t=pt(),n=t.queue;if(n===null)throw Error(i(311));n.lastRenderedReducer=e;var r=n.dispatch,a=n.pending,u=t.memoizedState;if(a!==null){n.pending=null;var m=a=a.next;do u=e(u,m.action),m=m.next;while(m!==a);yt(u,t.memoizedState)||(tt=!0),t.memoizedState=u,t.baseQueue===null&&(t.baseState=u),n.lastRenderedState=u}return[u,r]}function hu(){}function gu(e,t){var n=Ie,r=pt(),a=t(),u=!yt(r.memoizedState,a);if(u&&(r.memoizedState=a,tt=!0),r=r.queue,Ri(yu.bind(null,n,r,e),[e]),r.getSnapshot!==t||u||Ae!==null&&Ae.memoizedState.tag&1){if(n.flags|=2048,Dr(9,vu.bind(null,n,r,a,t),void 0,null),$e===null)throw Error(i(349));(Sn&30)!==0||xu(n,t,a)}return a}function xu(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=Ie.updateQueue,t===null?(t={lastEffect:null,stores:null},Ie.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function vu(e,t,n,r){t.value=n,t.getSnapshot=r,wu(t)&&Su(e)}function yu(e,t,n){return n(function(){wu(t)&&Su(e)})}function wu(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!yt(e,n)}catch{return!0}}function Su(e){var t=Ft(e,1);t!==null&&bt(t,e,1,-1)}function ku(e){var t=Tt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Pr,lastRenderedState:e},t.queue=e,e=e.dispatch=sp.bind(null,Ie,e),[t.memoizedState,e]}function Dr(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=Ie.updateQueue,t===null?(t={lastEffect:null,stores:null},Ie.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Cu(){return pt().memoizedState}function Fo(e,t,n,r){var a=Tt();Ie.flags|=e,a.memoizedState=Dr(1|t,n,void 0,r===void 0?null:r)}function Oo(e,t,n,r){var a=pt();r=r===void 0?null:r;var u=void 0;if(Fe!==null){var m=Fe.memoizedState;if(u=m.destroy,r!==null&&bi(r,m.deps)){a.memoizedState=Dr(t,n,u,r);return}}Ie.flags|=e,a.memoizedState=Dr(1|t,n,u,r)}function bu(e,t){return Fo(8390656,8,e,t)}function Ri(e,t){return Oo(2048,8,e,t)}function ju(e,t){return Oo(4,2,e,t)}function Eu(e,t){return Oo(4,4,e,t)}function Nu(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Tu(e,t,n){return n=n!=null?n.concat([e]):null,Oo(4,4,Nu.bind(null,t,e),n)}function Ii(){}function Ru(e,t){var n=pt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&bi(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Iu(e,t){var n=pt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&bi(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Mu(e,t,n){return(Sn&21)===0?(e.baseState&&(e.baseState=!1,tt=!0),e.memoizedState=n):(yt(n,t)||(n=as(),Ie.lanes|=n,kn|=n,e.baseState=!0),t)}function ip(e,t){var n=Se;Se=n!==0&&4>n?n:4,e(!0);var r=Ci.transition;Ci.transition={};try{e(!1),t()}finally{Se=n,Ci.transition=r}}function Pu(){return pt().memoizedState}function ap(e,t,n){var r=sn(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Du(e))_u(t,n);else if(n=cu(e,t,n,r),n!==null){var a=Xe();bt(n,e,r,a),Lu(n,t,r)}}function sp(e,t,n){var r=sn(e),a={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Du(e))_u(t,a);else{var u=e.alternate;if(e.lanes===0&&(u===null||u.lanes===0)&&(u=t.lastRenderedReducer,u!==null))try{var m=t.lastRenderedState,k=u(m,n);if(a.hasEagerState=!0,a.eagerState=k,yt(k,m)){var N=t.interleaved;N===null?(a.next=a,xi(t)):(a.next=N.next,N.next=a),t.interleaved=a;return}}catch{}finally{}n=cu(e,t,a,r),n!==null&&(a=Xe(),bt(n,e,r,a),Lu(n,t,r))}}function Du(e){var t=e.alternate;return e===Ie||t!==null&&t===Ie}function _u(e,t){Ir=zo=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Lu(e,t,n){if((n&4194240)!==0){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Pl(e,n)}}var Ao={readContext:ft,useCallback:Qe,useContext:Qe,useEffect:Qe,useImperativeHandle:Qe,useInsertionEffect:Qe,useLayoutEffect:Qe,useMemo:Qe,useReducer:Qe,useRef:Qe,useState:Qe,useDebugValue:Qe,useDeferredValue:Qe,useTransition:Qe,useMutableSource:Qe,useSyncExternalStore:Qe,useId:Qe,unstable_isNewReconciler:!1},up={readContext:ft,useCallback:function(e,t){return Tt().memoizedState=[e,t===void 0?null:t],e},useContext:ft,useEffect:bu,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Fo(4194308,4,Nu.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Fo(4194308,4,e,t)},useInsertionEffect:function(e,t){return Fo(4,2,e,t)},useMemo:function(e,t){var n=Tt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Tt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=ap.bind(null,Ie,e),[r.memoizedState,e]},useRef:function(e){var t=Tt();return e={current:e},t.memoizedState=e},useState:ku,useDebugValue:Ii,useDeferredValue:function(e){return Tt().memoizedState=e},useTransition:function(){var e=ku(!1),t=e[0];return e=ip.bind(null,e[1]),Tt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=Ie,a=Tt();if(Ne){if(n===void 0)throw Error(i(407));n=n()}else{if(n=t(),$e===null)throw Error(i(349));(Sn&30)!==0||xu(r,t,n)}a.memoizedState=n;var u={value:n,getSnapshot:t};return a.queue=u,bu(yu.bind(null,r,u,e),[e]),r.flags|=2048,Dr(9,vu.bind(null,r,u,n,t),void 0,null),n},useId:function(){var e=Tt(),t=$e.identifierPrefix;if(Ne){var n=zt,r=Lt;n=(r&~(1<<32-vt(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Mr++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=lp++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},cp={readContext:ft,useCallback:Ru,useContext:ft,useEffect:Ri,useImperativeHandle:Tu,useInsertionEffect:ju,useLayoutEffect:Eu,useMemo:Iu,useReducer:Ni,useRef:Cu,useState:function(){return Ni(Pr)},useDebugValue:Ii,useDeferredValue:function(e){var t=pt();return Mu(t,Fe.memoizedState,e)},useTransition:function(){var e=Ni(Pr)[0],t=pt().memoizedState;return[e,t]},useMutableSource:hu,useSyncExternalStore:gu,useId:Pu,unstable_isNewReconciler:!1},dp={readContext:ft,useCallback:Ru,useContext:ft,useEffect:Ri,useImperativeHandle:Tu,useInsertionEffect:ju,useLayoutEffect:Eu,useMemo:Iu,useReducer:Ti,useRef:Cu,useState:function(){return Ti(Pr)},useDebugValue:Ii,useDeferredValue:function(e){var t=pt();return Fe===null?t.memoizedState=e:Mu(t,Fe.memoizedState,e)},useTransition:function(){var e=Ti(Pr)[0],t=pt().memoizedState;return[e,t]},useMutableSource:hu,useSyncExternalStore:gu,useId:Pu,unstable_isNewReconciler:!1};function St(e,t){if(e&&e.defaultProps){t=B({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Mi(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:B({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var $o={isMounted:function(e){return(e=e._reactInternals)?mn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Xe(),a=sn(e),u=Ot(r,a);u.payload=t,n!=null&&(u.callback=n),t=rn(e,u,a),t!==null&&(bt(t,e,a,r),Po(t,e,a))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Xe(),a=sn(e),u=Ot(r,a);u.tag=1,u.payload=t,n!=null&&(u.callback=n),t=rn(e,u,a),t!==null&&(bt(t,e,a,r),Po(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Xe(),r=sn(e),a=Ot(n,r);a.tag=2,t!=null&&(a.callback=t),t=rn(e,a,r),t!==null&&(bt(t,e,r,n),Po(t,e,r))}};function zu(e,t,n,r,a,u,m){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,u,m):t.prototype&&t.prototype.isPureReactComponent?!yr(n,r)||!yr(a,u):!0}function Fu(e,t,n){var r=!1,a=en,u=t.contextType;return typeof u=="object"&&u!==null?u=ft(u):(a=et(t)?gn:He.current,r=t.contextTypes,u=(r=r!=null)?An(e,a):en),t=new t(n,u),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=$o,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=a,e.__reactInternalMemoizedMaskedChildContext=u),t}function Ou(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&$o.enqueueReplaceState(t,t.state,null)}function Pi(e,t,n,r){var a=e.stateNode;a.props=n,a.state=e.memoizedState,a.refs={},vi(e);var u=t.contextType;typeof u=="object"&&u!==null?a.context=ft(u):(u=et(t)?gn:He.current,a.context=An(e,u)),a.state=e.memoizedState,u=t.getDerivedStateFromProps,typeof u=="function"&&(Mi(e,t,u,n),a.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof a.getSnapshotBeforeUpdate=="function"||typeof a.UNSAFE_componentWillMount!="function"&&typeof a.componentWillMount!="function"||(t=a.state,typeof a.componentWillMount=="function"&&a.componentWillMount(),typeof a.UNSAFE_componentWillMount=="function"&&a.UNSAFE_componentWillMount(),t!==a.state&&$o.enqueueReplaceState(a,a.state,null),Do(e,n,a,r),a.state=e.memoizedState),typeof a.componentDidMount=="function"&&(e.flags|=4194308)}function Gn(e,t){try{var n="",r=t;do n+=he(r),r=r.return;while(r);var a=n}catch(u){a=`
Error generating stack: `+u.message+`
`+u.stack}return{value:e,source:t,stack:a,digest:null}}function Di(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function _i(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var fp=typeof WeakMap=="function"?WeakMap:Map;function Au(e,t,n){n=Ot(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Go||(Go=!0,qi=r),_i(e,t)},n}function $u(e,t,n){n=Ot(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var a=t.value;n.payload=function(){return r(a)},n.callback=function(){_i(e,t)}}var u=e.stateNode;return u!==null&&typeof u.componentDidCatch=="function"&&(n.callback=function(){_i(e,t),typeof r!="function"&&(ln===null?ln=new Set([this]):ln.add(this));var m=t.stack;this.componentDidCatch(t.value,{componentStack:m!==null?m:""})}),n}function Uu(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new fp;var a=new Set;r.set(t,a)}else a=r.get(t),a===void 0&&(a=new Set,r.set(t,a));a.has(n)||(a.add(n),e=Ep.bind(null,e,t,n),t.then(e,e))}function Bu(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Vu(e,t,n,r,a){return(e.mode&1)===0?(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Ot(-1,1),t.tag=2,rn(n,t,1))),n.lanes|=1),e):(e.flags|=65536,e.lanes=a,e)}var pp=V.ReactCurrentOwner,tt=!1;function Ye(e,t,n,r){t.child=e===null?uu(t,null,n,r):Vn(t,e.child,n,r)}function Wu(e,t,n,r,a){n=n.render;var u=t.ref;return Hn(t,a),r=ji(e,t,n,r,u,a),n=Ei(),e!==null&&!tt?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,At(e,t,a)):(Ne&&n&&si(t),t.flags|=1,Ye(e,t,r,a),t.child)}function Hu(e,t,n,r,a){if(e===null){var u=n.type;return typeof u=="function"&&!na(u)&&u.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=u,Qu(e,t,u,r,a)):(e=Zo(n.type,null,r,t,t.mode,a),e.ref=t.ref,e.return=t,t.child=e)}if(u=e.child,(e.lanes&a)===0){var m=u.memoizedProps;if(n=n.compare,n=n!==null?n:yr,n(m,r)&&e.ref===t.ref)return At(e,t,a)}return t.flags|=1,e=cn(u,r),e.ref=t.ref,e.return=t,t.child=e}function Qu(e,t,n,r,a){if(e!==null){var u=e.memoizedProps;if(yr(u,r)&&e.ref===t.ref)if(tt=!1,t.pendingProps=r=u,(e.lanes&a)!==0)(e.flags&131072)!==0&&(tt=!0);else return t.lanes=e.lanes,At(e,t,a)}return Li(e,t,n,r,a)}function Gu(e,t,n){var r=t.pendingProps,a=r.children,u=e!==null?e.memoizedState:null;if(r.mode==="hidden")if((t.mode&1)===0)t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Ce(qn,ut),ut|=n;else{if((n&1073741824)===0)return e=u!==null?u.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Ce(qn,ut),ut|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=u!==null?u.baseLanes:n,Ce(qn,ut),ut|=r}else u!==null?(r=u.baseLanes|n,t.memoizedState=null):r=n,Ce(qn,ut),ut|=r;return Ye(e,t,a,n),t.child}function Ku(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Li(e,t,n,r,a){var u=et(n)?gn:He.current;return u=An(t,u),Hn(t,a),n=ji(e,t,n,r,u,a),r=Ei(),e!==null&&!tt?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,At(e,t,a)):(Ne&&r&&si(t),t.flags|=1,Ye(e,t,n,a),t.child)}function qu(e,t,n,r,a){if(et(n)){var u=!0;bo(t)}else u=!1;if(Hn(t,a),t.stateNode===null)Bo(e,t),Fu(t,n,r),Pi(t,n,r,a),r=!0;else if(e===null){var m=t.stateNode,k=t.memoizedProps;m.props=k;var N=m.context,$=n.contextType;typeof $=="object"&&$!==null?$=ft($):($=et(n)?gn:He.current,$=An(t,$));var G=n.getDerivedStateFromProps,q=typeof G=="function"||typeof m.getSnapshotBeforeUpdate=="function";q||typeof m.UNSAFE_componentWillReceiveProps!="function"&&typeof m.componentWillReceiveProps!="function"||(k!==r||N!==$)&&Ou(t,m,r,$),nn=!1;var Q=t.memoizedState;m.state=Q,Do(t,r,m,a),N=t.memoizedState,k!==r||Q!==N||Ze.current||nn?(typeof G=="function"&&(Mi(t,n,G,r),N=t.memoizedState),(k=nn||zu(t,n,k,r,Q,N,$))?(q||typeof m.UNSAFE_componentWillMount!="function"&&typeof m.componentWillMount!="function"||(typeof m.componentWillMount=="function"&&m.componentWillMount(),typeof m.UNSAFE_componentWillMount=="function"&&m.UNSAFE_componentWillMount()),typeof m.componentDidMount=="function"&&(t.flags|=4194308)):(typeof m.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=N),m.props=r,m.state=N,m.context=$,r=k):(typeof m.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{m=t.stateNode,du(e,t),k=t.memoizedProps,$=t.type===t.elementType?k:St(t.type,k),m.props=$,q=t.pendingProps,Q=m.context,N=n.contextType,typeof N=="object"&&N!==null?N=ft(N):(N=et(n)?gn:He.current,N=An(t,N));var ee=n.getDerivedStateFromProps;(G=typeof ee=="function"||typeof m.getSnapshotBeforeUpdate=="function")||typeof m.UNSAFE_componentWillReceiveProps!="function"&&typeof m.componentWillReceiveProps!="function"||(k!==q||Q!==N)&&Ou(t,m,r,N),nn=!1,Q=t.memoizedState,m.state=Q,Do(t,r,m,a);var oe=t.memoizedState;k!==q||Q!==oe||Ze.current||nn?(typeof ee=="function"&&(Mi(t,n,ee,r),oe=t.memoizedState),($=nn||zu(t,n,$,r,Q,oe,N)||!1)?(G||typeof m.UNSAFE_componentWillUpdate!="function"&&typeof m.componentWillUpdate!="function"||(typeof m.componentWillUpdate=="function"&&m.componentWillUpdate(r,oe,N),typeof m.UNSAFE_componentWillUpdate=="function"&&m.UNSAFE_componentWillUpdate(r,oe,N)),typeof m.componentDidUpdate=="function"&&(t.flags|=4),typeof m.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof m.componentDidUpdate!="function"||k===e.memoizedProps&&Q===e.memoizedState||(t.flags|=4),typeof m.getSnapshotBeforeUpdate!="function"||k===e.memoizedProps&&Q===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=oe),m.props=r,m.state=oe,m.context=N,r=$):(typeof m.componentDidUpdate!="function"||k===e.memoizedProps&&Q===e.memoizedState||(t.flags|=4),typeof m.getSnapshotBeforeUpdate!="function"||k===e.memoizedProps&&Q===e.memoizedState||(t.flags|=1024),r=!1)}return zi(e,t,n,r,u,a)}function zi(e,t,n,r,a,u){Ku(e,t);var m=(t.flags&128)!==0;if(!r&&!m)return a&&eu(t,n,!1),At(e,t,u);r=t.stateNode,pp.current=t;var k=m&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&m?(t.child=Vn(t,e.child,null,u),t.child=Vn(t,null,k,u)):Ye(e,t,k,u),t.memoizedState=r.state,a&&eu(t,n,!0),t.child}function Yu(e){var t=e.stateNode;t.pendingContext?Js(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Js(e,t.context,!1),yi(e,t.containerInfo)}function Xu(e,t,n,r,a){return Bn(),fi(a),t.flags|=256,Ye(e,t,n,r),t.child}var Fi={dehydrated:null,treeContext:null,retryLane:0};function Oi(e){return{baseLanes:e,cachePool:null,transitions:null}}function Ju(e,t,n){var r=t.pendingProps,a=Re.current,u=!1,m=(t.flags&128)!==0,k;if((k=m)||(k=e!==null&&e.memoizedState===null?!1:(a&2)!==0),k?(u=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(a|=1),Ce(Re,a&1),e===null)return di(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?((t.mode&1)===0?t.lanes=1:e.data==="$!"?t.lanes=8:t.lanes=1073741824,null):(m=r.children,e=r.fallback,u?(r=t.mode,u=t.child,m={mode:"hidden",children:m},(r&1)===0&&u!==null?(u.childLanes=0,u.pendingProps=m):u=el(m,r,0,null),e=En(e,r,n,null),u.return=t,e.return=t,u.sibling=e,t.child=u,t.child.memoizedState=Oi(n),t.memoizedState=Fi,e):Ai(t,m));if(a=e.memoizedState,a!==null&&(k=a.dehydrated,k!==null))return mp(e,t,m,r,k,a,n);if(u){u=r.fallback,m=t.mode,a=e.child,k=a.sibling;var N={mode:"hidden",children:r.children};return(m&1)===0&&t.child!==a?(r=t.child,r.childLanes=0,r.pendingProps=N,t.deletions=null):(r=cn(a,N),r.subtreeFlags=a.subtreeFlags&14680064),k!==null?u=cn(k,u):(u=En(u,m,n,null),u.flags|=2),u.return=t,r.return=t,r.sibling=u,t.child=r,r=u,u=t.child,m=e.child.memoizedState,m=m===null?Oi(n):{baseLanes:m.baseLanes|n,cachePool:null,transitions:m.transitions},u.memoizedState=m,u.childLanes=e.childLanes&~n,t.memoizedState=Fi,r}return u=e.child,e=u.sibling,r=cn(u,{mode:"visible",children:r.children}),(t.mode&1)===0&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Ai(e,t){return t=el({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Uo(e,t,n,r){return r!==null&&fi(r),Vn(t,e.child,null,n),e=Ai(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function mp(e,t,n,r,a,u,m){if(n)return t.flags&256?(t.flags&=-257,r=Di(Error(i(422))),Uo(e,t,m,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(u=r.fallback,a=t.mode,r=el({mode:"visible",children:r.children},a,0,null),u=En(u,a,m,null),u.flags|=2,r.return=t,u.return=t,r.sibling=u,t.child=r,(t.mode&1)!==0&&Vn(t,e.child,null,m),t.child.memoizedState=Oi(m),t.memoizedState=Fi,u);if((t.mode&1)===0)return Uo(e,t,m,null);if(a.data==="$!"){if(r=a.nextSibling&&a.nextSibling.dataset,r)var k=r.dgst;return r=k,u=Error(i(419)),r=Di(u,r,void 0),Uo(e,t,m,r)}if(k=(m&e.childLanes)!==0,tt||k){if(r=$e,r!==null){switch(m&-m){case 4:a=2;break;case 16:a=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:a=32;break;case 536870912:a=268435456;break;default:a=0}a=(a&(r.suspendedLanes|m))!==0?0:a,a!==0&&a!==u.retryLane&&(u.retryLane=a,Ft(e,a),bt(r,e,a,-1))}return ta(),r=Di(Error(i(421))),Uo(e,t,m,r)}return a.data==="$?"?(t.flags|=128,t.child=e.child,t=Np.bind(null,e),a._reactRetry=t,null):(e=u.treeContext,st=Jt(a.nextSibling),at=t,Ne=!0,wt=null,e!==null&&(ct[dt++]=Lt,ct[dt++]=zt,ct[dt++]=xn,Lt=e.id,zt=e.overflow,xn=t),t=Ai(t,r.children),t.flags|=4096,t)}function Zu(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),gi(e.return,t,n)}function $i(e,t,n,r,a){var u=e.memoizedState;u===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:a}:(u.isBackwards=t,u.rendering=null,u.renderingStartTime=0,u.last=r,u.tail=n,u.tailMode=a)}function ec(e,t,n){var r=t.pendingProps,a=r.revealOrder,u=r.tail;if(Ye(e,t,r.children,n),r=Re.current,(r&2)!==0)r=r&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Zu(e,n,t);else if(e.tag===19)Zu(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(Ce(Re,r),(t.mode&1)===0)t.memoizedState=null;else switch(a){case"forwards":for(n=t.child,a=null;n!==null;)e=n.alternate,e!==null&&_o(e)===null&&(a=n),n=n.sibling;n=a,n===null?(a=t.child,t.child=null):(a=n.sibling,n.sibling=null),$i(t,!1,a,n,u);break;case"backwards":for(n=null,a=t.child,t.child=null;a!==null;){if(e=a.alternate,e!==null&&_o(e)===null){t.child=a;break}e=a.sibling,a.sibling=n,n=a,a=e}$i(t,!0,n,null,u);break;case"together":$i(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Bo(e,t){(t.mode&1)===0&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function At(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),kn|=t.lanes,(n&t.childLanes)===0)return null;if(e!==null&&t.child!==e.child)throw Error(i(153));if(t.child!==null){for(e=t.child,n=cn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=cn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function hp(e,t,n){switch(t.tag){case 3:Yu(t),Bn();break;case 5:mu(t);break;case 1:et(t.type)&&bo(t);break;case 4:yi(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,a=t.memoizedProps.value;Ce(Io,r._currentValue),r._currentValue=a;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(Ce(Re,Re.current&1),t.flags|=128,null):(n&t.child.childLanes)!==0?Ju(e,t,n):(Ce(Re,Re.current&1),e=At(e,t,n),e!==null?e.sibling:null);Ce(Re,Re.current&1);break;case 19:if(r=(n&t.childLanes)!==0,(e.flags&128)!==0){if(r)return ec(e,t,n);t.flags|=128}if(a=t.memoizedState,a!==null&&(a.rendering=null,a.tail=null,a.lastEffect=null),Ce(Re,Re.current),r)break;return null;case 22:case 23:return t.lanes=0,Gu(e,t,n)}return At(e,t,n)}var tc,Ui,nc,rc;tc=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},Ui=function(){},nc=function(e,t,n,r){var a=e.memoizedProps;if(a!==r){e=t.stateNode,wn(Nt.current);var u=null;switch(n){case"input":a=gl(e,a),r=gl(e,r),u=[];break;case"select":a=B({},a,{value:void 0}),r=B({},r,{value:void 0}),u=[];break;case"textarea":a=yl(e,a),r=yl(e,r),u=[];break;default:typeof a.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=So)}Sl(n,r);var m;n=null;for($ in a)if(!r.hasOwnProperty($)&&a.hasOwnProperty($)&&a[$]!=null)if($==="style"){var k=a[$];for(m in k)k.hasOwnProperty(m)&&(n||(n={}),n[m]="")}else $!=="dangerouslySetInnerHTML"&&$!=="children"&&$!=="suppressContentEditableWarning"&&$!=="suppressHydrationWarning"&&$!=="autoFocus"&&(d.hasOwnProperty($)?u||(u=[]):(u=u||[]).push($,null));for($ in r){var N=r[$];if(k=a!=null?a[$]:void 0,r.hasOwnProperty($)&&N!==k&&(N!=null||k!=null))if($==="style")if(k){for(m in k)!k.hasOwnProperty(m)||N&&N.hasOwnProperty(m)||(n||(n={}),n[m]="");for(m in N)N.hasOwnProperty(m)&&k[m]!==N[m]&&(n||(n={}),n[m]=N[m])}else n||(u||(u=[]),u.push($,n)),n=N;else $==="dangerouslySetInnerHTML"?(N=N?N.__html:void 0,k=k?k.__html:void 0,N!=null&&k!==N&&(u=u||[]).push($,N)):$==="children"?typeof N!="string"&&typeof N!="number"||(u=u||[]).push($,""+N):$!=="suppressContentEditableWarning"&&$!=="suppressHydrationWarning"&&(d.hasOwnProperty($)?(N!=null&&$==="onScroll"&&be("scroll",e),u||k===N||(u=[])):(u=u||[]).push($,N))}n&&(u=u||[]).push("style",n);var $=u;(t.updateQueue=$)&&(t.flags|=4)}},rc=function(e,t,n,r){n!==r&&(t.flags|=4)};function _r(e,t){if(!Ne)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Ge(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var a=e.child;a!==null;)n|=a.lanes|a.childLanes,r|=a.subtreeFlags&14680064,r|=a.flags&14680064,a.return=e,a=a.sibling;else for(a=e.child;a!==null;)n|=a.lanes|a.childLanes,r|=a.subtreeFlags,r|=a.flags,a.return=e,a=a.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function gp(e,t,n){var r=t.pendingProps;switch(ui(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ge(t),null;case 1:return et(t.type)&&Co(),Ge(t),null;case 3:return r=t.stateNode,Qn(),je(Ze),je(He),ki(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(To(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,wt!==null&&(Ji(wt),wt=null))),Ui(e,t),Ge(t),null;case 5:wi(t);var a=wn(Rr.current);if(n=t.type,e!==null&&t.stateNode!=null)nc(e,t,n,r,a),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(i(166));return Ge(t),null}if(e=wn(Nt.current),To(t)){r=t.stateNode,n=t.type;var u=t.memoizedProps;switch(r[Et]=t,r[br]=u,e=(t.mode&1)!==0,n){case"dialog":be("cancel",r),be("close",r);break;case"iframe":case"object":case"embed":be("load",r);break;case"video":case"audio":for(a=0;a<Sr.length;a++)be(Sr[a],r);break;case"source":be("error",r);break;case"img":case"image":case"link":be("error",r),be("load",r);break;case"details":be("toggle",r);break;case"input":Fa(r,u),be("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!u.multiple},be("invalid",r);break;case"textarea":$a(r,u),be("invalid",r)}Sl(n,u),a=null;for(var m in u)if(u.hasOwnProperty(m)){var k=u[m];m==="children"?typeof k=="string"?r.textContent!==k&&(u.suppressHydrationWarning!==!0&&wo(r.textContent,k,e),a=["children",k]):typeof k=="number"&&r.textContent!==""+k&&(u.suppressHydrationWarning!==!0&&wo(r.textContent,k,e),a=["children",""+k]):d.hasOwnProperty(m)&&k!=null&&m==="onScroll"&&be("scroll",r)}switch(n){case"input":Yr(r),Aa(r,u,!0);break;case"textarea":Yr(r),Ba(r);break;case"select":case"option":break;default:typeof u.onClick=="function"&&(r.onclick=So)}r=a,t.updateQueue=r,r!==null&&(t.flags|=4)}else{m=a.nodeType===9?a:a.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Va(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=m.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=m.createElement(n,{is:r.is}):(e=m.createElement(n),n==="select"&&(m=e,r.multiple?m.multiple=!0:r.size&&(m.size=r.size))):e=m.createElementNS(e,n),e[Et]=t,e[br]=r,tc(e,t,!1,!1),t.stateNode=e;e:{switch(m=kl(n,r),n){case"dialog":be("cancel",e),be("close",e),a=r;break;case"iframe":case"object":case"embed":be("load",e),a=r;break;case"video":case"audio":for(a=0;a<Sr.length;a++)be(Sr[a],e);a=r;break;case"source":be("error",e),a=r;break;case"img":case"image":case"link":be("error",e),be("load",e),a=r;break;case"details":be("toggle",e),a=r;break;case"input":Fa(e,r),a=gl(e,r),be("invalid",e);break;case"option":a=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},a=B({},r,{value:void 0}),be("invalid",e);break;case"textarea":$a(e,r),a=yl(e,r),be("invalid",e);break;default:a=r}Sl(n,a),k=a;for(u in k)if(k.hasOwnProperty(u)){var N=k[u];u==="style"?Qa(e,N):u==="dangerouslySetInnerHTML"?(N=N?N.__html:void 0,N!=null&&Wa(e,N)):u==="children"?typeof N=="string"?(n!=="textarea"||N!=="")&&nr(e,N):typeof N=="number"&&nr(e,""+N):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(d.hasOwnProperty(u)?N!=null&&u==="onScroll"&&be("scroll",e):N!=null&&W(e,u,N,m))}switch(n){case"input":Yr(e),Aa(e,r,!1);break;case"textarea":Yr(e),Ba(e);break;case"option":r.value!=null&&e.setAttribute("value",""+we(r.value));break;case"select":e.multiple=!!r.multiple,u=r.value,u!=null?Nn(e,!!r.multiple,u,!1):r.defaultValue!=null&&Nn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof a.onClick=="function"&&(e.onclick=So)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Ge(t),null;case 6:if(e&&t.stateNode!=null)rc(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(i(166));if(n=wn(Rr.current),wn(Nt.current),To(t)){if(r=t.stateNode,n=t.memoizedProps,r[Et]=t,(u=r.nodeValue!==n)&&(e=at,e!==null))switch(e.tag){case 3:wo(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&wo(r.nodeValue,n,(e.mode&1)!==0)}u&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[Et]=t,t.stateNode=r}return Ge(t),null;case 13:if(je(Re),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(Ne&&st!==null&&(t.mode&1)!==0&&(t.flags&128)===0)iu(),Bn(),t.flags|=98560,u=!1;else if(u=To(t),r!==null&&r.dehydrated!==null){if(e===null){if(!u)throw Error(i(318));if(u=t.memoizedState,u=u!==null?u.dehydrated:null,!u)throw Error(i(317));u[Et]=t}else Bn(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;Ge(t),u=!1}else wt!==null&&(Ji(wt),wt=null),u=!0;if(!u)return t.flags&65536?t:null}return(t.flags&128)!==0?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,(t.mode&1)!==0&&(e===null||(Re.current&1)!==0?Oe===0&&(Oe=3):ta())),t.updateQueue!==null&&(t.flags|=4),Ge(t),null);case 4:return Qn(),Ui(e,t),e===null&&kr(t.stateNode.containerInfo),Ge(t),null;case 10:return hi(t.type._context),Ge(t),null;case 17:return et(t.type)&&Co(),Ge(t),null;case 19:if(je(Re),u=t.memoizedState,u===null)return Ge(t),null;if(r=(t.flags&128)!==0,m=u.rendering,m===null)if(r)_r(u,!1);else{if(Oe!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(m=_o(e),m!==null){for(t.flags|=128,_r(u,!1),r=m.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)u=n,e=r,u.flags&=14680066,m=u.alternate,m===null?(u.childLanes=0,u.lanes=e,u.child=null,u.subtreeFlags=0,u.memoizedProps=null,u.memoizedState=null,u.updateQueue=null,u.dependencies=null,u.stateNode=null):(u.childLanes=m.childLanes,u.lanes=m.lanes,u.child=m.child,u.subtreeFlags=0,u.deletions=null,u.memoizedProps=m.memoizedProps,u.memoizedState=m.memoizedState,u.updateQueue=m.updateQueue,u.type=m.type,e=m.dependencies,u.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return Ce(Re,Re.current&1|2),t.child}e=e.sibling}u.tail!==null&&_e()>Yn&&(t.flags|=128,r=!0,_r(u,!1),t.lanes=4194304)}else{if(!r)if(e=_o(m),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),_r(u,!0),u.tail===null&&u.tailMode==="hidden"&&!m.alternate&&!Ne)return Ge(t),null}else 2*_e()-u.renderingStartTime>Yn&&n!==1073741824&&(t.flags|=128,r=!0,_r(u,!1),t.lanes=4194304);u.isBackwards?(m.sibling=t.child,t.child=m):(n=u.last,n!==null?n.sibling=m:t.child=m,u.last=m)}return u.tail!==null?(t=u.tail,u.rendering=t,u.tail=t.sibling,u.renderingStartTime=_e(),t.sibling=null,n=Re.current,Ce(Re,r?n&1|2:n&1),t):(Ge(t),null);case 22:case 23:return ea(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&(t.mode&1)!==0?(ut&1073741824)!==0&&(Ge(t),t.subtreeFlags&6&&(t.flags|=8192)):Ge(t),null;case 24:return null;case 25:return null}throw Error(i(156,t.tag))}function xp(e,t){switch(ui(t),t.tag){case 1:return et(t.type)&&Co(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Qn(),je(Ze),je(He),ki(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 5:return wi(t),null;case 13:if(je(Re),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(i(340));Bn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return je(Re),null;case 4:return Qn(),null;case 10:return hi(t.type._context),null;case 22:case 23:return ea(),null;case 24:return null;default:return null}}var Vo=!1,Ke=!1,vp=typeof WeakSet=="function"?WeakSet:Set,ne=null;function Kn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){Pe(e,t,r)}else n.current=null}function Bi(e,t,n){try{n()}catch(r){Pe(e,t,r)}}var oc=!1;function yp(e,t){if(ei=so,e=zs(),Ql(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var a=r.anchorOffset,u=r.focusNode;r=r.focusOffset;try{n.nodeType,u.nodeType}catch{n=null;break e}var m=0,k=-1,N=-1,$=0,G=0,q=e,Q=null;t:for(;;){for(var ee;q!==n||a!==0&&q.nodeType!==3||(k=m+a),q!==u||r!==0&&q.nodeType!==3||(N=m+r),q.nodeType===3&&(m+=q.nodeValue.length),(ee=q.firstChild)!==null;)Q=q,q=ee;for(;;){if(q===e)break t;if(Q===n&&++$===a&&(k=m),Q===u&&++G===r&&(N=m),(ee=q.nextSibling)!==null)break;q=Q,Q=q.parentNode}q=ee}n=k===-1||N===-1?null:{start:k,end:N}}else n=null}n=n||{start:0,end:0}}else n=null;for(ti={focusedElem:e,selectionRange:n},so=!1,ne=t;ne!==null;)if(t=ne,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,ne=e;else for(;ne!==null;){t=ne;try{var oe=t.alternate;if((t.flags&1024)!==0)switch(t.tag){case 0:case 11:case 15:break;case 1:if(oe!==null){var le=oe.memoizedProps,Le=oe.memoizedState,L=t.stateNode,I=L.getSnapshotBeforeUpdate(t.elementType===t.type?le:St(t.type,le),Le);L.__reactInternalSnapshotBeforeUpdate=I}break;case 3:var z=t.stateNode.containerInfo;z.nodeType===1?z.textContent="":z.nodeType===9&&z.documentElement&&z.removeChild(z.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(i(163))}}catch(Y){Pe(t,t.return,Y)}if(e=t.sibling,e!==null){e.return=t.return,ne=e;break}ne=t.return}return oe=oc,oc=!1,oe}function Lr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var a=r=r.next;do{if((a.tag&e)===e){var u=a.destroy;a.destroy=void 0,u!==void 0&&Bi(t,n,u)}a=a.next}while(a!==r)}}function Wo(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Vi(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function lc(e){var t=e.alternate;t!==null&&(e.alternate=null,lc(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Et],delete t[br],delete t[li],delete t[tp],delete t[np])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function ic(e){return e.tag===5||e.tag===3||e.tag===4}function ac(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||ic(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Wi(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=So));else if(r!==4&&(e=e.child,e!==null))for(Wi(e,t,n),e=e.sibling;e!==null;)Wi(e,t,n),e=e.sibling}function Hi(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Hi(e,t,n),e=e.sibling;e!==null;)Hi(e,t,n),e=e.sibling}var Be=null,kt=!1;function on(e,t,n){for(n=n.child;n!==null;)sc(e,t,n),n=n.sibling}function sc(e,t,n){if(jt&&typeof jt.onCommitFiberUnmount=="function")try{jt.onCommitFiberUnmount(no,n)}catch{}switch(n.tag){case 5:Ke||Kn(n,t);case 6:var r=Be,a=kt;Be=null,on(e,t,n),Be=r,kt=a,Be!==null&&(kt?(e=Be,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):Be.removeChild(n.stateNode));break;case 18:Be!==null&&(kt?(e=Be,n=n.stateNode,e.nodeType===8?oi(e.parentNode,n):e.nodeType===1&&oi(e,n),pr(e)):oi(Be,n.stateNode));break;case 4:r=Be,a=kt,Be=n.stateNode.containerInfo,kt=!0,on(e,t,n),Be=r,kt=a;break;case 0:case 11:case 14:case 15:if(!Ke&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){a=r=r.next;do{var u=a,m=u.destroy;u=u.tag,m!==void 0&&((u&2)!==0||(u&4)!==0)&&Bi(n,t,m),a=a.next}while(a!==r)}on(e,t,n);break;case 1:if(!Ke&&(Kn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(k){Pe(n,t,k)}on(e,t,n);break;case 21:on(e,t,n);break;case 22:n.mode&1?(Ke=(r=Ke)||n.memoizedState!==null,on(e,t,n),Ke=r):on(e,t,n);break;default:on(e,t,n)}}function uc(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new vp),t.forEach(function(r){var a=Tp.bind(null,e,r);n.has(r)||(n.add(r),r.then(a,a))})}}function Ct(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var a=n[r];try{var u=e,m=t,k=m;e:for(;k!==null;){switch(k.tag){case 5:Be=k.stateNode,kt=!1;break e;case 3:Be=k.stateNode.containerInfo,kt=!0;break e;case 4:Be=k.stateNode.containerInfo,kt=!0;break e}k=k.return}if(Be===null)throw Error(i(160));sc(u,m,a),Be=null,kt=!1;var N=a.alternate;N!==null&&(N.return=null),a.return=null}catch($){Pe(a,t,$)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)cc(t,e),t=t.sibling}function cc(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Ct(t,e),Rt(e),r&4){try{Lr(3,e,e.return),Wo(3,e)}catch(le){Pe(e,e.return,le)}try{Lr(5,e,e.return)}catch(le){Pe(e,e.return,le)}}break;case 1:Ct(t,e),Rt(e),r&512&&n!==null&&Kn(n,n.return);break;case 5:if(Ct(t,e),Rt(e),r&512&&n!==null&&Kn(n,n.return),e.flags&32){var a=e.stateNode;try{nr(a,"")}catch(le){Pe(e,e.return,le)}}if(r&4&&(a=e.stateNode,a!=null)){var u=e.memoizedProps,m=n!==null?n.memoizedProps:u,k=e.type,N=e.updateQueue;if(e.updateQueue=null,N!==null)try{k==="input"&&u.type==="radio"&&u.name!=null&&Oa(a,u),kl(k,m);var $=kl(k,u);for(m=0;m<N.length;m+=2){var G=N[m],q=N[m+1];G==="style"?Qa(a,q):G==="dangerouslySetInnerHTML"?Wa(a,q):G==="children"?nr(a,q):W(a,G,q,$)}switch(k){case"input":xl(a,u);break;case"textarea":Ua(a,u);break;case"select":var Q=a._wrapperState.wasMultiple;a._wrapperState.wasMultiple=!!u.multiple;var ee=u.value;ee!=null?Nn(a,!!u.multiple,ee,!1):Q!==!!u.multiple&&(u.defaultValue!=null?Nn(a,!!u.multiple,u.defaultValue,!0):Nn(a,!!u.multiple,u.multiple?[]:"",!1))}a[br]=u}catch(le){Pe(e,e.return,le)}}break;case 6:if(Ct(t,e),Rt(e),r&4){if(e.stateNode===null)throw Error(i(162));a=e.stateNode,u=e.memoizedProps;try{a.nodeValue=u}catch(le){Pe(e,e.return,le)}}break;case 3:if(Ct(t,e),Rt(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{pr(t.containerInfo)}catch(le){Pe(e,e.return,le)}break;case 4:Ct(t,e),Rt(e);break;case 13:Ct(t,e),Rt(e),a=e.child,a.flags&8192&&(u=a.memoizedState!==null,a.stateNode.isHidden=u,!u||a.alternate!==null&&a.alternate.memoizedState!==null||(Ki=_e())),r&4&&uc(e);break;case 22:if(G=n!==null&&n.memoizedState!==null,e.mode&1?(Ke=($=Ke)||G,Ct(t,e),Ke=$):Ct(t,e),Rt(e),r&8192){if($=e.memoizedState!==null,(e.stateNode.isHidden=$)&&!G&&(e.mode&1)!==0)for(ne=e,G=e.child;G!==null;){for(q=ne=G;ne!==null;){switch(Q=ne,ee=Q.child,Q.tag){case 0:case 11:case 14:case 15:Lr(4,Q,Q.return);break;case 1:Kn(Q,Q.return);var oe=Q.stateNode;if(typeof oe.componentWillUnmount=="function"){r=Q,n=Q.return;try{t=r,oe.props=t.memoizedProps,oe.state=t.memoizedState,oe.componentWillUnmount()}catch(le){Pe(r,n,le)}}break;case 5:Kn(Q,Q.return);break;case 22:if(Q.memoizedState!==null){pc(q);continue}}ee!==null?(ee.return=Q,ne=ee):pc(q)}G=G.sibling}e:for(G=null,q=e;;){if(q.tag===5){if(G===null){G=q;try{a=q.stateNode,$?(u=a.style,typeof u.setProperty=="function"?u.setProperty("display","none","important"):u.display="none"):(k=q.stateNode,N=q.memoizedProps.style,m=N!=null&&N.hasOwnProperty("display")?N.display:null,k.style.display=Ha("display",m))}catch(le){Pe(e,e.return,le)}}}else if(q.tag===6){if(G===null)try{q.stateNode.nodeValue=$?"":q.memoizedProps}catch(le){Pe(e,e.return,le)}}else if((q.tag!==22&&q.tag!==23||q.memoizedState===null||q===e)&&q.child!==null){q.child.return=q,q=q.child;continue}if(q===e)break e;for(;q.sibling===null;){if(q.return===null||q.return===e)break e;G===q&&(G=null),q=q.return}G===q&&(G=null),q.sibling.return=q.return,q=q.sibling}}break;case 19:Ct(t,e),Rt(e),r&4&&uc(e);break;case 21:break;default:Ct(t,e),Rt(e)}}function Rt(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(ic(n)){var r=n;break e}n=n.return}throw Error(i(160))}switch(r.tag){case 5:var a=r.stateNode;r.flags&32&&(nr(a,""),r.flags&=-33);var u=ac(e);Hi(e,u,a);break;case 3:case 4:var m=r.stateNode.containerInfo,k=ac(e);Wi(e,k,m);break;default:throw Error(i(161))}}catch(N){Pe(e,e.return,N)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function wp(e,t,n){ne=e,dc(e)}function dc(e,t,n){for(var r=(e.mode&1)!==0;ne!==null;){var a=ne,u=a.child;if(a.tag===22&&r){var m=a.memoizedState!==null||Vo;if(!m){var k=a.alternate,N=k!==null&&k.memoizedState!==null||Ke;k=Vo;var $=Ke;if(Vo=m,(Ke=N)&&!$)for(ne=a;ne!==null;)m=ne,N=m.child,m.tag===22&&m.memoizedState!==null?mc(a):N!==null?(N.return=m,ne=N):mc(a);for(;u!==null;)ne=u,dc(u),u=u.sibling;ne=a,Vo=k,Ke=$}fc(e)}else(a.subtreeFlags&8772)!==0&&u!==null?(u.return=a,ne=u):fc(e)}}function fc(e){for(;ne!==null;){var t=ne;if((t.flags&8772)!==0){var n=t.alternate;try{if((t.flags&8772)!==0)switch(t.tag){case 0:case 11:case 15:Ke||Wo(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!Ke)if(n===null)r.componentDidMount();else{var a=t.elementType===t.type?n.memoizedProps:St(t.type,n.memoizedProps);r.componentDidUpdate(a,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var u=t.updateQueue;u!==null&&pu(t,u,r);break;case 3:var m=t.updateQueue;if(m!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}pu(t,m,n)}break;case 5:var k=t.stateNode;if(n===null&&t.flags&4){n=k;var N=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":N.autoFocus&&n.focus();break;case"img":N.src&&(n.src=N.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var $=t.alternate;if($!==null){var G=$.memoizedState;if(G!==null){var q=G.dehydrated;q!==null&&pr(q)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(i(163))}Ke||t.flags&512&&Vi(t)}catch(Q){Pe(t,t.return,Q)}}if(t===e){ne=null;break}if(n=t.sibling,n!==null){n.return=t.return,ne=n;break}ne=t.return}}function pc(e){for(;ne!==null;){var t=ne;if(t===e){ne=null;break}var n=t.sibling;if(n!==null){n.return=t.return,ne=n;break}ne=t.return}}function mc(e){for(;ne!==null;){var t=ne;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Wo(4,t)}catch(N){Pe(t,n,N)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var a=t.return;try{r.componentDidMount()}catch(N){Pe(t,a,N)}}var u=t.return;try{Vi(t)}catch(N){Pe(t,u,N)}break;case 5:var m=t.return;try{Vi(t)}catch(N){Pe(t,m,N)}}}catch(N){Pe(t,t.return,N)}if(t===e){ne=null;break}var k=t.sibling;if(k!==null){k.return=t.return,ne=k;break}ne=t.return}}var Sp=Math.ceil,Ho=V.ReactCurrentDispatcher,Qi=V.ReactCurrentOwner,mt=V.ReactCurrentBatchConfig,xe=0,$e=null,ze=null,Ve=0,ut=0,qn=Zt(0),Oe=0,zr=null,kn=0,Qo=0,Gi=0,Fr=null,nt=null,Ki=0,Yn=1/0,$t=null,Go=!1,qi=null,ln=null,Ko=!1,an=null,qo=0,Or=0,Yi=null,Yo=-1,Xo=0;function Xe(){return(xe&6)!==0?_e():Yo!==-1?Yo:Yo=_e()}function sn(e){return(e.mode&1)===0?1:(xe&2)!==0&&Ve!==0?Ve&-Ve:op.transition!==null?(Xo===0&&(Xo=as()),Xo):(e=Se,e!==0||(e=window.event,e=e===void 0?16:gs(e.type)),e)}function bt(e,t,n,r){if(50<Or)throw Or=0,Yi=null,Error(i(185));sr(e,n,r),((xe&2)===0||e!==$e)&&(e===$e&&((xe&2)===0&&(Qo|=n),Oe===4&&un(e,Ve)),rt(e,r),n===1&&xe===0&&(t.mode&1)===0&&(Yn=_e()+500,jo&&tn()))}function rt(e,t){var n=e.callbackNode;rf(e,t);var r=lo(e,e===$e?Ve:0);if(r===0)n!==null&&os(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&os(n),t===1)e.tag===0?rp(gc.bind(null,e)):tu(gc.bind(null,e)),Zf(function(){(xe&6)===0&&tn()}),n=null;else{switch(ss(r)){case 1:n=Rl;break;case 4:n=ls;break;case 16:n=to;break;case 536870912:n=is;break;default:n=to}n=bc(n,hc.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function hc(e,t){if(Yo=-1,Xo=0,(xe&6)!==0)throw Error(i(327));var n=e.callbackNode;if(Xn()&&e.callbackNode!==n)return null;var r=lo(e,e===$e?Ve:0);if(r===0)return null;if((r&30)!==0||(r&e.expiredLanes)!==0||t)t=Jo(e,r);else{t=r;var a=xe;xe|=2;var u=vc();($e!==e||Ve!==t)&&($t=null,Yn=_e()+500,bn(e,t));do try{bp();break}catch(k){xc(e,k)}while(!0);mi(),Ho.current=u,xe=a,ze!==null?t=0:($e=null,Ve=0,t=Oe)}if(t!==0){if(t===2&&(a=Il(e),a!==0&&(r=a,t=Xi(e,a))),t===1)throw n=zr,bn(e,0),un(e,r),rt(e,_e()),n;if(t===6)un(e,r);else{if(a=e.current.alternate,(r&30)===0&&!kp(a)&&(t=Jo(e,r),t===2&&(u=Il(e),u!==0&&(r=u,t=Xi(e,u))),t===1))throw n=zr,bn(e,0),un(e,r),rt(e,_e()),n;switch(e.finishedWork=a,e.finishedLanes=r,t){case 0:case 1:throw Error(i(345));case 2:jn(e,nt,$t);break;case 3:if(un(e,r),(r&130023424)===r&&(t=Ki+500-_e(),10<t)){if(lo(e,0)!==0)break;if(a=e.suspendedLanes,(a&r)!==r){Xe(),e.pingedLanes|=e.suspendedLanes&a;break}e.timeoutHandle=ri(jn.bind(null,e,nt,$t),t);break}jn(e,nt,$t);break;case 4:if(un(e,r),(r&4194240)===r)break;for(t=e.eventTimes,a=-1;0<r;){var m=31-vt(r);u=1<<m,m=t[m],m>a&&(a=m),r&=~u}if(r=a,r=_e()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Sp(r/1960))-r,10<r){e.timeoutHandle=ri(jn.bind(null,e,nt,$t),r);break}jn(e,nt,$t);break;case 5:jn(e,nt,$t);break;default:throw Error(i(329))}}}return rt(e,_e()),e.callbackNode===n?hc.bind(null,e):null}function Xi(e,t){var n=Fr;return e.current.memoizedState.isDehydrated&&(bn(e,t).flags|=256),e=Jo(e,t),e!==2&&(t=nt,nt=n,t!==null&&Ji(t)),e}function Ji(e){nt===null?nt=e:nt.push.apply(nt,e)}function kp(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var a=n[r],u=a.getSnapshot;a=a.value;try{if(!yt(u(),a))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function un(e,t){for(t&=~Gi,t&=~Qo,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-vt(t),r=1<<n;e[n]=-1,t&=~r}}function gc(e){if((xe&6)!==0)throw Error(i(327));Xn();var t=lo(e,0);if((t&1)===0)return rt(e,_e()),null;var n=Jo(e,t);if(e.tag!==0&&n===2){var r=Il(e);r!==0&&(t=r,n=Xi(e,r))}if(n===1)throw n=zr,bn(e,0),un(e,t),rt(e,_e()),n;if(n===6)throw Error(i(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,jn(e,nt,$t),rt(e,_e()),null}function Zi(e,t){var n=xe;xe|=1;try{return e(t)}finally{xe=n,xe===0&&(Yn=_e()+500,jo&&tn())}}function Cn(e){an!==null&&an.tag===0&&(xe&6)===0&&Xn();var t=xe;xe|=1;var n=mt.transition,r=Se;try{if(mt.transition=null,Se=1,e)return e()}finally{Se=r,mt.transition=n,xe=t,(xe&6)===0&&tn()}}function ea(){ut=qn.current,je(qn)}function bn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Jf(n)),ze!==null)for(n=ze.return;n!==null;){var r=n;switch(ui(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Co();break;case 3:Qn(),je(Ze),je(He),ki();break;case 5:wi(r);break;case 4:Qn();break;case 13:je(Re);break;case 19:je(Re);break;case 10:hi(r.type._context);break;case 22:case 23:ea()}n=n.return}if($e=e,ze=e=cn(e.current,null),Ve=ut=t,Oe=0,zr=null,Gi=Qo=kn=0,nt=Fr=null,yn!==null){for(t=0;t<yn.length;t++)if(n=yn[t],r=n.interleaved,r!==null){n.interleaved=null;var a=r.next,u=n.pending;if(u!==null){var m=u.next;u.next=a,r.next=m}n.pending=r}yn=null}return e}function xc(e,t){do{var n=ze;try{if(mi(),Lo.current=Ao,zo){for(var r=Ie.memoizedState;r!==null;){var a=r.queue;a!==null&&(a.pending=null),r=r.next}zo=!1}if(Sn=0,Ae=Fe=Ie=null,Ir=!1,Mr=0,Qi.current=null,n===null||n.return===null){Oe=1,zr=t,ze=null;break}e:{var u=e,m=n.return,k=n,N=t;if(t=Ve,k.flags|=32768,N!==null&&typeof N=="object"&&typeof N.then=="function"){var $=N,G=k,q=G.tag;if((G.mode&1)===0&&(q===0||q===11||q===15)){var Q=G.alternate;Q?(G.updateQueue=Q.updateQueue,G.memoizedState=Q.memoizedState,G.lanes=Q.lanes):(G.updateQueue=null,G.memoizedState=null)}var ee=Bu(m);if(ee!==null){ee.flags&=-257,Vu(ee,m,k,u,t),ee.mode&1&&Uu(u,$,t),t=ee,N=$;var oe=t.updateQueue;if(oe===null){var le=new Set;le.add(N),t.updateQueue=le}else oe.add(N);break e}else{if((t&1)===0){Uu(u,$,t),ta();break e}N=Error(i(426))}}else if(Ne&&k.mode&1){var Le=Bu(m);if(Le!==null){(Le.flags&65536)===0&&(Le.flags|=256),Vu(Le,m,k,u,t),fi(Gn(N,k));break e}}u=N=Gn(N,k),Oe!==4&&(Oe=2),Fr===null?Fr=[u]:Fr.push(u),u=m;do{switch(u.tag){case 3:u.flags|=65536,t&=-t,u.lanes|=t;var L=Au(u,N,t);fu(u,L);break e;case 1:k=N;var I=u.type,z=u.stateNode;if((u.flags&128)===0&&(typeof I.getDerivedStateFromError=="function"||z!==null&&typeof z.componentDidCatch=="function"&&(ln===null||!ln.has(z)))){u.flags|=65536,t&=-t,u.lanes|=t;var Y=$u(u,k,t);fu(u,Y);break e}}u=u.return}while(u!==null)}wc(n)}catch(ie){t=ie,ze===n&&n!==null&&(ze=n=n.return);continue}break}while(!0)}function vc(){var e=Ho.current;return Ho.current=Ao,e===null?Ao:e}function ta(){(Oe===0||Oe===3||Oe===2)&&(Oe=4),$e===null||(kn&268435455)===0&&(Qo&268435455)===0||un($e,Ve)}function Jo(e,t){var n=xe;xe|=2;var r=vc();($e!==e||Ve!==t)&&($t=null,bn(e,t));do try{Cp();break}catch(a){xc(e,a)}while(!0);if(mi(),xe=n,Ho.current=r,ze!==null)throw Error(i(261));return $e=null,Ve=0,Oe}function Cp(){for(;ze!==null;)yc(ze)}function bp(){for(;ze!==null&&!Kd();)yc(ze)}function yc(e){var t=Cc(e.alternate,e,ut);e.memoizedProps=e.pendingProps,t===null?wc(e):ze=t,Qi.current=null}function wc(e){var t=e;do{var n=t.alternate;if(e=t.return,(t.flags&32768)===0){if(n=gp(n,t,ut),n!==null){ze=n;return}}else{if(n=xp(n,t),n!==null){n.flags&=32767,ze=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{Oe=6,ze=null;return}}if(t=t.sibling,t!==null){ze=t;return}ze=t=e}while(t!==null);Oe===0&&(Oe=5)}function jn(e,t,n){var r=Se,a=mt.transition;try{mt.transition=null,Se=1,jp(e,t,n,r)}finally{mt.transition=a,Se=r}return null}function jp(e,t,n,r){do Xn();while(an!==null);if((xe&6)!==0)throw Error(i(327));n=e.finishedWork;var a=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(i(177));e.callbackNode=null,e.callbackPriority=0;var u=n.lanes|n.childLanes;if(of(e,u),e===$e&&(ze=$e=null,Ve=0),(n.subtreeFlags&2064)===0&&(n.flags&2064)===0||Ko||(Ko=!0,bc(to,function(){return Xn(),null})),u=(n.flags&15990)!==0,(n.subtreeFlags&15990)!==0||u){u=mt.transition,mt.transition=null;var m=Se;Se=1;var k=xe;xe|=4,Qi.current=null,yp(e,n),cc(n,e),Hf(ti),so=!!ei,ti=ei=null,e.current=n,wp(n),qd(),xe=k,Se=m,mt.transition=u}else e.current=n;if(Ko&&(Ko=!1,an=e,qo=a),u=e.pendingLanes,u===0&&(ln=null),Jd(n.stateNode),rt(e,_e()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)a=t[n],r(a.value,{componentStack:a.stack,digest:a.digest});if(Go)throw Go=!1,e=qi,qi=null,e;return(qo&1)!==0&&e.tag!==0&&Xn(),u=e.pendingLanes,(u&1)!==0?e===Yi?Or++:(Or=0,Yi=e):Or=0,tn(),null}function Xn(){if(an!==null){var e=ss(qo),t=mt.transition,n=Se;try{if(mt.transition=null,Se=16>e?16:e,an===null)var r=!1;else{if(e=an,an=null,qo=0,(xe&6)!==0)throw Error(i(331));var a=xe;for(xe|=4,ne=e.current;ne!==null;){var u=ne,m=u.child;if((ne.flags&16)!==0){var k=u.deletions;if(k!==null){for(var N=0;N<k.length;N++){var $=k[N];for(ne=$;ne!==null;){var G=ne;switch(G.tag){case 0:case 11:case 15:Lr(8,G,u)}var q=G.child;if(q!==null)q.return=G,ne=q;else for(;ne!==null;){G=ne;var Q=G.sibling,ee=G.return;if(lc(G),G===$){ne=null;break}if(Q!==null){Q.return=ee,ne=Q;break}ne=ee}}}var oe=u.alternate;if(oe!==null){var le=oe.child;if(le!==null){oe.child=null;do{var Le=le.sibling;le.sibling=null,le=Le}while(le!==null)}}ne=u}}if((u.subtreeFlags&2064)!==0&&m!==null)m.return=u,ne=m;else e:for(;ne!==null;){if(u=ne,(u.flags&2048)!==0)switch(u.tag){case 0:case 11:case 15:Lr(9,u,u.return)}var L=u.sibling;if(L!==null){L.return=u.return,ne=L;break e}ne=u.return}}var I=e.current;for(ne=I;ne!==null;){m=ne;var z=m.child;if((m.subtreeFlags&2064)!==0&&z!==null)z.return=m,ne=z;else e:for(m=I;ne!==null;){if(k=ne,(k.flags&2048)!==0)try{switch(k.tag){case 0:case 11:case 15:Wo(9,k)}}catch(ie){Pe(k,k.return,ie)}if(k===m){ne=null;break e}var Y=k.sibling;if(Y!==null){Y.return=k.return,ne=Y;break e}ne=k.return}}if(xe=a,tn(),jt&&typeof jt.onPostCommitFiberRoot=="function")try{jt.onPostCommitFiberRoot(no,e)}catch{}r=!0}return r}finally{Se=n,mt.transition=t}}return!1}function Sc(e,t,n){t=Gn(n,t),t=Au(e,t,1),e=rn(e,t,1),t=Xe(),e!==null&&(sr(e,1,t),rt(e,t))}function Pe(e,t,n){if(e.tag===3)Sc(e,e,n);else for(;t!==null;){if(t.tag===3){Sc(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(ln===null||!ln.has(r))){e=Gn(n,e),e=$u(t,e,1),t=rn(t,e,1),e=Xe(),t!==null&&(sr(t,1,e),rt(t,e));break}}t=t.return}}function Ep(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=Xe(),e.pingedLanes|=e.suspendedLanes&n,$e===e&&(Ve&n)===n&&(Oe===4||Oe===3&&(Ve&130023424)===Ve&&500>_e()-Ki?bn(e,0):Gi|=n),rt(e,t)}function kc(e,t){t===0&&((e.mode&1)===0?t=1:(t=oo,oo<<=1,(oo&130023424)===0&&(oo=4194304)));var n=Xe();e=Ft(e,t),e!==null&&(sr(e,t,n),rt(e,n))}function Np(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),kc(e,n)}function Tp(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,a=e.memoizedState;a!==null&&(n=a.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(i(314))}r!==null&&r.delete(t),kc(e,n)}var Cc;Cc=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Ze.current)tt=!0;else{if((e.lanes&n)===0&&(t.flags&128)===0)return tt=!1,hp(e,t,n);tt=(e.flags&131072)!==0}else tt=!1,Ne&&(t.flags&1048576)!==0&&nu(t,No,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Bo(e,t),e=t.pendingProps;var a=An(t,He.current);Hn(t,n),a=ji(null,t,r,e,a,n);var u=Ei();return t.flags|=1,typeof a=="object"&&a!==null&&typeof a.render=="function"&&a.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,et(r)?(u=!0,bo(t)):u=!1,t.memoizedState=a.state!==null&&a.state!==void 0?a.state:null,vi(t),a.updater=$o,t.stateNode=a,a._reactInternals=t,Pi(t,r,e,n),t=zi(null,t,r,!0,u,n)):(t.tag=0,Ne&&u&&si(t),Ye(null,t,a,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Bo(e,t),e=t.pendingProps,a=r._init,r=a(r._payload),t.type=r,a=t.tag=Ip(r),e=St(r,e),a){case 0:t=Li(null,t,r,e,n);break e;case 1:t=qu(null,t,r,e,n);break e;case 11:t=Wu(null,t,r,e,n);break e;case 14:t=Hu(null,t,r,St(r.type,e),n);break e}throw Error(i(306,r,""))}return t;case 0:return r=t.type,a=t.pendingProps,a=t.elementType===r?a:St(r,a),Li(e,t,r,a,n);case 1:return r=t.type,a=t.pendingProps,a=t.elementType===r?a:St(r,a),qu(e,t,r,a,n);case 3:e:{if(Yu(t),e===null)throw Error(i(387));r=t.pendingProps,u=t.memoizedState,a=u.element,du(e,t),Do(t,r,null,n);var m=t.memoizedState;if(r=m.element,u.isDehydrated)if(u={element:r,isDehydrated:!1,cache:m.cache,pendingSuspenseBoundaries:m.pendingSuspenseBoundaries,transitions:m.transitions},t.updateQueue.baseState=u,t.memoizedState=u,t.flags&256){a=Gn(Error(i(423)),t),t=Xu(e,t,r,n,a);break e}else if(r!==a){a=Gn(Error(i(424)),t),t=Xu(e,t,r,n,a);break e}else for(st=Jt(t.stateNode.containerInfo.firstChild),at=t,Ne=!0,wt=null,n=uu(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Bn(),r===a){t=At(e,t,n);break e}Ye(e,t,r,n)}t=t.child}return t;case 5:return mu(t),e===null&&di(t),r=t.type,a=t.pendingProps,u=e!==null?e.memoizedProps:null,m=a.children,ni(r,a)?m=null:u!==null&&ni(r,u)&&(t.flags|=32),Ku(e,t),Ye(e,t,m,n),t.child;case 6:return e===null&&di(t),null;case 13:return Ju(e,t,n);case 4:return yi(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Vn(t,null,r,n):Ye(e,t,r,n),t.child;case 11:return r=t.type,a=t.pendingProps,a=t.elementType===r?a:St(r,a),Wu(e,t,r,a,n);case 7:return Ye(e,t,t.pendingProps,n),t.child;case 8:return Ye(e,t,t.pendingProps.children,n),t.child;case 12:return Ye(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,a=t.pendingProps,u=t.memoizedProps,m=a.value,Ce(Io,r._currentValue),r._currentValue=m,u!==null)if(yt(u.value,m)){if(u.children===a.children&&!Ze.current){t=At(e,t,n);break e}}else for(u=t.child,u!==null&&(u.return=t);u!==null;){var k=u.dependencies;if(k!==null){m=u.child;for(var N=k.firstContext;N!==null;){if(N.context===r){if(u.tag===1){N=Ot(-1,n&-n),N.tag=2;var $=u.updateQueue;if($!==null){$=$.shared;var G=$.pending;G===null?N.next=N:(N.next=G.next,G.next=N),$.pending=N}}u.lanes|=n,N=u.alternate,N!==null&&(N.lanes|=n),gi(u.return,n,t),k.lanes|=n;break}N=N.next}}else if(u.tag===10)m=u.type===t.type?null:u.child;else if(u.tag===18){if(m=u.return,m===null)throw Error(i(341));m.lanes|=n,k=m.alternate,k!==null&&(k.lanes|=n),gi(m,n,t),m=u.sibling}else m=u.child;if(m!==null)m.return=u;else for(m=u;m!==null;){if(m===t){m=null;break}if(u=m.sibling,u!==null){u.return=m.return,m=u;break}m=m.return}u=m}Ye(e,t,a.children,n),t=t.child}return t;case 9:return a=t.type,r=t.pendingProps.children,Hn(t,n),a=ft(a),r=r(a),t.flags|=1,Ye(e,t,r,n),t.child;case 14:return r=t.type,a=St(r,t.pendingProps),a=St(r.type,a),Hu(e,t,r,a,n);case 15:return Qu(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,a=t.pendingProps,a=t.elementType===r?a:St(r,a),Bo(e,t),t.tag=1,et(r)?(e=!0,bo(t)):e=!1,Hn(t,n),Fu(t,r,a),Pi(t,r,a,n),zi(null,t,r,!0,e,n);case 19:return ec(e,t,n);case 22:return Gu(e,t,n)}throw Error(i(156,t.tag))};function bc(e,t){return rs(e,t)}function Rp(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function ht(e,t,n,r){return new Rp(e,t,n,r)}function na(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Ip(e){if(typeof e=="function")return na(e)?1:0;if(e!=null){if(e=e.$$typeof,e===X)return 11;if(e===K)return 14}return 2}function cn(e,t){var n=e.alternate;return n===null?(n=ht(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Zo(e,t,n,r,a,u){var m=2;if(r=e,typeof e=="function")na(e)&&(m=1);else if(typeof e=="string")m=5;else e:switch(e){case M:return En(n.children,a,u,t);case F:m=8,a|=8;break;case H:return e=ht(12,n,t,a|2),e.elementType=H,e.lanes=u,e;case ae:return e=ht(13,n,t,a),e.elementType=ae,e.lanes=u,e;case Z:return e=ht(19,n,t,a),e.elementType=Z,e.lanes=u,e;case se:return el(n,a,u,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case J:m=10;break e;case te:m=9;break e;case X:m=11;break e;case K:m=14;break e;case fe:m=16,r=null;break e}throw Error(i(130,e==null?e:typeof e,""))}return t=ht(m,n,t,a),t.elementType=e,t.type=r,t.lanes=u,t}function En(e,t,n,r){return e=ht(7,e,r,t),e.lanes=n,e}function el(e,t,n,r){return e=ht(22,e,r,t),e.elementType=se,e.lanes=n,e.stateNode={isHidden:!1},e}function ra(e,t,n){return e=ht(6,e,null,t),e.lanes=n,e}function oa(e,t,n){return t=ht(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Mp(e,t,n,r,a){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Ml(0),this.expirationTimes=Ml(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Ml(0),this.identifierPrefix=r,this.onRecoverableError=a,this.mutableSourceEagerHydrationData=null}function la(e,t,n,r,a,u,m,k,N){return e=new Mp(e,t,n,k,N),t===1?(t=1,u===!0&&(t|=8)):t=0,u=ht(3,null,null,t),e.current=u,u.stateNode=e,u.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},vi(u),e}function Pp(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:S,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function jc(e){if(!e)return en;e=e._reactInternals;e:{if(mn(e)!==e||e.tag!==1)throw Error(i(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(et(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(i(171))}if(e.tag===1){var n=e.type;if(et(n))return Zs(e,n,t)}return t}function Ec(e,t,n,r,a,u,m,k,N){return e=la(n,r,!0,e,a,u,m,k,N),e.context=jc(null),n=e.current,r=Xe(),a=sn(n),u=Ot(r,a),u.callback=t??null,rn(n,u,a),e.current.lanes=a,sr(e,a,r),rt(e,r),e}function tl(e,t,n,r){var a=t.current,u=Xe(),m=sn(a);return n=jc(n),t.context===null?t.context=n:t.pendingContext=n,t=Ot(u,m),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=rn(a,t,m),e!==null&&(bt(e,a,m,u),Po(e,a,m)),m}function nl(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Nc(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function ia(e,t){Nc(e,t),(e=e.alternate)&&Nc(e,t)}function Dp(){return null}var Tc=typeof reportError=="function"?reportError:function(e){console.error(e)};function aa(e){this._internalRoot=e}rl.prototype.render=aa.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(i(409));tl(e,t,null,null)},rl.prototype.unmount=aa.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Cn(function(){tl(null,e,null,null)}),t[Dt]=null}};function rl(e){this._internalRoot=e}rl.prototype.unstable_scheduleHydration=function(e){if(e){var t=ds();e={blockedOn:null,target:e,priority:t};for(var n=0;n<qt.length&&t!==0&&t<qt[n].priority;n++);qt.splice(n,0,e),n===0&&ms(e)}};function sa(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function ol(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Rc(){}function _p(e,t,n,r,a){if(a){if(typeof r=="function"){var u=r;r=function(){var $=nl(m);u.call($)}}var m=Ec(t,r,e,0,null,!1,!1,"",Rc);return e._reactRootContainer=m,e[Dt]=m.current,kr(e.nodeType===8?e.parentNode:e),Cn(),m}for(;a=e.lastChild;)e.removeChild(a);if(typeof r=="function"){var k=r;r=function(){var $=nl(N);k.call($)}}var N=la(e,0,!1,null,null,!1,!1,"",Rc);return e._reactRootContainer=N,e[Dt]=N.current,kr(e.nodeType===8?e.parentNode:e),Cn(function(){tl(t,N,n,r)}),N}function ll(e,t,n,r,a){var u=n._reactRootContainer;if(u){var m=u;if(typeof a=="function"){var k=a;a=function(){var N=nl(m);k.call(N)}}tl(t,m,e,a)}else m=_p(n,t,e,a,r);return nl(m)}us=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=ar(t.pendingLanes);n!==0&&(Pl(t,n|1),rt(t,_e()),(xe&6)===0&&(Yn=_e()+500,tn()))}break;case 13:Cn(function(){var r=Ft(e,1);if(r!==null){var a=Xe();bt(r,e,1,a)}}),ia(e,1)}},Dl=function(e){if(e.tag===13){var t=Ft(e,134217728);if(t!==null){var n=Xe();bt(t,e,134217728,n)}ia(e,134217728)}},cs=function(e){if(e.tag===13){var t=sn(e),n=Ft(e,t);if(n!==null){var r=Xe();bt(n,e,t,r)}ia(e,t)}},ds=function(){return Se},fs=function(e,t){var n=Se;try{return Se=e,t()}finally{Se=n}},jl=function(e,t,n){switch(t){case"input":if(xl(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var a=ko(r);if(!a)throw Error(i(90));za(r),xl(r,a)}}}break;case"textarea":Ua(e,n);break;case"select":t=n.value,t!=null&&Nn(e,!!n.multiple,t,!1)}},Ya=Zi,Xa=Cn;var Lp={usingClientEntryPoint:!1,Events:[jr,Fn,ko,Ka,qa,Zi]},Ar={findFiberByHostInstance:hn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},zp={bundleType:Ar.bundleType,version:Ar.version,rendererPackageName:Ar.rendererPackageName,rendererConfig:Ar.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:V.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=ts(e),e===null?null:e.stateNode},findFiberByHostInstance:Ar.findFiberByHostInstance||Dp,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var il=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!il.isDisabled&&il.supportsFiber)try{no=il.inject(zp),jt=il}catch{}}return ot.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Lp,ot.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!sa(t))throw Error(i(200));return Pp(e,t,null,n)},ot.createRoot=function(e,t){if(!sa(e))throw Error(i(299));var n=!1,r="",a=Tc;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(a=t.onRecoverableError)),t=la(e,1,!1,null,null,n,!1,r,a),e[Dt]=t.current,kr(e.nodeType===8?e.parentNode:e),new aa(t)},ot.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(i(188)):(e=Object.keys(e).join(","),Error(i(268,e)));return e=ts(t),e=e===null?null:e.stateNode,e},ot.flushSync=function(e){return Cn(e)},ot.hydrate=function(e,t,n){if(!ol(t))throw Error(i(200));return ll(null,e,t,!0,n)},ot.hydrateRoot=function(e,t,n){if(!sa(e))throw Error(i(405));var r=n!=null&&n.hydratedSources||null,a=!1,u="",m=Tc;if(n!=null&&(n.unstable_strictMode===!0&&(a=!0),n.identifierPrefix!==void 0&&(u=n.identifierPrefix),n.onRecoverableError!==void 0&&(m=n.onRecoverableError)),t=Ec(t,null,e,1,n??null,a,!1,u,m),e[Dt]=t.current,kr(e),r)for(e=0;e<r.length;e++)n=r[e],a=n._getVersion,a=a(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,a]:t.mutableSourceEagerHydrationData.push(n,a);return new rl(t)},ot.render=function(e,t,n){if(!ol(t))throw Error(i(200));return ll(null,e,t,!1,n)},ot.unmountComponentAtNode=function(e){if(!ol(e))throw Error(i(40));return e._reactRootContainer?(Cn(function(){ll(null,null,e,!1,function(){e._reactRootContainer=null,e[Dt]=null})}),!0):!1},ot.unstable_batchedUpdates=Zi,ot.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!ol(n))throw Error(i(200));if(e==null||e._reactInternals===void 0)throw Error(i(38));return ll(e,t,n,!1,r)},ot.version="18.3.1-next-f1338f8080-20240426",ot}var Fc;function Gp(){if(Fc)return da.exports;Fc=1;function o(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(o)}catch(l){console.error(l)}}return o(),da.exports=Qp(),da.exports}var Oc;function Kp(){if(Oc)return al;Oc=1;var o=Gp();return al.createRoot=o.createRoot,al.hydrateRoot=o.hydrateRoot,al}var qp=Kp(),Ur={},Ac;function Yp(){if(Ac)return Ur;Ac=1,Object.defineProperty(Ur,"__esModule",{value:!0}),Ur.parse=p,Ur.serialize=y;const o=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,l=/^[\u0021-\u003A\u003C-\u007E]*$/,i=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,c=/^[\u0020-\u003A\u003D-\u007E]*$/,d=Object.prototype.toString,f=(()=>{const w=function(){};return w.prototype=Object.create(null),w})();function p(w,D){const T=new f,j=w.length;if(j<2)return T;const R=(D==null?void 0:D.decode)||C;let b=0;do{const _=w.indexOf("=",b);if(_===-1)break;const W=w.indexOf(";",b),V=W===-1?j:W;if(_>V){b=w.lastIndexOf(";",_-1)+1;continue}const P=g(w,b,_),S=x(w,_,P),M=w.slice(P,S);if(T[M]===void 0){let F=g(w,_+1,V),H=x(w,V,F);const J=R(w.slice(F,H));T[M]=J}b=V+1}while(b<j);return T}function g(w,D,T){do{const j=w.charCodeAt(D);if(j!==32&&j!==9)return D}while(++D<T);return T}function x(w,D,T){for(;D>T;){const j=w.charCodeAt(--D);if(j!==32&&j!==9)return D+1}return T}function y(w,D,T){const j=(T==null?void 0:T.encode)||encodeURIComponent;if(!o.test(w))throw new TypeError(`argument name is invalid: ${w}`);const R=j(D);if(!l.test(R))throw new TypeError(`argument val is invalid: ${D}`);let b=w+"="+R;if(!T)return b;if(T.maxAge!==void 0){if(!Number.isInteger(T.maxAge))throw new TypeError(`option maxAge is invalid: ${T.maxAge}`);b+="; Max-Age="+T.maxAge}if(T.domain){if(!i.test(T.domain))throw new TypeError(`option domain is invalid: ${T.domain}`);b+="; Domain="+T.domain}if(T.path){if(!c.test(T.path))throw new TypeError(`option path is invalid: ${T.path}`);b+="; Path="+T.path}if(T.expires){if(!v(T.expires)||!Number.isFinite(T.expires.valueOf()))throw new TypeError(`option expires is invalid: ${T.expires}`);b+="; Expires="+T.expires.toUTCString()}if(T.httpOnly&&(b+="; HttpOnly"),T.secure&&(b+="; Secure"),T.partitioned&&(b+="; Partitioned"),T.priority)switch(typeof T.priority=="string"?T.priority.toLowerCase():void 0){case"low":b+="; Priority=Low";break;case"medium":b+="; Priority=Medium";break;case"high":b+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${T.priority}`)}if(T.sameSite)switch(typeof T.sameSite=="string"?T.sameSite.toLowerCase():T.sameSite){case!0:case"strict":b+="; SameSite=Strict";break;case"lax":b+="; SameSite=Lax";break;case"none":b+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${T.sameSite}`)}return b}function C(w){if(w.indexOf("%")===-1)return w;try{return decodeURIComponent(w)}catch{return w}}function v(w){return d.call(w)==="[object Date]"}return Ur}Yp();var $c="popstate";function Xp(o={}){function l(c,d){let{pathname:f,search:p,hash:g}=c.location;return Sa("",{pathname:f,search:p,hash:g},d.state&&d.state.usr||null,d.state&&d.state.key||"default")}function i(c,d){return typeof d=="string"?d:Wr(d)}return Zp(l,i,null,o)}function Me(o,l){if(o===!1||o===null||typeof o>"u")throw new Error(l)}function Mt(o,l){if(!o){typeof console<"u"&&console.warn(l);try{throw new Error(l)}catch{}}}function Jp(){return Math.random().toString(36).substring(2,10)}function Uc(o,l){return{usr:o.state,key:o.key,idx:l}}function Sa(o,l,i=null,c){return{pathname:typeof o=="string"?o:o.pathname,search:"",hash:"",...typeof l=="string"?Zn(l):l,state:i,key:l&&l.key||c||Jp()}}function Wr({pathname:o="/",search:l="",hash:i=""}){return l&&l!=="?"&&(o+=l.charAt(0)==="?"?l:"?"+l),i&&i!=="#"&&(o+=i.charAt(0)==="#"?i:"#"+i),o}function Zn(o){let l={};if(o){let i=o.indexOf("#");i>=0&&(l.hash=o.substring(i),o=o.substring(0,i));let c=o.indexOf("?");c>=0&&(l.search=o.substring(c),o=o.substring(0,c)),o&&(l.pathname=o)}return l}function Zp(o,l,i,c={}){let{window:d=document.defaultView,v5Compat:f=!1}=c,p=d.history,g="POP",x=null,y=C();y==null&&(y=0,p.replaceState({...p.state,idx:y},""));function C(){return(p.state||{idx:null}).idx}function v(){g="POP";let R=C(),b=R==null?null:R-y;y=R,x&&x({action:g,location:j.location,delta:b})}function w(R,b){g="PUSH";let _=Sa(j.location,R,b);y=C()+1;let W=Uc(_,y),V=j.createHref(_);try{p.pushState(W,"",V)}catch(P){if(P instanceof DOMException&&P.name==="DataCloneError")throw P;d.location.assign(V)}f&&x&&x({action:g,location:j.location,delta:1})}function D(R,b){g="REPLACE";let _=Sa(j.location,R,b);y=C();let W=Uc(_,y),V=j.createHref(_);p.replaceState(W,"",V),f&&x&&x({action:g,location:j.location,delta:0})}function T(R){return em(R)}let j={get action(){return g},get location(){return o(d,p)},listen(R){if(x)throw new Error("A history only accepts one active listener");return d.addEventListener($c,v),x=R,()=>{d.removeEventListener($c,v),x=null}},createHref(R){return l(d,R)},createURL:T,encodeLocation(R){let b=T(R);return{pathname:b.pathname,search:b.search,hash:b.hash}},push:w,replace:D,go(R){return p.go(R)}};return j}function em(o,l=!1){let i="http://localhost";typeof window<"u"&&(i=window.location.origin!=="null"?window.location.origin:window.location.href),Me(i,"No window.location.(origin|href) available to create URL");let c=typeof o=="string"?o:Wr(o);return c=c.replace(/ $/,"%20"),!l&&c.startsWith("//")&&(c=i+c),new URL(c,i)}function Jc(o,l,i="/"){return tm(o,l,i,!1)}function tm(o,l,i,c){let d=typeof l=="string"?Zn(l):l,f=Wt(d.pathname||"/",i);if(f==null)return null;let p=Zc(o);nm(p);let g=null;for(let x=0;g==null&&x<p.length;++x){let y=pm(f);g=dm(p[x],y,c)}return g}function Zc(o,l=[],i=[],c=""){let d=(f,p,g)=>{let x={relativePath:g===void 0?f.path||"":g,caseSensitive:f.caseSensitive===!0,childrenIndex:p,route:f};x.relativePath.startsWith("/")&&(Me(x.relativePath.startsWith(c),`Absolute route path "${x.relativePath}" nested under path "${c}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),x.relativePath=x.relativePath.slice(c.length));let y=Vt([c,x.relativePath]),C=i.concat(x);f.children&&f.children.length>0&&(Me(f.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${y}".`),Zc(f.children,l,C,y)),!(f.path==null&&!f.index)&&l.push({path:y,score:um(y,f.index),routesMeta:C})};return o.forEach((f,p)=>{var g;if(f.path===""||!((g=f.path)!=null&&g.includes("?")))d(f,p);else for(let x of ed(f.path))d(f,p,x)}),l}function ed(o){let l=o.split("/");if(l.length===0)return[];let[i,...c]=l,d=i.endsWith("?"),f=i.replace(/\?$/,"");if(c.length===0)return d?[f,""]:[f];let p=ed(c.join("/")),g=[];return g.push(...p.map(x=>x===""?f:[f,x].join("/"))),d&&g.push(...p),g.map(x=>o.startsWith("/")&&x===""?"/":x)}function nm(o){o.sort((l,i)=>l.score!==i.score?i.score-l.score:cm(l.routesMeta.map(c=>c.childrenIndex),i.routesMeta.map(c=>c.childrenIndex)))}var rm=/^:[\w-]+$/,om=3,lm=2,im=1,am=10,sm=-2,Bc=o=>o==="*";function um(o,l){let i=o.split("/"),c=i.length;return i.some(Bc)&&(c+=sm),l&&(c+=lm),i.filter(d=>!Bc(d)).reduce((d,f)=>d+(rm.test(f)?om:f===""?im:am),c)}function cm(o,l){return o.length===l.length&&o.slice(0,-1).every((c,d)=>c===l[d])?o[o.length-1]-l[l.length-1]:0}function dm(o,l,i=!1){let{routesMeta:c}=o,d={},f="/",p=[];for(let g=0;g<c.length;++g){let x=c[g],y=g===c.length-1,C=f==="/"?l:l.slice(f.length)||"/",v=fl({path:x.relativePath,caseSensitive:x.caseSensitive,end:y},C),w=x.route;if(!v&&y&&i&&!c[c.length-1].route.index&&(v=fl({path:x.relativePath,caseSensitive:x.caseSensitive,end:!1},C)),!v)return null;Object.assign(d,v.params),p.push({params:d,pathname:Vt([f,v.pathname]),pathnameBase:xm(Vt([f,v.pathnameBase])),route:w}),v.pathnameBase!=="/"&&(f=Vt([f,v.pathnameBase]))}return p}function fl(o,l){typeof o=="string"&&(o={path:o,caseSensitive:!1,end:!0});let[i,c]=fm(o.path,o.caseSensitive,o.end),d=l.match(i);if(!d)return null;let f=d[0],p=f.replace(/(.)\/+$/,"$1"),g=d.slice(1);return{params:c.reduce((y,{paramName:C,isOptional:v},w)=>{if(C==="*"){let T=g[w]||"";p=f.slice(0,f.length-T.length).replace(/(.)\/+$/,"$1")}const D=g[w];return v&&!D?y[C]=void 0:y[C]=(D||"").replace(/%2F/g,"/"),y},{}),pathname:f,pathnameBase:p,pattern:o}}function fm(o,l=!1,i=!0){Mt(o==="*"||!o.endsWith("*")||o.endsWith("/*"),`Route path "${o}" will be treated as if it were "${o.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${o.replace(/\*$/,"/*")}".`);let c=[],d="^"+o.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(p,g,x)=>(c.push({paramName:g,isOptional:x!=null}),x?"/?([^\\/]+)?":"/([^\\/]+)"));return o.endsWith("*")?(c.push({paramName:"*"}),d+=o==="*"||o==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):i?d+="\\/*$":o!==""&&o!=="/"&&(d+="(?:(?=\\/|$))"),[new RegExp(d,l?void 0:"i"),c]}function pm(o){try{return o.split("/").map(l=>decodeURIComponent(l).replace(/\//g,"%2F")).join("/")}catch(l){return Mt(!1,`The URL path "${o}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${l}).`),o}}function Wt(o,l){if(l==="/")return o;if(!o.toLowerCase().startsWith(l.toLowerCase()))return null;let i=l.endsWith("/")?l.length-1:l.length,c=o.charAt(i);return c&&c!=="/"?null:o.slice(i)||"/"}function mm(o,l="/"){let{pathname:i,search:c="",hash:d=""}=typeof o=="string"?Zn(o):o;return{pathname:i?i.startsWith("/")?i:hm(i,l):l,search:vm(c),hash:ym(d)}}function hm(o,l){let i=l.replace(/\/+$/,"").split("/");return o.split("/").forEach(d=>{d===".."?i.length>1&&i.pop():d!=="."&&i.push(d)}),i.length>1?i.join("/"):"/"}function ma(o,l,i,c){return`Cannot include a '${o}' character in a manually specified \`to.${l}\` field [${JSON.stringify(c)}].  Please separate it out to the \`to.${i}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function gm(o){return o.filter((l,i)=>i===0||l.route.path&&l.route.path.length>0)}function td(o){let l=gm(o);return l.map((i,c)=>c===l.length-1?i.pathname:i.pathnameBase)}function nd(o,l,i,c=!1){let d;typeof o=="string"?d=Zn(o):(d={...o},Me(!d.pathname||!d.pathname.includes("?"),ma("?","pathname","search",d)),Me(!d.pathname||!d.pathname.includes("#"),ma("#","pathname","hash",d)),Me(!d.search||!d.search.includes("#"),ma("#","search","hash",d)));let f=o===""||d.pathname==="",p=f?"/":d.pathname,g;if(p==null)g=i;else{let v=l.length-1;if(!c&&p.startsWith("..")){let w=p.split("/");for(;w[0]==="..";)w.shift(),v-=1;d.pathname=w.join("/")}g=v>=0?l[v]:"/"}let x=mm(d,g),y=p&&p!=="/"&&p.endsWith("/"),C=(f||p===".")&&i.endsWith("/");return!x.pathname.endsWith("/")&&(y||C)&&(x.pathname+="/"),x}var Vt=o=>o.join("/").replace(/\/\/+/g,"/"),xm=o=>o.replace(/\/+$/,"").replace(/^\/*/,"/"),vm=o=>!o||o==="?"?"":o.startsWith("?")?o:"?"+o,ym=o=>!o||o==="#"?"":o.startsWith("#")?o:"#"+o;function wm(o){return o!=null&&typeof o.status=="number"&&typeof o.statusText=="string"&&typeof o.internal=="boolean"&&"data"in o}var rd=["POST","PUT","PATCH","DELETE"];new Set(rd);var Sm=["GET",...rd];new Set(Sm);var er=h.createContext(null);er.displayName="DataRouter";var pl=h.createContext(null);pl.displayName="DataRouterState";var od=h.createContext({isTransitioning:!1});od.displayName="ViewTransition";var km=h.createContext(new Map);km.displayName="Fetchers";var Cm=h.createContext(null);Cm.displayName="Await";var Pt=h.createContext(null);Pt.displayName="Navigation";var Hr=h.createContext(null);Hr.displayName="Location";var Ht=h.createContext({outlet:null,matches:[],isDataRoute:!1});Ht.displayName="Route";var Ea=h.createContext(null);Ea.displayName="RouteError";function bm(o,{relative:l}={}){Me(Qr(),"useHref() may be used only in the context of a <Router> component.");let{basename:i,navigator:c}=h.useContext(Pt),{hash:d,pathname:f,search:p}=Gr(o,{relative:l}),g=f;return i!=="/"&&(g=f==="/"?i:Vt([i,f])),c.createHref({pathname:g,search:p,hash:d})}function Qr(){return h.useContext(Hr)!=null}function pn(){return Me(Qr(),"useLocation() may be used only in the context of a <Router> component."),h.useContext(Hr).location}var ld="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function id(o){h.useContext(Pt).static||h.useLayoutEffect(o)}function ad(){let{isDataRoute:o}=h.useContext(Ht);return o?Fm():jm()}function jm(){Me(Qr(),"useNavigate() may be used only in the context of a <Router> component.");let o=h.useContext(er),{basename:l,navigator:i}=h.useContext(Pt),{matches:c}=h.useContext(Ht),{pathname:d}=pn(),f=JSON.stringify(td(c)),p=h.useRef(!1);return id(()=>{p.current=!0}),h.useCallback((x,y={})=>{if(Mt(p.current,ld),!p.current)return;if(typeof x=="number"){i.go(x);return}let C=nd(x,JSON.parse(f),d,y.relative==="path");o==null&&l!=="/"&&(C.pathname=C.pathname==="/"?l:Vt([l,C.pathname])),(y.replace?i.replace:i.push)(C,y.state,y)},[l,i,f,d,o])}h.createContext(null);function Gr(o,{relative:l}={}){let{matches:i}=h.useContext(Ht),{pathname:c}=pn(),d=JSON.stringify(td(i));return h.useMemo(()=>nd(o,JSON.parse(d),c,l==="path"),[o,d,c,l])}function Em(o,l){return sd(o,l)}function sd(o,l,i,c){var b;Me(Qr(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:d}=h.useContext(Pt),{matches:f}=h.useContext(Ht),p=f[f.length-1],g=p?p.params:{},x=p?p.pathname:"/",y=p?p.pathnameBase:"/",C=p&&p.route;{let _=C&&C.path||"";ud(x,!C||_.endsWith("*")||_.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${x}" (under <Route path="${_}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${_}"> to <Route path="${_==="/"?"*":`${_}/*`}">.`)}let v=pn(),w;if(l){let _=typeof l=="string"?Zn(l):l;Me(y==="/"||((b=_.pathname)==null?void 0:b.startsWith(y)),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${y}" but pathname "${_.pathname}" was given in the \`location\` prop.`),w=_}else w=v;let D=w.pathname||"/",T=D;if(y!=="/"){let _=y.replace(/^\//,"").split("/");T="/"+D.replace(/^\//,"").split("/").slice(_.length).join("/")}let j=Jc(o,{pathname:T});Mt(C||j!=null,`No routes matched location "${w.pathname}${w.search}${w.hash}" `),Mt(j==null||j[j.length-1].route.element!==void 0||j[j.length-1].route.Component!==void 0||j[j.length-1].route.lazy!==void 0,`Matched leaf route at location "${w.pathname}${w.search}${w.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let R=Mm(j&&j.map(_=>Object.assign({},_,{params:Object.assign({},g,_.params),pathname:Vt([y,d.encodeLocation?d.encodeLocation(_.pathname).pathname:_.pathname]),pathnameBase:_.pathnameBase==="/"?y:Vt([y,d.encodeLocation?d.encodeLocation(_.pathnameBase).pathname:_.pathnameBase])})),f,i,c);return l&&R?h.createElement(Hr.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...w},navigationType:"POP"}},R):R}function Nm(){let o=zm(),l=wm(o)?`${o.status} ${o.statusText}`:o instanceof Error?o.message:JSON.stringify(o),i=o instanceof Error?o.stack:null,c="rgba(200,200,200, 0.5)",d={padding:"0.5rem",backgroundColor:c},f={padding:"2px 4px",backgroundColor:c},p=null;return console.error("Error handled by React Router default ErrorBoundary:",o),p=h.createElement(h.Fragment,null,h.createElement("p",null,"💿 Hey developer 👋"),h.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",h.createElement("code",{style:f},"ErrorBoundary")," or"," ",h.createElement("code",{style:f},"errorElement")," prop on your route.")),h.createElement(h.Fragment,null,h.createElement("h2",null,"Unexpected Application Error!"),h.createElement("h3",{style:{fontStyle:"italic"}},l),i?h.createElement("pre",{style:d},i):null,p)}var Tm=h.createElement(Nm,null),Rm=class extends h.Component{constructor(o){super(o),this.state={location:o.location,revalidation:o.revalidation,error:o.error}}static getDerivedStateFromError(o){return{error:o}}static getDerivedStateFromProps(o,l){return l.location!==o.location||l.revalidation!=="idle"&&o.revalidation==="idle"?{error:o.error,location:o.location,revalidation:o.revalidation}:{error:o.error!==void 0?o.error:l.error,location:l.location,revalidation:o.revalidation||l.revalidation}}componentDidCatch(o,l){console.error("React Router caught the following error during render",o,l)}render(){return this.state.error!==void 0?h.createElement(Ht.Provider,{value:this.props.routeContext},h.createElement(Ea.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function Im({routeContext:o,match:l,children:i}){let c=h.useContext(er);return c&&c.static&&c.staticContext&&(l.route.errorElement||l.route.ErrorBoundary)&&(c.staticContext._deepestRenderedBoundaryId=l.route.id),h.createElement(Ht.Provider,{value:o},i)}function Mm(o,l=[],i=null,c=null){if(o==null){if(!i)return null;if(i.errors)o=i.matches;else if(l.length===0&&!i.initialized&&i.matches.length>0)o=i.matches;else return null}let d=o,f=i==null?void 0:i.errors;if(f!=null){let x=d.findIndex(y=>y.route.id&&(f==null?void 0:f[y.route.id])!==void 0);Me(x>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(f).join(",")}`),d=d.slice(0,Math.min(d.length,x+1))}let p=!1,g=-1;if(i)for(let x=0;x<d.length;x++){let y=d[x];if((y.route.HydrateFallback||y.route.hydrateFallbackElement)&&(g=x),y.route.id){let{loaderData:C,errors:v}=i,w=y.route.loader&&!C.hasOwnProperty(y.route.id)&&(!v||v[y.route.id]===void 0);if(y.route.lazy||w){p=!0,g>=0?d=d.slice(0,g+1):d=[d[0]];break}}}return d.reduceRight((x,y,C)=>{let v,w=!1,D=null,T=null;i&&(v=f&&y.route.id?f[y.route.id]:void 0,D=y.route.errorElement||Tm,p&&(g<0&&C===0?(ud("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),w=!0,T=null):g===C&&(w=!0,T=y.route.hydrateFallbackElement||null)));let j=l.concat(d.slice(0,C+1)),R=()=>{let b;return v?b=D:w?b=T:y.route.Component?b=h.createElement(y.route.Component,null):y.route.element?b=y.route.element:b=x,h.createElement(Im,{match:y,routeContext:{outlet:x,matches:j,isDataRoute:i!=null},children:b})};return i&&(y.route.ErrorBoundary||y.route.errorElement||C===0)?h.createElement(Rm,{location:i.location,revalidation:i.revalidation,component:D,error:v,children:R(),routeContext:{outlet:null,matches:j,isDataRoute:!0}}):R()},null)}function Na(o){return`${o} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Pm(o){let l=h.useContext(er);return Me(l,Na(o)),l}function Dm(o){let l=h.useContext(pl);return Me(l,Na(o)),l}function _m(o){let l=h.useContext(Ht);return Me(l,Na(o)),l}function Ta(o){let l=_m(o),i=l.matches[l.matches.length-1];return Me(i.route.id,`${o} can only be used on routes that contain a unique "id"`),i.route.id}function Lm(){return Ta("useRouteId")}function zm(){var c;let o=h.useContext(Ea),l=Dm("useRouteError"),i=Ta("useRouteError");return o!==void 0?o:(c=l.errors)==null?void 0:c[i]}function Fm(){let{router:o}=Pm("useNavigate"),l=Ta("useNavigate"),i=h.useRef(!1);return id(()=>{i.current=!0}),h.useCallback(async(d,f={})=>{Mt(i.current,ld),i.current&&(typeof d=="number"?o.navigate(d):await o.navigate(d,{fromRouteId:l,...f}))},[o,l])}var Vc={};function ud(o,l,i){!l&&!Vc[o]&&(Vc[o]=!0,Mt(!1,i))}h.memo(Om);function Om({routes:o,future:l,state:i}){return sd(o,void 0,i,l)}function ul(o){Me(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function Am({basename:o="/",children:l=null,location:i,navigationType:c="POP",navigator:d,static:f=!1}){Me(!Qr(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let p=o.replace(/^\/*/,"/"),g=h.useMemo(()=>({basename:p,navigator:d,static:f,future:{}}),[p,d,f]);typeof i=="string"&&(i=Zn(i));let{pathname:x="/",search:y="",hash:C="",state:v=null,key:w="default"}=i,D=h.useMemo(()=>{let T=Wt(x,p);return T==null?null:{location:{pathname:T,search:y,hash:C,state:v,key:w},navigationType:c}},[p,x,y,C,v,w,c]);return Mt(D!=null,`<Router basename="${p}"> is not able to match the URL "${x}${y}${C}" because it does not start with the basename, so the <Router> won't render anything.`),D==null?null:h.createElement(Pt.Provider,{value:g},h.createElement(Hr.Provider,{children:l,value:D}))}function $m({children:o,location:l}){return Em(ka(o),l)}function ka(o,l=[]){let i=[];return h.Children.forEach(o,(c,d)=>{if(!h.isValidElement(c))return;let f=[...l,d];if(c.type===h.Fragment){i.push.apply(i,ka(c.props.children,f));return}Me(c.type===ul,`[${typeof c.type=="string"?c.type:c.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),Me(!c.props.index||!c.props.children,"An index route cannot have child routes.");let p={id:c.props.id||f.join("-"),caseSensitive:c.props.caseSensitive,element:c.props.element,Component:c.props.Component,index:c.props.index,path:c.props.path,loader:c.props.loader,action:c.props.action,hydrateFallbackElement:c.props.hydrateFallbackElement,HydrateFallback:c.props.HydrateFallback,errorElement:c.props.errorElement,ErrorBoundary:c.props.ErrorBoundary,hasErrorBoundary:c.props.hasErrorBoundary===!0||c.props.ErrorBoundary!=null||c.props.errorElement!=null,shouldRevalidate:c.props.shouldRevalidate,handle:c.props.handle,lazy:c.props.lazy};c.props.children&&(p.children=ka(c.props.children,f)),i.push(p)}),i}var cl="get",dl="application/x-www-form-urlencoded";function ml(o){return o!=null&&typeof o.tagName=="string"}function Um(o){return ml(o)&&o.tagName.toLowerCase()==="button"}function Bm(o){return ml(o)&&o.tagName.toLowerCase()==="form"}function Vm(o){return ml(o)&&o.tagName.toLowerCase()==="input"}function Wm(o){return!!(o.metaKey||o.altKey||o.ctrlKey||o.shiftKey)}function Hm(o,l){return o.button===0&&(!l||l==="_self")&&!Wm(o)}var sl=null;function Qm(){if(sl===null)try{new FormData(document.createElement("form"),0),sl=!1}catch{sl=!0}return sl}var Gm=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function ha(o){return o!=null&&!Gm.has(o)?(Mt(!1,`"${o}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${dl}"`),null):o}function Km(o,l){let i,c,d,f,p;if(Bm(o)){let g=o.getAttribute("action");c=g?Wt(g,l):null,i=o.getAttribute("method")||cl,d=ha(o.getAttribute("enctype"))||dl,f=new FormData(o)}else if(Um(o)||Vm(o)&&(o.type==="submit"||o.type==="image")){let g=o.form;if(g==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let x=o.getAttribute("formaction")||g.getAttribute("action");if(c=x?Wt(x,l):null,i=o.getAttribute("formmethod")||g.getAttribute("method")||cl,d=ha(o.getAttribute("formenctype"))||ha(g.getAttribute("enctype"))||dl,f=new FormData(g,o),!Qm()){let{name:y,type:C,value:v}=o;if(C==="image"){let w=y?`${y}.`:"";f.append(`${w}x`,"0"),f.append(`${w}y`,"0")}else y&&f.append(y,v)}}else{if(ml(o))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');i=cl,c=null,d=dl,p=o}return f&&d==="text/plain"&&(p=f,f=void 0),{action:c,method:i.toLowerCase(),encType:d,formData:f,body:p}}function Ra(o,l){if(o===!1||o===null||typeof o>"u")throw new Error(l)}async function qm(o,l){if(o.id in l)return l[o.id];try{let i=await import(o.module);return l[o.id]=i,i}catch(i){return console.error(`Error loading route module \`${o.module}\`, reloading page...`),console.error(i),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function Ym(o){return o==null?!1:o.href==null?o.rel==="preload"&&typeof o.imageSrcSet=="string"&&typeof o.imageSizes=="string":typeof o.rel=="string"&&typeof o.href=="string"}async function Xm(o,l,i){let c=await Promise.all(o.map(async d=>{let f=l.routes[d.route.id];if(f){let p=await qm(f,i);return p.links?p.links():[]}return[]}));return th(c.flat(1).filter(Ym).filter(d=>d.rel==="stylesheet"||d.rel==="preload").map(d=>d.rel==="stylesheet"?{...d,rel:"prefetch",as:"style"}:{...d,rel:"prefetch"}))}function Wc(o,l,i,c,d,f){let p=(x,y)=>i[y]?x.route.id!==i[y].route.id:!0,g=(x,y)=>{var C;return i[y].pathname!==x.pathname||((C=i[y].route.path)==null?void 0:C.endsWith("*"))&&i[y].params["*"]!==x.params["*"]};return f==="assets"?l.filter((x,y)=>p(x,y)||g(x,y)):f==="data"?l.filter((x,y)=>{var v;let C=c.routes[x.route.id];if(!C||!C.hasLoader)return!1;if(p(x,y)||g(x,y))return!0;if(x.route.shouldRevalidate){let w=x.route.shouldRevalidate({currentUrl:new URL(d.pathname+d.search+d.hash,window.origin),currentParams:((v=i[0])==null?void 0:v.params)||{},nextUrl:new URL(o,window.origin),nextParams:x.params,defaultShouldRevalidate:!0});if(typeof w=="boolean")return w}return!0}):[]}function Jm(o,l,{includeHydrateFallback:i}={}){return Zm(o.map(c=>{let d=l.routes[c.route.id];if(!d)return[];let f=[d.module];return d.clientActionModule&&(f=f.concat(d.clientActionModule)),d.clientLoaderModule&&(f=f.concat(d.clientLoaderModule)),i&&d.hydrateFallbackModule&&(f=f.concat(d.hydrateFallbackModule)),d.imports&&(f=f.concat(d.imports)),f}).flat(1))}function Zm(o){return[...new Set(o)]}function eh(o){let l={},i=Object.keys(o).sort();for(let c of i)l[c]=o[c];return l}function th(o,l){let i=new Set;return new Set(l),o.reduce((c,d)=>{let f=JSON.stringify(eh(d));return i.has(f)||(i.add(f),c.push({key:f,link:d})),c},[])}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");var nh=new Set([100,101,204,205]);function rh(o,l){let i=typeof o=="string"?new URL(o,typeof window>"u"?"server://singlefetch/":window.location.origin):o;return i.pathname==="/"?i.pathname="_root.data":l&&Wt(i.pathname,l)==="/"?i.pathname=`${l.replace(/\/$/,"")}/_root.data`:i.pathname=`${i.pathname.replace(/\/$/,"")}.data`,i}function cd(){let o=h.useContext(er);return Ra(o,"You must render this element inside a <DataRouterContext.Provider> element"),o}function oh(){let o=h.useContext(pl);return Ra(o,"You must render this element inside a <DataRouterStateContext.Provider> element"),o}var Ia=h.createContext(void 0);Ia.displayName="FrameworkContext";function dd(){let o=h.useContext(Ia);return Ra(o,"You must render this element inside a <HydratedRouter> element"),o}function lh(o,l){let i=h.useContext(Ia),[c,d]=h.useState(!1),[f,p]=h.useState(!1),{onFocus:g,onBlur:x,onMouseEnter:y,onMouseLeave:C,onTouchStart:v}=l,w=h.useRef(null);h.useEffect(()=>{if(o==="render"&&p(!0),o==="viewport"){let j=b=>{b.forEach(_=>{p(_.isIntersecting)})},R=new IntersectionObserver(j,{threshold:.5});return w.current&&R.observe(w.current),()=>{R.disconnect()}}},[o]),h.useEffect(()=>{if(c){let j=setTimeout(()=>{p(!0)},100);return()=>{clearTimeout(j)}}},[c]);let D=()=>{d(!0)},T=()=>{d(!1),p(!1)};return i?o!=="intent"?[f,w,{}]:[f,w,{onFocus:Br(g,D),onBlur:Br(x,T),onMouseEnter:Br(y,D),onMouseLeave:Br(C,T),onTouchStart:Br(v,D)}]:[!1,w,{}]}function Br(o,l){return i=>{o&&o(i),i.defaultPrevented||l(i)}}function ih({page:o,...l}){let{router:i}=cd(),c=h.useMemo(()=>Jc(i.routes,o,i.basename),[i.routes,o,i.basename]);return c?h.createElement(sh,{page:o,matches:c,...l}):null}function ah(o){let{manifest:l,routeModules:i}=dd(),[c,d]=h.useState([]);return h.useEffect(()=>{let f=!1;return Xm(o,l,i).then(p=>{f||d(p)}),()=>{f=!0}},[o,l,i]),c}function sh({page:o,matches:l,...i}){let c=pn(),{manifest:d,routeModules:f}=dd(),{basename:p}=cd(),{loaderData:g,matches:x}=oh(),y=h.useMemo(()=>Wc(o,l,x,d,c,"data"),[o,l,x,d,c]),C=h.useMemo(()=>Wc(o,l,x,d,c,"assets"),[o,l,x,d,c]),v=h.useMemo(()=>{if(o===c.pathname+c.search+c.hash)return[];let T=new Set,j=!1;if(l.forEach(b=>{var W;let _=d.routes[b.route.id];!_||!_.hasLoader||(!y.some(V=>V.route.id===b.route.id)&&b.route.id in g&&((W=f[b.route.id])!=null&&W.shouldRevalidate)||_.hasClientLoader?j=!0:T.add(b.route.id))}),T.size===0)return[];let R=rh(o,p);return j&&T.size>0&&R.searchParams.set("_routes",l.filter(b=>T.has(b.route.id)).map(b=>b.route.id).join(",")),[R.pathname+R.search]},[p,g,c,d,y,l,o,f]),w=h.useMemo(()=>Jm(C,d),[C,d]),D=ah(C);return h.createElement(h.Fragment,null,v.map(T=>h.createElement("link",{key:T,rel:"prefetch",as:"fetch",href:T,...i})),w.map(T=>h.createElement("link",{key:T,rel:"modulepreload",href:T,...i})),D.map(({key:T,link:j})=>h.createElement("link",{key:T,...j})))}function uh(...o){return l=>{o.forEach(i=>{typeof i=="function"?i(l):i!=null&&(i.current=l)})}}var fd=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{fd&&(window.__reactRouterVersion="7.6.3")}catch{}function ch({basename:o,children:l,window:i}){let c=h.useRef();c.current==null&&(c.current=Xp({window:i,v5Compat:!0}));let d=c.current,[f,p]=h.useState({action:d.action,location:d.location}),g=h.useCallback(x=>{h.startTransition(()=>p(x))},[p]);return h.useLayoutEffect(()=>d.listen(g),[d,g]),h.createElement(Am,{basename:o,children:l,location:f.location,navigationType:f.action,navigator:d})}var pd=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Vr=h.forwardRef(function({onClick:l,discover:i="render",prefetch:c="none",relative:d,reloadDocument:f,replace:p,state:g,target:x,to:y,preventScrollReset:C,viewTransition:v,...w},D){let{basename:T}=h.useContext(Pt),j=typeof y=="string"&&pd.test(y),R,b=!1;if(typeof y=="string"&&j&&(R=y,fd))try{let H=new URL(window.location.href),J=y.startsWith("//")?new URL(H.protocol+y):new URL(y),te=Wt(J.pathname,T);J.origin===H.origin&&te!=null?y=te+J.search+J.hash:b=!0}catch{Mt(!1,`<Link to="${y}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let _=bm(y,{relative:d}),[W,V,P]=lh(c,w),S=mh(y,{replace:p,state:g,target:x,preventScrollReset:C,relative:d,viewTransition:v});function M(H){l&&l(H),H.defaultPrevented||S(H)}let F=h.createElement("a",{...w,...P,href:R||_,onClick:b||f?l:M,ref:uh(D,V),target:x,"data-discover":!j&&i==="render"?"true":void 0});return W&&!j?h.createElement(h.Fragment,null,F,h.createElement(ih,{page:_})):F});Vr.displayName="Link";var dh=h.forwardRef(function({"aria-current":l="page",caseSensitive:i=!1,className:c="",end:d=!1,style:f,to:p,viewTransition:g,children:x,...y},C){let v=Gr(p,{relative:y.relative}),w=pn(),D=h.useContext(pl),{navigator:T,basename:j}=h.useContext(Pt),R=D!=null&&yh(v)&&g===!0,b=T.encodeLocation?T.encodeLocation(v).pathname:v.pathname,_=w.pathname,W=D&&D.navigation&&D.navigation.location?D.navigation.location.pathname:null;i||(_=_.toLowerCase(),W=W?W.toLowerCase():null,b=b.toLowerCase()),W&&j&&(W=Wt(W,j)||W);const V=b!=="/"&&b.endsWith("/")?b.length-1:b.length;let P=_===b||!d&&_.startsWith(b)&&_.charAt(V)==="/",S=W!=null&&(W===b||!d&&W.startsWith(b)&&W.charAt(b.length)==="/"),M={isActive:P,isPending:S,isTransitioning:R},F=P?l:void 0,H;typeof c=="function"?H=c(M):H=[c,P?"active":null,S?"pending":null,R?"transitioning":null].filter(Boolean).join(" ");let J=typeof f=="function"?f(M):f;return h.createElement(Vr,{...y,"aria-current":F,className:H,ref:C,style:J,to:p,viewTransition:g},typeof x=="function"?x(M):x)});dh.displayName="NavLink";var fh=h.forwardRef(({discover:o="render",fetcherKey:l,navigate:i,reloadDocument:c,replace:d,state:f,method:p=cl,action:g,onSubmit:x,relative:y,preventScrollReset:C,viewTransition:v,...w},D)=>{let T=xh(),j=vh(g,{relative:y}),R=p.toLowerCase()==="get"?"get":"post",b=typeof g=="string"&&pd.test(g),_=W=>{if(x&&x(W),W.defaultPrevented)return;W.preventDefault();let V=W.nativeEvent.submitter,P=(V==null?void 0:V.getAttribute("formmethod"))||p;T(V||W.currentTarget,{fetcherKey:l,method:P,navigate:i,replace:d,state:f,relative:y,preventScrollReset:C,viewTransition:v})};return h.createElement("form",{ref:D,method:R,action:j,onSubmit:c?x:_,...w,"data-discover":!b&&o==="render"?"true":void 0})});fh.displayName="Form";function ph(o){return`${o} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function md(o){let l=h.useContext(er);return Me(l,ph(o)),l}function mh(o,{target:l,replace:i,state:c,preventScrollReset:d,relative:f,viewTransition:p}={}){let g=ad(),x=pn(),y=Gr(o,{relative:f});return h.useCallback(C=>{if(Hm(C,l)){C.preventDefault();let v=i!==void 0?i:Wr(x)===Wr(y);g(o,{replace:v,state:c,preventScrollReset:d,relative:f,viewTransition:p})}},[x,g,y,i,c,l,o,d,f,p])}var hh=0,gh=()=>`__${String(++hh)}__`;function xh(){let{router:o}=md("useSubmit"),{basename:l}=h.useContext(Pt),i=Lm();return h.useCallback(async(c,d={})=>{let{action:f,method:p,encType:g,formData:x,body:y}=Km(c,l);if(d.navigate===!1){let C=d.fetcherKey||gh();await o.fetch(C,i,d.action||f,{preventScrollReset:d.preventScrollReset,formData:x,body:y,formMethod:d.method||p,formEncType:d.encType||g,flushSync:d.flushSync})}else await o.navigate(d.action||f,{preventScrollReset:d.preventScrollReset,formData:x,body:y,formMethod:d.method||p,formEncType:d.encType||g,replace:d.replace,state:d.state,fromRouteId:i,flushSync:d.flushSync,viewTransition:d.viewTransition})},[o,l,i])}function vh(o,{relative:l}={}){let{basename:i}=h.useContext(Pt),c=h.useContext(Ht);Me(c,"useFormAction must be used inside a RouteContext");let[d]=c.matches.slice(-1),f={...Gr(o||".",{relative:l})},p=pn();if(o==null){f.search=p.search;let g=new URLSearchParams(f.search),x=g.getAll("index");if(x.some(C=>C==="")){g.delete("index"),x.filter(v=>v).forEach(v=>g.append("index",v));let C=g.toString();f.search=C?`?${C}`:""}}return(!o||o===".")&&d.route.index&&(f.search=f.search?f.search.replace(/^\?/,"?index&"):"?index"),i!=="/"&&(f.pathname=f.pathname==="/"?i:Vt([i,f.pathname])),Wr(f)}function yh(o,l={}){let i=h.useContext(od);Me(i!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:c}=md("useViewTransitionState"),d=Gr(o,{relative:l.relative});if(!i.isTransitioning)return!1;let f=Wt(i.currentLocation.pathname,c)||i.currentLocation.pathname,p=Wt(i.nextLocation.pathname,c)||i.nextLocation.pathname;return fl(d.pathname,p)!=null||fl(d.pathname,f)!=null}[...nh];const Ma=({onOpenAPIKeyModal:o,currentMode:l="home",projectName:i})=>s.jsxs("header",{style:{height:"70px",backgroundColor:"#1a1a1b",borderBottom:"1px solid #343536",display:"flex",alignItems:"center",padding:"0 20px",flexShrink:0,position:"sticky",top:0,zIndex:100},children:[s.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"16px"},children:[s.jsxs(Vr,{to:"/",style:{textDecoration:"none",display:"flex",alignItems:"center",gap:"12px",padding:"8px 12px",borderRadius:"8px",transition:"all 0.2s ease"},onMouseEnter:c=>{c.currentTarget.style.backgroundColor="rgba(236, 72, 153, 0.1)"},onMouseLeave:c=>{c.currentTarget.style.backgroundColor="transparent"},children:[s.jsx("div",{style:{width:"40px",height:"40px",backgroundColor:"rgba(236, 72, 153, 0.15)",border:"2px solid #ec4899",borderRadius:"8px",display:"flex",alignItems:"center",justifyContent:"center"},children:s.jsx("svg",{style:{width:"20px",height:"20px",color:"#ec4899"},fill:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{d:"M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"})})}),s.jsxs("div",{children:[s.jsx("h1",{style:{fontSize:"18px",fontWeight:"bold",color:"white",margin:0,fontFamily:'"Space Mono", monospace'},children:"AnimaGen"}),s.jsx("p",{style:{fontSize:"10px",color:"#9ca3af",margin:0,fontFamily:'"Space Mono", monospace'},children:"Home"})]})]}),i&&s.jsxs(s.Fragment,{children:[s.jsx("span",{style:{color:"#666666",fontSize:"16px"},children:"→"}),s.jsx("div",{style:{padding:"4px 8px",backgroundColor:"rgba(59, 130, 246, 0.1)",borderRadius:"4px",border:"1px solid rgba(59, 130, 246, 0.3)"},children:s.jsx("span",{style:{fontSize:"14px",color:"#3b82f6",fontFamily:'"Space Mono", monospace',fontWeight:"bold"},children:i})})]})]}),l!=="home"&&s.jsxs("nav",{style:{display:"flex",alignItems:"center",gap:"8px",marginLeft:"auto",marginRight:"auto"},children:[s.jsx(Vr,{to:"/slideshow",style:{textDecoration:"none",padding:"8px 16px",borderRadius:"6px",fontSize:"14px",fontFamily:'"Space Mono", monospace',fontWeight:"bold",transition:"all 0.2s ease",backgroundColor:l==="slideshow"?"rgba(236, 72, 153, 0.2)":"transparent",color:l==="slideshow"?"#ec4899":"#9ca3af",border:l==="slideshow"?"1px solid #ec4899":"1px solid transparent"},onMouseEnter:c=>{l!=="slideshow"&&(c.currentTarget.style.backgroundColor="rgba(236, 72, 153, 0.1)",c.currentTarget.style.color="#ec4899")},onMouseLeave:c=>{l!=="slideshow"&&(c.currentTarget.style.backgroundColor="transparent",c.currentTarget.style.color="#9ca3af")},children:"🖼️ SlideShow"}),s.jsx(Vr,{to:"/video-editor",style:{textDecoration:"none",padding:"8px 16px",borderRadius:"6px",fontSize:"14px",fontFamily:'"Space Mono", monospace',fontWeight:"bold",transition:"all 0.2s ease",backgroundColor:l==="video-editor"?"rgba(59, 130, 246, 0.2)":"transparent",color:l==="video-editor"?"#3b82f6":"#9ca3af",border:l==="video-editor"?"1px solid #3b82f6":"1px solid transparent"},onMouseEnter:c=>{l!=="video-editor"&&(c.currentTarget.style.backgroundColor="rgba(59, 130, 246, 0.1)",c.currentTarget.style.color="#3b82f6")},onMouseLeave:c=>{l!=="video-editor"&&(c.currentTarget.style.backgroundColor="transparent",c.currentTarget.style.color="#9ca3af")},children:"🎬 Video Editor"})]}),s.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"16px",marginLeft:l==="home"?"auto":"0"},children:[o&&s.jsxs("button",{onClick:o,style:{display:"flex",alignItems:"center",gap:"6px",padding:"8px 12px",backgroundColor:"rgba(236, 72, 153, 0.1)",border:"1px solid rgba(236, 72, 153, 0.3)",borderRadius:"6px",color:"#ec4899",fontSize:"12px",fontFamily:'"Space Mono", monospace',fontWeight:"bold",cursor:"pointer",transition:"all 0.2s ease"},onMouseEnter:c=>{c.currentTarget.style.backgroundColor="rgba(236, 72, 153, 0.2)",c.currentTarget.style.borderColor="#ec4899"},onMouseLeave:c=>{c.currentTarget.style.backgroundColor="rgba(236, 72, 153, 0.1)",c.currentTarget.style.borderColor="rgba(236, 72, 153, 0.3)"},children:[s.jsx("svg",{style:{width:"14px",height:"14px"},fill:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{d:"M7 14c-1.66 0-3 1.34-3 3 0 1.31.84 2.41 2 2.83V22h2v-2.17c1.16-.42 2-1.52 2-2.83 0-1.66-1.34-3-3-3zM10.5 2C9 2 7.73 3.15 7.59 4.59L7.17 8.41C7.05 9.85 8.23 11 9.67 11h1.66c1.44 0 2.62-1.15 2.5-2.59L13.41 4.59C13.27 3.15 12 2 10.5 2z"})}),"API KEYS"]}),l==="slideshow"&&s.jsxs("div",{style:{fontSize:"14px",color:"#ec4899",fontFamily:'"Space Mono", monospace',fontWeight:"bold",letterSpacing:"0.5px"},children:["DURATION: ",s.jsx("span",{id:"timeline-duration",children:"0.0s"})]})]})]}),wh=()=>{const o=ad(),l=[{icon:"🖼️",title:"Create Slideshow",description:"Transform your images into stunning videos with smooth transitions and effects",path:"/slideshow",color:"#ec4899",bgColor:"rgba(236, 72, 153, 0.1)"},{icon:"🎬",title:"Edit Video",description:"Trim, edit, and enhance your video files with professional tools",path:"/video-editor",color:"#3b82f6",bgColor:"rgba(59, 130, 246, 0.1)"}];return s.jsxs("div",{style:{minHeight:"100vh",backgroundColor:"#0a0a0b",color:"white",fontFamily:'"Space Mono", monospace',display:"flex",flexDirection:"column"},children:[s.jsx(Ma,{currentMode:"home"}),s.jsx("main",{style:{flex:1,display:"flex",alignItems:"center",justifyContent:"center",padding:"40px"},children:s.jsxs("div",{style:{maxWidth:"800px",width:"100%",textAlign:"center"},children:[s.jsxs("div",{style:{marginBottom:"60px"},children:[s.jsx("div",{style:{width:"80px",height:"80px",backgroundColor:"rgba(236, 72, 153, 0.15)",border:"3px solid #ec4899",borderRadius:"20px",display:"flex",alignItems:"center",justifyContent:"center",margin:"0 auto 24px auto"},children:s.jsx("svg",{style:{width:"40px",height:"40px",color:"#ec4899"},fill:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{d:"M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"})})}),s.jsx("h1",{style:{fontSize:"48px",fontWeight:"bold",margin:"0 0 16px 0",background:"linear-gradient(135deg, #ec4899 0%, #3b82f6 100%)",WebkitBackgroundClip:"text",WebkitTextFillColor:"transparent"},children:"AnimaGen"}),s.jsxs("p",{style:{fontSize:"20px",color:"#9ca3af",margin:"0 0 40px 0",lineHeight:"1.6"},children:["Create stunning animated content with professional tools.",s.jsx("br",{}),"Choose your creative path and start building amazing videos."]})]}),s.jsx("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(350px, 1fr))",gap:"32px",maxWidth:"800px",margin:"0 auto"},children:l.map((i,c)=>s.jsxs("div",{onClick:()=>o(i.path),style:{backgroundColor:"#1a1a1b",border:"2px solid #343536",borderRadius:"16px",padding:"40px 32px",cursor:"pointer",transition:"all 0.3s ease",textAlign:"center"},onMouseEnter:d=>{d.currentTarget.style.borderColor=i.color,d.currentTarget.style.backgroundColor=i.bgColor,d.currentTarget.style.transform="translateY(-4px)"},onMouseLeave:d=>{d.currentTarget.style.borderColor="#343536",d.currentTarget.style.backgroundColor="#1a1a1b",d.currentTarget.style.transform="translateY(0)"},children:[s.jsx("div",{style:{fontSize:"64px",marginBottom:"20px"},children:i.icon}),s.jsx("h3",{style:{fontSize:"24px",fontWeight:"bold",color:i.color,margin:"0 0 12px 0"},children:i.title}),s.jsx("p",{style:{fontSize:"16px",color:"#9ca3af",margin:"0 0 24px 0",lineHeight:"1.5"},children:i.description}),s.jsxs("div",{style:{display:"inline-flex",alignItems:"center",gap:"8px",color:i.color,fontSize:"16px",fontWeight:"bold"},children:["Get Started",s.jsx("span",{children:"→"})]})]},c))}),s.jsxs("div",{style:{marginTop:"60px",display:"flex",gap:"32px",justifyContent:"center",flexWrap:"wrap"},children:[s.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"8px",color:"#9ca3af",fontSize:"14px"},children:[s.jsx("div",{style:{width:"8px",height:"8px",backgroundColor:"#10b981",borderRadius:"50%"}}),"No watermarks"]}),s.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"8px",color:"#9ca3af",fontSize:"14px"},children:[s.jsx("div",{style:{width:"8px",height:"8px",backgroundColor:"#3b82f6",borderRadius:"50%"}}),"Fast export"]}),s.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"8px",color:"#9ca3af",fontSize:"14px"},children:[s.jsx("div",{style:{width:"8px",height:"8px",backgroundColor:"#ec4899",borderRadius:"50%"}}),"Professional quality"]})]})]})})]})},Sh=window.location.hostname==="localhost"?"http://localhost:3001":window.location.origin,kh=async(o,l)=>{const i=new FormData;o.forEach(d=>i.append("images",d));const c=await fetch(`${Sh}/upload?sessionId=${l}`,{method:"POST",body:i});if(!c.ok)throw new Error(`Upload failed: ${c.statusText}`);return c.json()},Ch=o=>{const[l,i]=h.useState([]),[c,d]=h.useState(!1),[f,p]=h.useState(!1),g=h.useCallback(async C=>{if(C.length===0)return;d(!0);const v=o||`session_${Date.now()}_${Math.random().toString(36).substr(2,9)}`;console.log("📤 Upload starting with sessionId:",v);try{const w=await kh(C,v);if(w.success&&w.files){console.log("✅ Images uploaded successfully:",w);const D=C.map((T,j)=>{const R=w.files[j];return{file:T,id:`image_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,name:T.name,size:T.size,preview:URL.createObjectURL(T),uploadedInfo:R,addedAt:new Date}});return i(T=>[...T,...D]),{images:D,sessionId:w.sessionId}}throw new Error("Upload failed - no response from server")}catch(w){throw console.error("❌ Upload failed:",w),w}finally{d(!1)}},[o]),x=h.useCallback(C=>{i(v=>v.filter(w=>w.id!==C))},[]),y=h.useCallback(()=>{i([])},[]);return{images:l,isUploading:c,dragActive:f,hasImages:l.length>0,uploadImages:g,removeImage:x,clearImages:y,setDragActive:p}},hd=window.location.hostname==="localhost"?"http://localhost:3001":window.location.origin,bh=async o=>{const l=await fetch(`${hd}/preview`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(o)});if(!l.ok){const i=await l.text();let c=`Preview API error (${l.status})`;try{const d=JSON.parse(i);c=d.error||d.details||c}catch{c=i||c}throw new Error(c)}return l.json()},jh=({timeline:o,images:l,sessionId:i,updatePreviewState:c})=>{const d=h.useCallback(async()=>{if(o.length!==0){c({isGenerating:!0,error:null});try{const p={images:o.map(x=>{var v;const y=l.find(w=>w.id===x.imageId),C=((v=y==null?void 0:y.uploadedInfo)==null?void 0:v.filename)||(y==null?void 0:y.name);return console.log(`🔍 Timeline item ${x.id} → image ${y==null?void 0:y.id} → filename: ${C}`),{filename:C}}),transitions:o.slice(0,-1).map(x=>{var y,C;return{type:((y=x.transition)==null?void 0:y.type)||"cut",duration:((C=x.transition)==null?void 0:C.duration)||0}}),frameDurations:o.map(x=>x.duration),sessionId:i};if(console.log("🎬 Preview payload:",p),console.log("🔍 State project:",{timelineLength:o.length,imagesLength:l.length,sessionId:i}),!p.sessionId)throw new Error("No session ID available. Please upload images first.");if(p.images.length===0)throw new Error("No images in timeline. Please add images to timeline first.");const g=await bh(p);if(console.log("🎬 Preview result:",g),g.success){const x=`${hd}${g.previewUrl}?t=${Date.now()}`;console.log("🎬 Preview video URL created"),c({url:x,isGenerating:!1,error:null})}else throw new Error(g.message||"Preview generation failed")}catch(p){console.error("❌ Preview generation failed:",p),c({isGenerating:!1,error:p instanceof Error?p.message:"Preview generation failed"})}}},[o,l,i,c]),f=h.useCallback(()=>{c({url:null,error:null})},[c]);return{generatePreview:d,clearPreview:f}},ga=window.location.hostname==="localhost"?"http://localhost:3001":window.location.origin,Eh=({timeline:o,images:l,sessionId:i,exportSettings:c,updateExportState:d,updateExportSettingsState:f})=>{const p=h.useCallback(async y=>{if(!y)throw new Error("Master filename is required");d({progress:60,currentStep:`Converting to ${c.format.toUpperCase()} (${c.quality} quality)...`});try{const C={masterFilename:y,format:c.format,quality:c.quality,sessionId:i};console.log("🔄 Step 2: Export from master payload:",C),d({progress:70,currentStep:`Processing ${c.format.toUpperCase()} conversion...`});const v=await fetch(`${ga}/export/from-master`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(C)});if(!v.ok){const D=await v.json();throw new Error(D.error||"Format conversion failed")}const w=await v.json();return console.log("✅ Step 2 completed: Export from master result:",w),d({isExporting:!1,progress:100,currentStep:`${c.format.toUpperCase()} export completed!`,downloadUrl:w.downloadUrl,isCompleted:!0}),w.downloadUrl&&setTimeout(()=>{const D=document.createElement("a");D.href=`${ga}${w.downloadUrl}`,D.download=w.filename||`export.${c.format}`,D.style.display="none",document.body.appendChild(D),D.click(),document.body.removeChild(D)},500),w}catch(C){throw console.error("❌ Export from master failed:",C),d({isExporting:!1,error:C instanceof Error?C.message:"Format conversion failed",progress:0,currentStep:"Export failed",isCompleted:!1}),C}},[c,i,d]),g=h.useCallback(async()=>{if(o.length!==0){d({isExporting:!0,error:null,progress:0,currentStep:"Preparing export...",isCompleted:!1,downloadUrl:void 0});try{d({progress:10,currentStep:"Generating high-quality master video..."});const y={images:o.map(D=>{var j;const T=l.find(R=>R.id===D.imageId);return{filename:((j=T==null?void 0:T.uploadedInfo)==null?void 0:j.filename)||(T==null?void 0:T.name)}}),transitions:o.slice(0,-1).map(D=>{var T,j;return{type:((T=D.transition)==null?void 0:T.type)||"cut",duration:((j=D.transition)==null?void 0:j.duration)||0}}),frameDurations:o.map(D=>D.duration),sessionId:i};console.log("🎬 Step 1: Generating high-quality master for export:",y);const C=await fetch(`${ga}/generate-master`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(y)});if(!C.ok){const D=await C.json();throw new Error(D.error||"High-quality master generation failed")}const v=await C.json();console.log("✅ Step 1 completed: High-quality master generated:",v),d({progress:50,currentStep:`Converting to ${c.format.toUpperCase()} (${c.quality})...`}),console.log("🔄 Step 2: Converting master to final format");const w=await p(v.filename);return console.log("✅ Export completed successfully:",w),w}catch(y){throw console.error("❌ Export slideshow failed:",y),d({isExporting:!1,error:y instanceof Error?y.message:"Export failed",progress:0,currentStep:"Export failed",isCompleted:!1}),y}}},[o,l,i,c,d,p]),x=h.useCallback(y=>{f(y)},[f]);return{exportSlideshow:g,updateExportSettings:x}},Nh={project:{id:`project_${Date.now()}`,images:[],timeline:[],exportSettings:{format:"mp4",preset:"quality",quality:"high",fps:30,resolution:{width:1920,height:1080,preset:"1080p"},loop:!0,tags:{}},sessionId:""},preview:{url:null,isGenerating:!1,error:null},export:{isExporting:!1,progress:0,lastResult:null,error:null,isCompleted:!1,downloadUrl:void 0},isUploading:!1,dragActive:!1,selection:{selectedImages:[],isSelectionMode:!1}},Th=()=>{const[o,l]=h.useState(Nh),i=h.useCallback(S=>{l(M=>({...M,preview:{...M.preview,...S}}))},[]),c=h.useCallback(S=>{l(M=>({...M,export:{...M.export,...S}}))},[]),d=h.useCallback(S=>{l(M=>({...M,project:{...M.project,exportSettings:{...M.project.exportSettings,...S}}}))},[]),f=Ch(o.project.sessionId||""),p=h.useCallback(async S=>{l(M=>({...M,isUploading:!0}));try{const M=await f.uploadImages(S);M&&l(F=>({...F,project:{...F.project,images:M.images,sessionId:M.sessionId}}))}finally{l(M=>({...M,isUploading:!1}))}},[f]),g=h.useCallback((S,M=1e3)=>{const F={id:`timeline_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,imageId:S,duration:M,position:o.project.timeline.length,transition:void 0};l(H=>({...H,project:{...H.project,timeline:[...H.project.timeline,F]},preview:{...H.preview,url:null}}))},[o.project.timeline.length]),x=h.useCallback((S,M)=>{l(F=>({...F,project:{...F.project,timeline:F.project.timeline.map(H=>H.id===S?{...H,...M}:H)},preview:{...F.preview,url:null}}))},[]),y=h.useCallback(S=>{l(M=>({...M,project:{...M.project,timeline:M.project.timeline.filter(F=>F.id!==S)},preview:{...M.preview,url:null}}))},[]),C=h.useCallback(S=>{l(M=>({...M,project:{...M.project,timeline:S},preview:{...M.preview,url:null}}))},[]),v=jh({timeline:o.project.timeline,images:o.project.images,sessionId:o.project.sessionId,updatePreviewState:i}),w=Eh({timeline:o.project.timeline,images:o.project.images,sessionId:o.project.sessionId,exportSettings:o.project.exportSettings,updateExportState:c,updateExportSettingsState:d}),D=h.useCallback(S=>{l(M=>({...M,dragActive:S}))},[]),T=h.useCallback(()=>{l(S=>({...S,project:{id:`project_${Date.now()}`,images:[],timeline:[],exportSettings:S.project.exportSettings,sessionId:""},preview:{url:null,isGenerating:!1,error:null},export:{isExporting:!1,progress:0,lastResult:null,error:null,isCompleted:!1,downloadUrl:void 0},selection:{selectedImages:[],isSelectionMode:!1}}))},[]),j=h.useCallback(async S=>{try{console.log("📁 Loading images from session:",S);const M=await fetch(`/api/debug/session/${S}`);if(M.ok){const F=await M.json();if(console.log("📁 Session data:",F),F.exists&&F.files&&F.files.length>0){const H=F.files.map((J,te)=>({id:`session_${te}_${Date.now()}`,file:new File([],J.filename,{type:"image/jpeg"}),name:J.filename,preview:`/uploads/${S}/${J.filename}`,order:te}));return l(J=>({...J,project:{...J.project,sessionId:S,images:H}})),console.log(`✅ Loaded ${H.length} images from session`),!0}}return l(F=>({...F,project:{...F.project,sessionId:S,images:[]}})),console.log("✅ Session initialized, ready for uploads"),!0}catch(M){return console.error("❌ Failed to load session:",M),!1}},[]),R=h.useCallback(async S=>{try{console.log("🎬 Loading slideshow from API:",S);const F=await(await fetch(`/api/slideshow/${S}`)).json();if(!F.success)throw new Error(F.error||"Failed to load slideshow");const H=F.slideshow;console.log("✅ Slideshow data loaded:",H);const J=H.frames.map(X=>({id:X.id,name:X.name,file:null,size:0,preview:X.imageUrl,addedAt:new Date,uploadedInfo:{filename:X.name,originalName:X.name,path:X.imageUrl,size:0,mimetype:"image/png"}})),te=H.frames.map(X=>({id:`timeline_${X.id}`,imageId:X.id,duration:X.duration,position:X.order,transition:{type:X.transition,duration:500}}));return l(X=>({...X,project:{id:H.id,images:J,timeline:te,exportSettings:{...X.project.exportSettings,format:H.settings.format==="slideshow"?"gif":H.settings.format,loop:H.settings.loop},sessionId:H.id},preview:{url:null,isGenerating:!1,error:null},export:{isExporting:!1,progress:0,lastResult:null,error:null,isCompleted:!1,downloadUrl:void 0},selection:{selectedImages:[],isSelectionMode:!1}})),console.log("✅ Slideshow loaded successfully into context"),!0}catch(M){return console.error("❌ Failed to load slideshow from API:",M),l(F=>({...F,preview:{...F.preview,error:M instanceof Error?M.message:"Failed to load slideshow"}})),!1}},[]),b=h.useCallback(()=>{l(S=>({...S,selection:{selectedImages:[],isSelectionMode:!S.selection.isSelectionMode}}))},[]),_=h.useCallback(S=>{l(M=>{const F=M.selection.selectedImages,J=F.includes(S)?F.filter(te=>te!==S):[...F,S];return{...M,selection:{...M.selection,selectedImages:J}}})},[]),W=h.useCallback(()=>{l(S=>({...S,selection:{...S.selection,selectedImages:[]}}))},[]),V=h.useCallback(()=>{o.selection.selectedImages.forEach(M=>{g(M)}),W()},[o.selection.selectedImages,g,W]),P=h.useCallback(S=>{l(M=>({...M,project:{...M.project,images:M.project.images.filter(F=>F.id!==S),timeline:M.project.timeline.filter(F=>F.imageId!==S)},preview:{...M.preview,url:null}}))},[]);return{project:o.project,preview:o.preview,export:o.export,isUploading:o.isUploading,dragActive:o.dragActive,selection:o.selection,hasImages:o.project.images.length>0,hasTimeline:o.project.timeline.length>0,uploadImages:p,removeImage:P,addToTimeline:g,updateTimelineItem:x,removeFromTimeline:y,reorderTimeline:C,generatePreview:v.generatePreview,clearPreview:v.clearPreview,exportSlideshow:w.exportSlideshow,updateExportSettings:w.updateExportSettings,updateExportState:c,setDragActive:D,clearProject:T,loadSlideshowFromAPI:R,loadImagesFromSession:j,toggleSelectionMode:b,toggleImageSelection:_,clearSelection:W,addSelectedToTimeline:V}},gd=h.createContext(null),Rh=({children:o})=>{const l=Th(),i=We.useMemo(()=>({...l}),[l]);return s.jsx(gd.Provider,{value:i,children:o})},Kr=()=>{const o=h.useContext(gd);if(!o)throw new Error("useSlideshowContext must be used within a SlideshowProvider");return o},qe={colors:{primary:"#ff4500",secondary:"#22c55e",accent:"#ec4899",background:"#0f0f0f",surface:"#1a1a1b",border:"#343536",text:"#f3f4f6",textSecondary:"#9ca3af",success:"#22c55e",warning:"#f59e0b",error:"#ef4444"},spacing:{xs:"0.25rem",sm:"0.5rem",md:"0.75rem",lg:"1rem",xl:"1.5rem"},borderRadius:{sm:"0.25rem",md:"0.5rem",lg:"0.75rem"},shadows:{sm:"0 1px 2px 0 rgba(0, 0, 0, 0.05)",md:"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",lg:"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)"},transitions:{fast:"all 0.15s ease",normal:"all 0.2s ease",slow:"all 0.3s ease"}},Ca={colors:{...qe.colors,primary:"#ec4899",accent:"#ec4899"}},Ih={colors:{...qe.colors,primary:"#3b82f6",accent:"#3b82f6"}},xd={small:{thumbnail:{width:48,height:48},item:{height:60},padding:qe.spacing.sm},medium:{thumbnail:{width:80,height:80},item:{height:96},padding:qe.spacing.md},large:{thumbnail:{width:120,height:120},item:{height:144},padding:qe.spacing.lg}},Mh={list:{direction:"column",gap:qe.spacing.sm,itemWidth:"100%"},grid:{direction:"row",gap:qe.spacing.md,itemWidth:"auto",columns:"auto-fill",minItemWidth:"200px"},timeline:{direction:"row",gap:qe.spacing.lg,itemWidth:"200px",overflow:"auto"}};qe.transitions.fast,qe.transitions.fast,qe.transitions.normal;const Hc=(o,l)=>({colors:{...o.colors,...l.colors},spacing:{...o.spacing,...l.spacing},borderRadius:{...o.borderRadius,...l.borderRadius},shadows:{...o.shadows,...l.shadows},transitions:{...o.transitions,...l.transitions}}),vd=({item:o,size:l="medium",showType:i=!1,loading:c=!1,error:d=!1,className:f="",style:p={},onClick:g,onLoad:x,onError:y})=>{const[C,v]=h.useState(!1),[w,D]=h.useState(!1),T=xd[l],j=qe,R=h.useCallback(()=>{v(!0),x==null||x()},[x]),b=h.useCallback(()=>{D(!0),y==null||y()},[y]),_=h.useCallback(F=>{F.stopPropagation(),g&&!c&&!d&&g(o)},[g,o,c,d]),W={position:"relative",width:(p==null?void 0:p.width)||T.thumbnail.width,height:(p==null?void 0:p.height)||T.thumbnail.height,minHeight:p==null?void 0:p.minHeight,maxHeight:p==null?void 0:p.maxHeight,aspectRatio:p==null?void 0:p.aspectRatio,borderRadius:j.borderRadius.sm,overflow:"hidden",backgroundColor:j.colors.surface,cursor:g?"pointer":"default",transition:j.transitions.normal,border:`1px solid ${j.colors.border}`,...p},V={width:"100%",height:"100%",objectFit:"cover",transition:j.transitions.normal,opacity:C?1:0},P={position:"absolute",inset:0,display:"flex",alignItems:"center",justifyContent:"center",backgroundColor:j.colors.surface,color:j.colors.textSecondary,fontSize:"0.75rem"},S={position:"absolute",inset:0,display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",backgroundColor:j.colors.surface,color:j.colors.error,fontSize:"0.75rem",textAlign:"center",padding:j.spacing.xs},M={position:"absolute",top:j.spacing.xs,right:j.spacing.xs,width:"16px",height:"16px",backgroundColor:"rgba(0,0,0,0.7)",borderRadius:j.borderRadius.sm,display:"flex",alignItems:"center",justifyContent:"center",color:"white",fontSize:"0.625rem"};return s.jsxs("div",{className:`media-thumbnail ${f}`,style:W,onClick:_,onMouseEnter:F=>{g&&(F.currentTarget.style.borderColor=j.colors.primary,F.currentTarget.style.transform="scale(1.02)")},onMouseLeave:F=>{g&&(F.currentTarget.style.borderColor=j.colors.border,F.currentTarget.style.transform="scale(1)")},children:[o.preview&&!w&&s.jsx("img",{src:o.preview,alt:o.name,style:V,onLoad:R,onError:b}),(c||!C&&!w)&&s.jsx("div",{style:P,children:s.jsx("div",{style:{width:"20px",height:"20px",border:`2px solid ${j.colors.border}`,borderTop:`2px solid ${j.colors.primary}`,borderRadius:"50%",animation:"spin 1s linear infinite"}})}),(d||w)&&s.jsxs("div",{style:S,children:[s.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[s.jsx("circle",{cx:"12",cy:"12",r:"10"}),s.jsx("line",{x1:"12",y1:"8",x2:"12",y2:"12"}),s.jsx("line",{x1:"12",y1:"16",x2:"12.01",y2:"16"})]}),s.jsx("span",{style:{marginTop:j.spacing.xs,fontSize:"0.625rem"},children:"Error"})]}),i&&s.jsx("div",{style:M,children:o.type==="video"?s.jsx("svg",{width:"12",height:"12",viewBox:"0 0 24 24",fill:"currentColor",children:s.jsx("path",{d:"M8 5v14l11-7z"})}):s.jsxs("svg",{width:"12",height:"12",viewBox:"0 0 24 24",fill:"currentColor",children:[s.jsx("rect",{x:"3",y:"3",width:"18",height:"18",rx:"2",ry:"2"}),s.jsx("circle",{cx:"8.5",cy:"8.5",r:"1.5"}),s.jsx("polyline",{points:"21,15 16,10 5,21"})]})}),s.jsx("style",{children:`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `})]})},Ph=({item:o,config:l={},handlers:i={},selected:c=!1,dragging:d=!1,draggedOver:f=!1,className:p="",style:g={}})=>{const[x,y]=h.useState(!1),{showMetadata:C=!1,showSelection:v=!1,size:w="medium",layout:D="list",interactive:T=!0}=l,{onSelect:j,onDeselect:R,onRemove:b,onAdd:_,onPreview:W}=i,V=qe,P=xd[w],S=h.useCallback(()=>{if(T){if(_){_(o);return}c&&R?R(o):!c&&j&&j(o)}},[o,c,j,R,_,T]),M=h.useCallback(Z=>{Z.stopPropagation(),b==null||b(o)},[o,b]),F=Z=>{if(Z===0)return"0 B";const K=1024,fe=["B","KB","MB","GB"],se=Math.floor(Math.log(Z)/Math.log(K));return parseFloat((Z/Math.pow(K,se)).toFixed(1))+" "+fe[se]},H=Z=>{const K=Math.floor(Z/60),fe=Math.floor(Z%60);return`${K}:${fe.toString().padStart(2,"0")}`},J=h.useMemo(()=>({display:"flex",flexDirection:"column",alignItems:"stretch",gap:V.spacing.sm,padding:P.padding,backgroundColor:c?`${V.colors.primary}15`:V.colors.surface,border:`1px solid ${f?V.colors.accent:c?V.colors.primary:V.colors.border}`,borderRadius:V.borderRadius.md,cursor:T?"pointer":"default",transition:V.transitions.normal,opacity:d?.8:1,transform:d?"scale(1.05)":x&&T?"scale(1.02)":"scale(1)",boxShadow:d?V.shadows.lg:x&&T?V.shadows.md:V.shadows.sm,position:"relative",width:"100%",...g}),[D,c,f,d,x,T,V,P,g]),te={display:"flex",flexDirection:"column",gap:V.spacing.xs,width:"100%",alignItems:"center"},X={fontSize:"0.75rem",color:V.colors.textSecondary,margin:0,fontFamily:'"Space Mono", monospace'},ae={position:"absolute",top:V.spacing.xs,right:V.spacing.xs,width:"20px",height:"20px",borderRadius:"50%",backgroundColor:c?V.colors.primary:"transparent",border:`2px solid ${c?V.colors.primary:V.colors.border}`,display:"flex",alignItems:"center",justifyContent:"center",transition:V.transitions.fast};return s.jsxs("div",{className:`media-item ${p}`,style:J,onClick:S,onMouseEnter:()=>y(!0),onMouseLeave:()=>y(!1),children:[v&&s.jsx("div",{style:ae,children:c&&s.jsx("svg",{width:"12",height:"12",viewBox:"0 0 24 24",fill:"white",children:s.jsx("polyline",{points:"20,6 9,17 4,12",stroke:"white",strokeWidth:"2",fill:"none"})})}),s.jsx(vd,{item:o,size:w,showDuration:!1,showType:!1,onClick:_?()=>_(o):W,style:{width:"100%",height:"auto",minHeight:"120px",maxHeight:"200px",aspectRatio:"16/9"}}),_&&x&&s.jsxs("button",{style:{position:"absolute",bottom:"8px",left:"8px",right:"8px",height:"32px",backgroundColor:"rgba(236, 72, 153, 0.95)",color:"white",border:"none",borderRadius:"4px",fontSize:"12px",fontWeight:"bold",fontFamily:'"Space Mono", monospace',cursor:"pointer",display:"flex",alignItems:"center",justifyContent:"center",gap:"4px",transition:"all 0.2s ease",zIndex:5,opacity:1},onClick:Z=>{Z.stopPropagation(),_(o)},onMouseEnter:Z=>{Z.currentTarget.style.backgroundColor="rgba(236, 72, 153, 1)",Z.currentTarget.style.transform="translateY(-1px)"},onMouseLeave:Z=>{Z.currentTarget.style.backgroundColor="rgba(236, 72, 153, 0.9)",Z.currentTarget.style.transform="translateY(0)"},children:[s.jsx("svg",{width:"12",height:"12",viewBox:"0 0 24 24",fill:"currentColor",children:s.jsx("path",{d:"M12 5v14m-7-7h14",stroke:"currentColor",strokeWidth:"2",fill:"none",strokeLinecap:"round",strokeLinejoin:"round"})}),"ADD TO TIMELINE"]}),b&&x&&s.jsx("button",{style:{position:"absolute",top:"8px",right:"8px",width:"28px",height:"28px",backgroundColor:"rgba(239, 68, 68, 0.9)",border:"none",borderRadius:"50%",color:"white",cursor:"pointer",display:"flex",alignItems:"center",justifyContent:"center",fontSize:"12px",boxShadow:"0 2px 6px rgba(0,0,0,0.3)",transition:"all 0.2s ease",zIndex:10},onClick:M,onMouseEnter:Z=>{Z.currentTarget.style.backgroundColor="rgba(239, 68, 68, 1)",Z.currentTarget.style.transform="scale(1.1)"},onMouseLeave:Z=>{Z.currentTarget.style.backgroundColor="rgba(239, 68, 68, 0.9)",Z.currentTarget.style.transform="scale(1)"},children:s.jsx("svg",{width:"10",height:"10",viewBox:"0 0 24 24",fill:"currentColor",children:s.jsx("path",{d:"M3 6h18M8 6V4a2 2 0 012-2h4a2 2 0 012 2v2M19 6v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6h14zM10 11v6M14 11v6",stroke:"currentColor",strokeWidth:"2",fill:"none",strokeLinecap:"round",strokeLinejoin:"round"})})}),s.jsx("div",{style:te,children:C===!0&&s.jsxs("div",{style:{...X,textAlign:"center",width:"100%"},children:[s.jsx("div",{children:F(o.size)}),o.type==="video"&&s.jsx("div",{children:H(o.duration||0)}),o.type==="image"&&o.dimensions&&s.jsxs("div",{children:[o.dimensions.width," × ",o.dimensions.height]})]})})]})},yd=({children:o,className:l=""})=>s.jsx("div",{className:`flex flex-col min-h-0 ${l}`,style:{width:"400px",backgroundColor:"#0a0a0b",paddingTop:"16px",paddingLeft:"16px",paddingRight:"8px",paddingBottom:"8px"},children:o}),wd=({children:o,className:l=""})=>s.jsx("div",{className:`flex-1 flex flex-col min-h-0 ${l}`,style:{backgroundColor:"#0a0a0b",paddingTop:"16px",paddingLeft:"8px",paddingRight:"8px",paddingBottom:"8px"},children:o}),Sd=({children:o,className:l=""})=>s.jsx("div",{className:`flex flex-col min-h-0 overflow-auto ${l}`,style:{width:"400px",backgroundColor:"#0a0a0b",paddingTop:"16px",paddingLeft:"8px",paddingRight:"16px",paddingBottom:"8px"},children:o}),kd=({children:o,className:l=""})=>s.jsx("div",{className:`flex flex-col ${l}`,style:{height:"280px",backgroundColor:"#0a0a0b",paddingTop:"8px",paddingLeft:"16px",paddingRight:"16px",paddingBottom:"16px"},children:o}),Dh="_container_3ntik_9",_h="_panel_3ntik_17",Lh="_iconContainer_3ntik_27",zh="_textContent_3ntik_32",Fh="_title_3ntik_39",Oh="_subtitle_3ntik_46",Ah="_titleSlideshow_3ntik_52",$h="_titleVideoEditor_3ntik_56",Uh="_titleNeutral_3ntik_60",Bh="_fadeIn_3ntik_120",It={container:Dh,panel:_h,iconContainer:Lh,textContent:zh,title:Fh,subtitle:Oh,titleSlideshow:Ah,titleVideoEditor:$h,titleNeutral:Uh,fadeIn:Bh},hl=({title:o,subtitle:l,icon:i,titleColor:c="neutral",className:d=""})=>{const p=(()=>{switch(c){case"slideshow":return It.titleSlideshow;case"video-editor":return It.titleVideoEditor;case"neutral":return It.titleNeutral;default:return""}})(),g=typeof c=="string"&&!["slideshow","video-editor","neutral"].includes(c)?{color:c}:{};return s.jsx("div",{className:`${It.container} ${It.fadeIn} ${d}`,children:s.jsxs("div",{className:`${It.panel} panel`,children:[s.jsx("div",{className:It.iconContainer,children:i}),s.jsxs("div",{className:It.textContent,children:[s.jsx("div",{className:`${It.title} ${p}`,style:g,children:o}),s.jsx("div",{className:It.subtitle,children:l})]})]})})},Cd=({mode:o,className:l=""})=>{const c=o==="slideshow"?{title:"No Preview Available",subtitle:"Add images to timeline to generate preview"}:{title:"No Video Loaded",subtitle:"Upload a video to see preview"},d=s.jsx("svg",{className:"w-12 h-12",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2M7 4h10M7 4H5a2 2 0 00-2 2v14a2 2 0 002 2h14a2 2 0 002-2V6a2 2 0 00-2-2h-2"})});return s.jsx(hl,{title:c.title,subtitle:c.subtitle,icon:d,titleColor:"neutral",className:l,mode:o})},Pa=({mode:o,className:l=""})=>{const c=o==="slideshow"?{title:"No media files",subtitle:"Upload some files to get started"}:{title:"No Video Loaded",subtitle:"Upload a video to get started"},d=s.jsx("svg",{className:"w-12 h-12",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})});return s.jsx(hl,{title:c.title,subtitle:c.subtitle,icon:d,titleColor:"neutral",className:l,mode:o})},bd=({mode:o,className:l=""})=>{const c=o==="slideshow"?{title:"No images in timeline",subtitle:"Drag images here to create your slideshow"}:{title:"No Video Loaded",subtitle:"Timeline will appear here"},d=s.jsx("svg",{className:"w-12 h-12",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})});return s.jsx(hl,{title:c.title,subtitle:c.subtitle,icon:d,titleColor:"neutral",className:l,mode:o})},Vh="_dropzone_1k3t9_9",Wh="_dropzoneSlideshow_1k3t9_29",Hh="_dragActive_1k3t9_45",Qh="_dropzoneVideoEditor_1k3t9_53",Gh="_dropzoneLoading_1k3t9_78",Kh="_dropzoneDisabled_1k3t9_83",qh="_dropzoneError_1k3t9_89",Yh="_dropzoneIcon_1k3t9_96",Xh="_dropzoneText_1k3t9_112",Jh="_dropzoneSubtext_1k3t9_119",Zh="_dropzoneSpinner_1k3t9_127",eg="_dropzoneSpinnerIcon_1k3t9_134",xt={dropzone:Vh,dropzoneSlideshow:Wh,dragActive:Hh,dropzoneVideoEditor:Qh,dropzoneLoading:Gh,dropzoneDisabled:Kh,dropzoneError:qh,dropzoneIcon:Yh,dropzoneText:Xh,dropzoneSubtext:Jh,dropzoneSpinner:Zh,dropzoneSpinnerIcon:eg},jd=({config:o,handlers:l,loading:i=!1,error:c,disabled:d=!1,children:f,className:p="",style:g={},mode:x="slideshow"})=>{var ae,Z;const[y,C]=h.useState(!1),[,v]=h.useState(0),w=h.useRef(null),{accept:D,multiple:T,maxSize:j,maxFiles:R}=o,{onUpload:b}=l,_=h.useCallback(K=>{var O;const fe="."+((O=K.name.split(".").pop())==null?void 0:O.toLowerCase()),se=K.type;return D.some(B=>B.startsWith(".")?fe===B.toLowerCase():B.includes("/*")?se.startsWith(B.split("/")[0]):se===B)?j&&K.size>j?`File too large. Maximum size: ${(j/1048576).toFixed(1)}MB`:null:`File type not supported. Accepted types: ${D.join(", ")}`},[D,j]),W=h.useCallback(K=>{const fe=[],se=[];return R&&K.length>R?(se.push(`Too many files. Maximum: ${R}`),{valid:fe,errors:se}):(K.forEach(A=>{const O=_(A);O?se.push(`${A.name}: ${O}`):fe.push(A)}),{valid:fe,errors:se})},[_,R]),V=h.useCallback(async K=>{if(d||i)return;const fe=Array.from(K),{valid:se,errors:A}=W(fe);if(A.length>0){console.error("File validation errors:",A);return}if(se.length>0)try{await(b==null?void 0:b(se))}catch(O){console.error("Upload failed:",O)}},[d,i,W,b]),P=h.useCallback(K=>{K.preventDefault(),K.stopPropagation(),v(fe=>fe+1),K.dataTransfer.items&&K.dataTransfer.items.length>0&&C(!0)},[]),S=h.useCallback(K=>{K.preventDefault(),K.stopPropagation(),v(fe=>{const se=fe-1;return se===0&&C(!1),se})},[]),M=h.useCallback(K=>{K.preventDefault(),K.stopPropagation(),K.dataTransfer.dropEffect="copy"},[]),F=h.useCallback(K=>{K.preventDefault(),K.stopPropagation(),C(!1),v(0),K.dataTransfer.files&&K.dataTransfer.files.length>0&&V(K.dataTransfer.files)},[V]),H=h.useCallback(K=>{K.target.files&&K.target.files.length>0&&(V(K.target.files),K.target.value="")},[V]),J=h.useCallback(()=>{var K;!d&&!i&&((K=w.current)==null||K.click())},[d,i]),te=()=>{const K=[xt.dropzone];return x==="slideshow"?K.push(xt.dropzoneSlideshow):K.push(xt.dropzoneVideoEditor),y&&K.push(xt.dragActive),i&&K.push(xt.dropzoneLoading),d&&K.push(xt.dropzoneDisabled),c&&K.push(xt.dropzoneError),p&&K.push(p),K.join(" ")},X={...g};return s.jsxs("div",{className:te(),style:X,onDragEnter:P,onDragLeave:S,onDragOver:M,onDrop:F,onClick:J,children:[s.jsx("input",{ref:w,type:"file",accept:D.join(","),multiple:T,onChange:H,style:{display:"none"},disabled:d||i}),i?s.jsxs("div",{className:xt.dropzoneSpinner,children:[s.jsx("div",{className:xt.dropzoneSpinnerIcon}),s.jsx("span",{children:"Uploading..."})]}):f||s.jsxs(s.Fragment,{children:[s.jsx("svg",{className:xt.dropzoneIcon,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})}),s.jsxs("div",{children:[s.jsx("p",{className:xt.dropzoneText,children:((ae=o.text)==null?void 0:ae.primary)||"Drop files here or click to browse"}),s.jsx("p",{className:xt.dropzoneSubtext,children:((Z=o.text)==null?void 0:Z.secondary)||"Supported file types"})]})]})]})},Ed=({mode:o,config:l,handlers:i,loading:c=!1,children:d,className:f=""})=>{const g=o==="slideshow"?Hc(qe,Ca):Hc(qe,Ih),x=()=>{const C={borderColor:g.colors.primary,backgroundColor:"transparent",color:g.colors.primary};return o==="slideshow"?{...C,minHeight:"60px"}:{...C,minHeight:"80px",backgroundColor:c?"#1a1a1b":"transparent"}},y=()=>o==="slideshow"?{...l,text:{primary:"Drop images here or click to browse",secondary:"Supports JPG, PNG, GIF, WebP"}}:{...l,text:{primary:"Drop video here or click to browse",secondary:"Supports MP4, WebM, MOV, AVI"}};return s.jsxs("div",{className:`h-full flex flex-col ${f}`,children:[s.jsx("div",{className:"flex-shrink-0",style:{marginBottom:"8px"},children:s.jsx(jd,{config:y(),handlers:i,loading:c,className:"h-auto",style:x(),theme:g,mode:o})}),s.jsx("div",{className:"flex-1 min-h-0",children:d})]})},tg=({items:o,config:l={},handlers:i={},selection:c,filter:d,sort:f,loading:p=!1,error:g,className:x="",style:y={},theme:C={},mode:v="slideshow"})=>{const[w,D]=h.useState(null),[T,j]=h.useState(null),{layout:R="list",size:b="medium",sortable:_=!1,selectable:W=!1,showActions:V=!0,showMetadata:P=!0,showSelection:S=!1}=l,M={...qe,...C},F=Mh[R],H=h.useMemo(()=>{let O=[...o];return d&&(O=O.filter(B=>!(d.type&&!d.type.includes(B.type)||d.name&&!B.name.toLowerCase().includes(d.name.toLowerCase())||d.sizeRange&&(B.size<d.sizeRange.min||B.size>d.sizeRange.max)||d.dateRange&&(B.createdAt<d.dateRange.start||B.createdAt>d.dateRange.end)))),f&&O.sort((B,E)=>{let U=B[f.field],re=E[f.field];(f.field==="createdAt"||f.field==="updatedAt")&&(U=new Date(U).getTime(),re=new Date(re).getTime()),typeof U=="string"&&(U=U.toLowerCase(),re=re.toLowerCase());const pe=U<re?-1:U>re?1:0;return f.direction==="desc"?-pe:pe}),O},[o,d,f]),J=h.useCallback((O,B)=>{_&&(D(B),O.dataTransfer.effectAllowed="move",O.dataTransfer.setData("text/plain",B))},[_]),te=h.useCallback(()=>{D(null),j(null)},[]),X=h.useCallback((O,B)=>{_&&(O.preventDefault(),O.dataTransfer.dropEffect="move",j(B))},[_]),ae=h.useCallback(()=>{j(null)},[]),Z=h.useCallback((O,B)=>{if(!_)return;O.preventDefault();const E=O.dataTransfer.getData("text/plain");if(!E||!i.onReorder)return;const U=H.findIndex(re=>re.id===E);U===-1||U===B||(i.onReorder(U,B),D(null),j(null))},[_,H,i]),K=h.useMemo(()=>({display:"flex",flexDirection:F.direction,gap:F.gap,padding:M.spacing.md,backgroundColor:M.colors.background,borderRadius:M.borderRadius.md,minHeight:"200px",maxHeight:"100%",position:"relative",overflow:"hidden",...y}),[F,C,y]),fe={display:R==="grid"?"grid":"flex",flexDirection:R==="list"?"column":"row",gap:F.gap,width:"100%",height:"100%",...R==="list"&&{overflowY:"auto",overflowX:"hidden"},...R==="grid"&&{gridTemplateColumns:`repeat(auto-fill, minmax(${F.minItemWidth||"200px"}, 1fr))`,overflowY:"auto",overflowX:"hidden"},...R==="timeline"&&{overflowX:"auto",overflowY:"hidden",paddingBottom:M.spacing.sm},scrollbarWidth:"thin",scrollbarColor:`${M.colors.border} transparent`},se={display:"flex",alignItems:"center",justifyContent:"center",padding:M.spacing.xl,color:M.colors.textSecondary,fontFamily:'"Space Mono", monospace'},A={color:M.colors.error,backgroundColor:`${M.colors.error}15`,border:`1px solid ${M.colors.error}`,borderRadius:M.borderRadius.md,padding:M.spacing.md,margin:M.spacing.md,fontSize:"0.875rem",fontFamily:'"Space Mono", monospace'};return p?s.jsx("div",{className:`media-list ${x}`,style:K,children:s.jsxs("div",{style:se,children:[s.jsx("div",{style:{width:"24px",height:"24px",border:`2px solid ${M.colors.border}`,borderTop:`2px solid ${M.colors.primary}`,borderRadius:"50%",animation:"spin 1s linear infinite",marginRight:M.spacing.sm}}),"Loading media..."]})}):g?s.jsx("div",{className:`media-list ${x}`,style:K,children:s.jsxs("div",{style:A,children:[s.jsx("strong",{children:"Error:"})," ",g]})}):H.length===0?s.jsx("div",{className:`media-list ${x}`,style:{...K,padding:0},children:s.jsx(Pa,{mode:v})}):s.jsxs("div",{className:`media-list ${x}`,style:K,children:[s.jsx("div",{style:fe,children:H.map((O,B)=>s.jsx("div",{draggable:_,onDragStart:E=>J(E,O.id),onDragEnd:te,onDragOver:E=>X(E,B),onDragLeave:ae,onDrop:E=>Z(E,B),style:{...R==="timeline"&&{flexShrink:0,width:F.itemWidth}},children:s.jsx(Ph,{item:O,config:{size:b,layout:R,showActions:V,showMetadata:P,showSelection:W||S,interactive:!0},handlers:i,selected:c==null?void 0:c.selectedIds.has(O.id),dragging:w===O.id,draggedOver:T===B})},O.id))}),s.jsx("style",{children:`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        /* Custom scrollbar styles */
        .media-list ::-webkit-scrollbar {
          width: 6px;
          height: 6px;
        }

        .media-list ::-webkit-scrollbar-track {
          background: transparent;
        }

        .media-list ::-webkit-scrollbar-thumb {
          background: ${M.colors.border};
          border-radius: 3px;
        }

        .media-list ::-webkit-scrollbar-thumb:hover {
          background: ${M.colors.primary};
        }

        .media-list ::-webkit-scrollbar-corner {
          background: transparent;
        }
      `})]})},Nd=o=>{const{config:l,onSuccess:i,onError:c,validators:d=[],uploadEndpoint:f="/api/upload",generatePreview:p=!0}=o,[g,x]=h.useState(!1),[y,C]=h.useState([]),[v,w]=h.useState([]),[D,T]=h.useState([]),j=h.useRef(null),R=h.useCallback(F=>{var X;const H="."+((X=F.name.split(".").pop())==null?void 0:X.toLowerCase()),J=F.type;if(!l.accept.some(ae=>ae.startsWith(".")?H===ae.toLowerCase():ae.includes("/*")?J.startsWith(ae.split("/")[0]):J===ae))return`File type not supported. Accepted types: ${l.accept.join(", ")}`;if(l.maxSize&&F.size>l.maxSize)return`File too large. Maximum size: ${(l.maxSize/1048576).toFixed(1)}MB`;for(const ae of d){const Z=ae(F);if(typeof Z=="string")return Z;if(Z===!1)return"File validation failed"}return null},[l,d]),b=h.useCallback(async F=>new Promise((H,J)=>{if(!p){H("");return}const te=new FileReader;te.onload=X=>{var Z;const ae=(Z=X.target)==null?void 0:Z.result;typeof ae=="string"?H(ae):J(new Error("Failed to generate preview"))},te.onerror=()=>{J(new Error("Failed to read file"))},F.type.startsWith("image/")?te.readAsDataURL(F):(F.type.startsWith("video/"),H(""))}),[p]),_=h.useCallback((F,H)=>{const J=new Date,te=F.type.startsWith("video/"),X={id:`${Date.now()}-${Math.random().toString(36).substr(2,9)}`,file:F,name:F.name,size:F.size,preview:H,createdAt:J,updatedAt:J};return te?{...X,type:"video",duration:0,dimensions:{width:0,height:0},thumbnails:[]}:{...X,type:"image"}},[]),W=h.useCallback(async(F,H)=>{var fe;const J=R(F);if(J)throw new Error(J);let te="";try{te=await b(F)}catch(se){console.warn("Failed to generate preview for",F.name,se)}const X=_(F,te);if(!l.autoUpload)return X;const ae=new FormData;ae.append("file",F),ae.append("type",X.type);const Z=await fetch(f,{method:"POST",body:ae,signal:(fe=j.current)==null?void 0:fe.signal});if(!Z.ok)throw new Error(`Upload failed: ${Z.statusText}`);const K=await Z.json();return{...X,uploadedInfo:{filename:K.filename,path:K.path,sessionId:K.sessionId,uploadedAt:new Date}}},[R,b,_,l,f]),V=h.useCallback(async F=>{if(g)throw new Error("Upload already in progress");if(l.maxFiles&&F.length>l.maxFiles)throw new Error(`Too many files. Maximum: ${l.maxFiles}`);x(!0),C([]),w([]),j.current=new AbortController;const H=[],J=[],te=[];try{for(let Z=0;Z<F.length;Z++){const K=F[Z],fe=W(K,Z).then(se=>(te.push(se),se)).catch(se=>{const A={file:K,error:se.message,code:se.code};throw J.push(A),A});H.push(fe)}const X=await Promise.allSettled(H),ae=[];return X.forEach(Z=>{Z.status==="fulfilled"&&ae.push(Z.value)}),T(Z=>[...Z,...ae]),w(Z=>[...Z,...J]),ae.length>0&&(i==null||i(ae)),J.length>0&&(c==null||c(J)),ae}catch(X){throw console.error("Upload failed:",X),X}finally{x(!1),C([]),j.current=null}},[g,l,W,i,c]),P=h.useCallback(()=>{j.current&&(j.current.abort(),j.current=null),x(!1),C([])},[]),S=h.useCallback(()=>{w([])},[]),M=h.useCallback(()=>{T([])},[]);return{isUploading:g,progress:y,errors:v,uploadedItems:D,uploadFiles:V,cancelUpload:P,clearErrors:S,clearUploaded:M,validateFile:R,generatePreviewUrl:b,createMediaItem:_}};function Qc({mode:o,items:l,uploadConfig:i,listConfig:c={},theme:d={},onUpload:f,onItemAction:p,onError:g,convertToMediaItem:x,isUploading:y=!1,layout:C="vertical",showList:v=!0,showDropZone:w=!0,customContent:D,emptyStateContent:T,className:j="",style:R={}}){const b=h.useMemo(()=>l.map(x),[l,x]),{uploadFiles:_,clearErrors:W}=Nd({config:i,onSuccess:te=>{const X=te.map(ae=>ae.file);f(X),W()},onError:te=>{g==null||g(te)}}),V={layout:"list",size:"medium",showActions:!0,showMetadata:!0,showSelection:!1,sortable:!1,selectable:!1,...c},P={onUpload:async te=>{await _(te)},onAdd:te=>{p==null||p("add",l.find(X=>x(X).id===te.id))},onRemove:te=>{p==null||p("remove",l.find(X=>x(X).id===te.id))},onPreview:te=>{p==null||p("preview",l.find(X=>x(X).id===te.id))},onEdit:te=>{p==null||p("edit",l.find(X=>x(X).id===te.id))}},S=l.length===0&&i.showEmptyState!==!1,F={colors:{primary:o==="slideshow"?"#ec4899":"#3b82f6",secondary:"#6b7280",accent:o==="slideshow"?"#ec4899":"#3b82f6",background:"#0a0a0b",surface:"#1f2937",text:"#f3f4f6",textSecondary:"#9ca3af",border:"#374151",success:"#10b981",warning:"#f59e0b",error:"#ef4444"},...d},H=()=>D||(S?T||s.jsx(Pa,{mode:o}):v&&b.length>0?s.jsx("div",{className:"flex-1 min-h-0",children:s.jsx(tg,{items:b,config:V,handlers:P,loading:y,theme:F,mode:o,className:"h-full",style:{backgroundColor:"transparent",padding:0}})}):null),J=["h-full",C==="horizontal"?"flex flex-row":"flex flex-col",j].filter(Boolean).join(" ");return s.jsx("div",{className:J,style:R,children:w?s.jsx(Ed,{mode:o,config:i,handlers:P,loading:y,children:H()}):H()})}const ng=()=>{const{project:o,uploadImages:l,addToTimeline:i,removeImage:c,isUploading:d}=Kr(),f={accept:["image/*"],multiple:!0,maxSize:10*1024*1024,autoUpload:!1,showEmptyState:!0},p={layout:"list",size:"medium",showActions:!1,showMetadata:!1,showSelection:!1,sortable:!1,selectable:!1},g=v=>({id:v.id,file:v.file,name:v.name,type:"image",size:v.file.size,preview:v.preview,uploadedInfo:v.uploadedInfo?{sessionId:"",uploadedAt:new Date,...v.uploadedInfo}:void 0,createdAt:new Date,updatedAt:new Date}),x=async v=>{await l(v)},y=(v,w)=>{switch(v){case"add":i(w.id);break;case"remove":c(w.id);break;default:console.log(`Unhandled action: ${v}`)}},C=v=>{v.forEach(w=>{console.error("Upload validation failed:",w.error,w.file.name)})};return s.jsx(Qc,{mode:"slideshow",items:o.images,uploadConfig:f,listConfig:p,theme:Ca,onUpload:x,onItemAction:y,onError:C,convertToMediaItem:g,isUploading:d,customContent:s.jsxs("div",{className:"flex flex-col h-full",children:[s.jsx(Qc,{mode:"slideshow",items:o.images,uploadConfig:f,listConfig:p,theme:Ca,onUpload:x,onItemAction:y,onError:C,convertToMediaItem:g,isUploading:d,showDropZone:!1,className:"flex-1"}),o.images.length>0&&s.jsx("div",{className:"flex gap-2 flex-shrink-0",style:{marginTop:"8px"},children:s.jsxs("button",{onClick:()=>{o.images.forEach(v=>i(v.id))},disabled:d,style:{width:"100%",padding:"12px",fontSize:"14px",fontWeight:"600",backgroundColor:"rgba(236, 72, 153, 0.15)",color:"#ec4899",border:"1px solid #ec4899",borderRadius:"3px",cursor:d?"not-allowed":"pointer",fontFamily:'"Space Mono", monospace',textTransform:"uppercase",letterSpacing:"0.5px",transition:"all 0.2s ease"},onMouseEnter:v=>{d||(v.currentTarget.style.backgroundColor="rgba(236, 72, 153, 0.25)")},onMouseLeave:v=>{d||(v.currentTarget.style.backgroundColor="rgba(236, 72, 153, 0.15)")},children:["ALL TO TIMELINE (",o.images.length,")"]})})]})})},rg="_container_1v5b3_9",og="_containerSlideshow_1v5b3_20",lg="_containerVideoEditor_1v5b3_24",ig="_video_1v5b3_29",ag="_loadingContainer_1v5b3_39",sg="_loadingDefault_1v5b3_48",ug="_loadingSpinner_1v5b3_55",cg="_spin_1v5b3_1",dg="_loadingText_1v5b3_64",fg="_errorContainer_1v5b3_71",pg="_errorDefault_1v5b3_80",mg="_errorIcon_1v5b3_88",hg="_errorText_1v5b3_93",gg="_emptyContainer_1v5b3_101",xg="_emptyIcon_1v5b3_110",vg="_emptyText_1v5b3_115",yg="_overlay_1v5b3_122",Je={container:rg,containerSlideshow:og,containerVideoEditor:lg,video:ig,loadingContainer:ag,loadingDefault:sg,loadingSpinner:ug,spin:cg,loadingText:dg,errorContainer:fg,errorDefault:pg,errorIcon:mg,errorText:hg,emptyContainer:gg,emptyIcon:xg,emptyText:vg,overlay:yg},Td=({videoSrc:o,mode:l,onVideoLoaded:i,onVideoError:c,onVideoPlay:d,onVideoPause:f,showControls:p=!0,showOverlay:g=!1,overlayContent:x,autoPlay:y=!1,loop:C=!1,muted:v=!0,className:w="",containerClassName:D="",isLoading:T=!1,loadingContent:j,error:R=null,errorContent:b})=>{const _=h.useRef(null),[W,V]=h.useState(!1),[P,S]=h.useState(null),M=()=>{V(!0),S(null),i==null||i()},F=ae=>{S("Failed to load video preview"),V(!1),c==null||c(ae.nativeEvent)},H=()=>{d==null||d()},J=()=>{f==null||f()};h.useEffect(()=>{_.current&&o&&_.current.load()},[o]);const te=[Je.container,Je[`container${l==="slideshow"?"Slideshow":"VideoEditor"}`],D].filter(Boolean).join(" "),X=[Je.video,w].filter(Boolean).join(" ");return T?s.jsx("div",{className:te,children:s.jsx("div",{className:Je.loadingContainer,children:j||s.jsxs("div",{className:Je.loadingDefault,children:[s.jsx("div",{className:Je.loadingSpinner}),s.jsx("span",{className:Je.loadingText,children:"Loading preview..."})]})})}):R||P?s.jsx("div",{className:te,children:s.jsx("div",{className:Je.errorContainer,children:b||s.jsxs("div",{className:Je.errorDefault,children:[s.jsx("div",{className:Je.errorIcon,children:"⚠️"}),s.jsx("span",{className:Je.errorText,children:R||P||"Failed to load video"})]})})}):o?s.jsxs("div",{className:te,children:[s.jsxs("video",{ref:_,className:X,controls:p,autoPlay:y,loop:C,muted:v,onLoadedData:M,onError:F,onPlay:H,onPause:J,children:[s.jsx("source",{src:o,type:"video/mp4"}),"Your browser does not support the video tag."]}),g&&x&&s.jsx("div",{className:Je.overlay,children:x})]}):s.jsx("div",{className:te,children:s.jsxs("div",{className:Je.emptyContainer,children:[s.jsx("div",{className:Je.emptyIcon,children:"🎬"}),s.jsx("span",{className:Je.emptyText,children:"No preview available"})]})})},wg=()=>{const{preview:o,hasTimeline:l,generatePreview:i}=Kr();if(h.useEffect(()=>{l&&!o.isGenerating&&!o.url&&!o.error&&(console.log("🎬 Auto-generating initial preview..."),i())},[l]),!l)return s.jsx(Cd,{mode:"slideshow"});const c=s.jsxs("div",{className:"flex flex-col items-center gap-3",children:[s.jsx("div",{className:"w-8 h-8 border-3 border-dark-700 border-t-accent-orange rounded-full animate-spin"}),s.jsx("div",{className:"text-accent-orange text-sm font-bold font-mono",children:"Generating Preview..."})]}),d=o.error?s.jsxs("div",{className:"text-center text-accent-red font-mono",children:[s.jsx("div",{className:"text-lg mb-2",children:"Preview Error"}),s.jsx("div",{className:"text-sm mb-3",children:o.error}),s.jsx("button",{onClick:i,className:"btn-pink text-sm py-2 px-4",children:"Retry Preview"})]}):null;return s.jsx(Td,{videoSrc:o.url||"",mode:"slideshow",isLoading:o.isGenerating,error:o.error,loadingContent:c,errorContent:d,onVideoLoaded:()=>{console.log("🎬 Slideshow preview loaded")},onVideoError:f=>{console.error("❌ Slideshow preview error:",f)},showControls:!0,autoPlay:!0,loop:!0,muted:!0,containerClassName:"h-full"})},Sg=({src:o,alt:l,isDragged:i,onDragStart:c,onDragEnd:d})=>s.jsx("div",{className:"timeline-thumbnail",draggable:!0,onDragStart:c,onDragEnd:d,style:{width:"150px",height:"100px",borderRadius:"8px",overflow:"hidden",cursor:"grab",opacity:i?.5:1,transition:"all 0.2s ease",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.15)",border:"none"},children:s.jsx("img",{src:o,alt:l,style:{width:"100%",height:"100%",objectFit:"cover",pointerEvents:"none"}})}),kg=({duration:o,formatDuration:l,onChange:i})=>{const c=d=>{const f=parseFloat(d.target.value)*1e3;i(f)};return s.jsxs("div",{className:"duration-control",style:{marginTop:"8px"},children:[s.jsx("input",{type:"range",min:"0.5",max:"5",step:"0.1",value:o/1e3,onChange:c,style:{width:"130px",height:"4px",background:"#374151",borderRadius:"2px",outline:"none",cursor:"pointer"}}),s.jsx("div",{style:{fontSize:"11px",color:"#9ca3af",textAlign:"center",marginTop:"4px",fontFamily:'"Space Mono", monospace'},children:l(o)})]})},Cg=({onRemove:o,isVisible:l})=>s.jsx("button",{onClick:i=>{i.stopPropagation(),o()},style:{position:"absolute",top:"8px",right:"8px",width:"28px",height:"28px",backgroundColor:"#ef4444",border:"none",borderRadius:"50%",color:"white",fontSize:"14px",cursor:"pointer",display:"flex",alignItems:"center",justifyContent:"center",boxShadow:"0 2px 6px rgba(0,0,0,0.6)",opacity:l?.8:0,transition:"opacity 0.3s ease",zIndex:10},onMouseEnter:i=>i.currentTarget.style.opacity="1",onMouseLeave:i=>i.currentTarget.style.opacity=l?"0.8":"0",title:"Remove frame",children:"×"}),bg=({item:o,image:l,index:i,dragState:c,handlers:d,utilities:f})=>s.jsxs("div",{className:"timeline-item","data-item-id":o.id,"data-index":i,style:{display:"flex",flexDirection:"column",alignItems:"center",position:"relative",flexShrink:0,transform:c.isDraggedOver?"scale(1.02)":"scale(1)",transition:"all 0.2s ease"},onDragOver:d.drag.onDragOver,onDragLeave:d.drag.onDragLeave,onDrop:d.drag.onDrop,onMouseEnter:d.ui.onMouseEnter,onMouseLeave:d.ui.onMouseLeave,children:[s.jsx(Sg,{src:l.preview,alt:l.name,isDragged:c.isDragged,onDragStart:d.drag.onDragStart,onDragEnd:d.drag.onDragEnd}),s.jsx(kg,{duration:o.duration,formatDuration:f.formatDuration,onChange:d.actions.onDurationChange}),s.jsx(Cg,{onRemove:d.actions.onRemove,isVisible:c.isHovered})]}),jg=({type:o})=>{const l=()=>{switch(o){case"fade":case"fadeblack":case"fadewhite":return"🌅";case"slide":case"slideleft":case"slideright":case"slideup":case"slidedown":return"➡️";case"zoom":case"zoomin":return"🔍";case"dissolve":return"✨";case"cut":return"⚡";case"coverleft":case"coverright":case"coverup":case"coverdown":case"revealleft":case"revealright":case"revealup":case"revealdown":return"↗️";case"pixelize":case"distance":case"hblur":return"⚡";case"wipeleft":case"wiperight":case"wipeup":case"wipedown":case"wipetl":case"wipetr":case"wipebl":case"wipebr":return"◐";case"squeezev":case"squeezeh":return"⬌";default:return"🔄"}};return s.jsx("div",{style:{fontSize:"18px",marginBottom:"2px"},children:l()})},Eg=({text:o,isActive:l})=>s.jsx("div",{style:{fontSize:"10px",color:l?"#ec4899":"#6b7280",textAlign:"center",fontFamily:'"Space Mono", monospace',fontWeight:l?"bold":"normal",textTransform:"uppercase",letterSpacing:"0.5px"},children:o}),Ng=({fromItem:o,toItem:l,index:i,handlers:c})=>{const d=o.transition,f=!!d;return s.jsxs("div",{className:"transition-element","data-from-item":o.id,"data-to-item":l.id,"data-index":i,style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",position:"relative",width:"70px",height:"100px",background:f?"rgba(236, 72, 153, 0.1)":"rgba(55, 65, 81, 0.3)",border:f?"2px solid rgba(236, 72, 153, 0.5)":"2px solid #374151",borderRadius:"8px",cursor:"pointer",transition:"all 0.2s ease",flexShrink:0,boxShadow:"0 2px 8px rgba(0, 0, 0, 0.1)"},onClick:()=>c.onEdit(o.id),onMouseEnter:p=>{const g=p.currentTarget;f?(g.style.background="rgba(236, 72, 153, 0.2)",g.style.borderColor="#ec4899",g.style.transform="scale(1.02)",g.style.boxShadow="0 4px 12px rgba(236, 72, 153, 0.2)"):(g.style.background="rgba(55, 65, 81, 0.5)",g.style.borderColor="#6b7280",g.style.transform="scale(1.02)",g.style.boxShadow="0 4px 12px rgba(0, 0, 0, 0.15)")},onMouseLeave:p=>{const g=p.currentTarget;f?(g.style.background="rgba(236, 72, 153, 0.1)",g.style.borderColor="rgba(236, 72, 153, 0.5)"):(g.style.background="rgba(55, 65, 81, 0.3)",g.style.borderColor="#374151"),g.style.transform="scale(1)",g.style.boxShadow="0 2px 8px rgba(0, 0, 0, 0.1)"},children:[s.jsx(jg,{type:d==null?void 0:d.type}),s.jsx(Eg,{text:(d==null?void 0:d.type)||"none",isActive:f}),s.jsx("div",{style:{position:"absolute",right:"-10px",top:"50%",transform:"translateY(-50%)",width:"20px",height:"2px",background:f?"linear-gradient(to right, rgba(236, 72, 153, 0.5), rgba(236, 72, 153, 0.2))":"linear-gradient(to right, #374151, rgba(55, 65, 81, 0.3))",borderRadius:"1px"}})]})},Tg=({isOpen:o,onClose:l,onSave:i,currentTransition:c,frameNumber:d})=>{const[f,p]=h.useState((c==null?void 0:c.type)||"fade"),[g,x]=h.useState((c==null?void 0:c.duration)||500),[y,C]=h.useState("BÁSICAS"),[v,w]=h.useState(""),[D,T]=h.useState(!1),j=h.useRef(null);We.useEffect(()=>{if(j.current&&o){const P=j.current,S=P.getContext("2d");S&&(S.fillStyle="#1a1a1a",S.fillRect(0,0,P.width,P.height),S.fillStyle="#666666",S.font='bold 16px "Space Mono", monospace',S.textAlign="center",S.fillText("Click Play to preview",P.width/2,P.height/2-10),S.fillText(`${f} transition`,P.width/2,P.height/2+15))}},[o,f]);const R=[{name:"BÁSICAS",icon:"🎬",transitions:[{type:"fade",name:"Fade",description:"Transición suave de opacidad",recommendedDuration:"400-800ms",mood:"suave"},{type:"cut",name:"Cut",description:"Cambio instantáneo sin transición",recommendedDuration:"0ms",mood:"directo"},{type:"dissolve",name:"Dissolve",description:"Disolución granular entre imágenes",recommendedDuration:"500-1000ms",mood:"orgánico"},{type:"fadeblack",name:"Fade to Black",description:"Fundido a negro intermedio",recommendedDuration:"600-1200ms",mood:"dramático"},{type:"fadewhite",name:"Fade to White",description:"Fundido a blanco intermedio",recommendedDuration:"600-1200ms",mood:"limpio"}]},{name:"DESLIZAMIENTOS",icon:"↔️",transitions:[{type:"slideleft",name:"Slide Left",description:"Nueva imagen entra desde la izquierda",recommendedDuration:"300-600ms",mood:"dinámico"},{type:"slideright",name:"Slide Right",description:"Nueva imagen entra desde la derecha",recommendedDuration:"300-600ms",mood:"dinámico"},{type:"slideup",name:"Slide Up",description:"Nueva imagen entra desde abajo",recommendedDuration:"300-600ms",mood:"dinámico"},{type:"slidedown",name:"Slide Down",description:"Nueva imagen entra desde arriba",recommendedDuration:"300-600ms",mood:"dinámico"},{type:"smoothleft",name:"Smooth Left",description:"Deslizamiento suave hacia la izquierda",recommendedDuration:"400-700ms",mood:"suave"},{type:"smoothright",name:"Smooth Right",description:"Deslizamiento suave hacia la derecha",recommendedDuration:"400-700ms",mood:"suave"},{type:"smoothup",name:"Smooth Up",description:"Deslizamiento suave hacia arriba",recommendedDuration:"400-700ms",mood:"suave"},{type:"smoothdown",name:"Smooth Down",description:"Deslizamiento suave hacia abajo",recommendedDuration:"400-700ms",mood:"suave"}]},{name:"BARRIDOS",icon:"🔲",transitions:[{type:"wipeleft",name:"Wipe Left",description:"Barrido horizontal de izquierda a derecha",recommendedDuration:"400-800ms",mood:"técnico"},{type:"wiperight",name:"Wipe Right",description:"Barrido horizontal de derecha a izquierda",recommendedDuration:"400-800ms",mood:"técnico"},{type:"wipeup",name:"Wipe Up",description:"Barrido vertical de abajo hacia arriba",recommendedDuration:"400-800ms",mood:"técnico"},{type:"wipedown",name:"Wipe Down",description:"Barrido vertical de arriba hacia abajo",recommendedDuration:"400-800ms",mood:"técnico"},{type:"wipetl",name:"Wipe Top-Left",description:"Barrido diagonal desde esquina superior izquierda",recommendedDuration:"500-900ms",mood:"geométrico"},{type:"wipetr",name:"Wipe Top-Right",description:"Barrido diagonal desde esquina superior derecha",recommendedDuration:"500-900ms",mood:"geométrico"},{type:"wipebl",name:"Wipe Bottom-Left",description:"Barrido diagonal desde esquina inferior izquierda",recommendedDuration:"500-900ms",mood:"geométrico"},{type:"wipebr",name:"Wipe Bottom-Right",description:"Barrido diagonal desde esquina inferior derecha",recommendedDuration:"500-900ms",mood:"geométrico"},{type:"squeezeh",name:"Squeeze Horizontal",description:"Compresión horizontal progresiva",recommendedDuration:"600-1000ms",mood:"dramático"},{type:"squeezev",name:"Squeeze Vertical",description:"Compresión vertical progresiva",recommendedDuration:"600-1000ms",mood:"dramático"},{type:"hlslice",name:"Horizontal Left Slice",description:"Corte horizontal desde la izquierda",recommendedDuration:"400-700ms",mood:"técnico"},{type:"hrslice",name:"Horizontal Right Slice",description:"Corte horizontal desde la derecha",recommendedDuration:"400-700ms",mood:"técnico"}]},{name:"CIRCULARES",icon:"⭕",transitions:[{type:"circlecrop",name:"Circle Crop",description:"Recorte circular progresivo",recommendedDuration:"500-1000ms",mood:"orgánico"},{type:"circleopen",name:"Circle Open",description:"Apertura circular desde el centro",recommendedDuration:"500-1000ms",mood:"orgánico"},{type:"circleclose",name:"Circle Close",description:"Cierre circular hacia el centro",recommendedDuration:"500-1000ms",mood:"orgánico"},{type:"radial",name:"Radial",description:"Transición radial en forma de reloj",recommendedDuration:"600-1200ms",mood:"técnico"},{type:"rectcrop",name:"Rectangle Crop",description:"Recorte rectangular progresivo",recommendedDuration:"500-900ms",mood:"geométrico"},{type:"distance",name:"Distance",description:"Transición basada en distancia de píxeles",recommendedDuration:"600-1200ms",mood:"técnico"}]},{name:"APERTURAS/CIERRES",icon:"🪟",transitions:[{type:"vertopen",name:"Vertical Open",description:"Apertura vertical desde el centro",recommendedDuration:"500-900ms",mood:"geométrico"},{type:"vertclose",name:"Vertical Close",description:"Cierre vertical hacia el centro",recommendedDuration:"500-900ms",mood:"geométrico"},{type:"horzopen",name:"Horizontal Open",description:"Apertura horizontal desde el centro",recommendedDuration:"500-900ms",mood:"geométrico"},{type:"horzclose",name:"Horizontal Close",description:"Cierre horizontal hacia el centro",recommendedDuration:"500-900ms",mood:"geométrico"},{type:"coverleft",name:"Cover Left",description:"Nueva imagen cubre desde la izquierda",recommendedDuration:"400-800ms",mood:"limpio"},{type:"coverright",name:"Cover Right",description:"Nueva imagen cubre desde la derecha",recommendedDuration:"400-800ms",mood:"limpio"},{type:"coverup",name:"Cover Up",description:"Nueva imagen cubre desde abajo",recommendedDuration:"400-800ms",mood:"limpio"},{type:"coverdown",name:"Cover Down",description:"Nueva imagen cubre desde arriba",recommendedDuration:"400-800ms",mood:"limpio"}]},{name:"EFECTOS ESPECIALES",icon:"✨",transitions:[{type:"pixelize",name:"Pixelize",description:"Efecto de pixelación progresiva",recommendedDuration:"700-1400ms",mood:"digital"},{type:"hblur",name:"Horizontal Blur",description:"Desenfoque horizontal dinámico",recommendedDuration:"500-1000ms",mood:"artístico"},{type:"fadegrays",name:"Fade Grays",description:"Fundido con escalas de grises",recommendedDuration:"600-1200ms",mood:"artístico"},{type:"revealleft",name:"Reveal Left",description:"Revelado progresivo desde la izquierda",recommendedDuration:"500-900ms",mood:"dramático"},{type:"revealright",name:"Reveal Right",description:"Revelado progresivo desde la derecha",recommendedDuration:"500-900ms",mood:"dramático"},{type:"revealup",name:"Reveal Up",description:"Revelado progresivo desde abajo",recommendedDuration:"500-900ms",mood:"dramático"},{type:"revealdown",name:"Reveal Down",description:"Revelado progresivo desde arriba",recommendedDuration:"500-900ms",mood:"dramático"},{type:"zoomin",name:"Zoom In",description:"Nueva imagen aparece escalando desde el centro",recommendedDuration:"400-800ms",mood:"dinámico"}]},{name:"DINÁMICAS",icon:"⚡",transitions:[{type:"hlwind",name:"Horizontal Left Wind",description:"Efecto de viento horizontal izquierdo",recommendedDuration:"600-1000ms",mood:"dinámico"},{type:"hrwind",name:"Horizontal Right Wind",description:"Efecto de viento horizontal derecho",recommendedDuration:"600-1000ms",mood:"dinámico"},{type:"vuwind",name:"Vertical Up Wind",description:"Efecto de viento vertical ascendente",recommendedDuration:"600-1000ms",mood:"dinámico"},{type:"vdwind",name:"Vertical Down Wind",description:"Efecto de viento vertical descendente",recommendedDuration:"600-1000ms",mood:"dinámico"},{type:"diagtl",name:"Diagonal Top-Left",description:"Transición diagonal superior izquierda",recommendedDuration:"500-800ms",mood:"geométrico"},{type:"diagtr",name:"Diagonal Top-Right",description:"Transición diagonal superior derecha",recommendedDuration:"500-800ms",mood:"geométrico"},{type:"diagbl",name:"Diagonal Bottom-Left",description:"Transición diagonal inferior izquierda",recommendedDuration:"500-800ms",mood:"geométrico"},{type:"diagbr",name:"Diagonal Bottom-Right",description:"Transición diagonal inferior derecha",recommendedDuration:"500-800ms",mood:"geométrico"},{type:"vuslice",name:"Vertical Up Slice",description:"Corte vertical ascendente",recommendedDuration:"400-700ms",mood:"técnico"},{type:"vdslice",name:"Vertical Down Slice",description:"Corte vertical descendente",recommendedDuration:"400-700ms",mood:"técnico"}]},{name:"VARIACIONES",icon:"🔄",transitions:[{type:"fadefast",name:"Fade Fast",description:"Fundido rápido y directo",recommendedDuration:"200-400ms",mood:"rápido"},{type:"fadeslow",name:"Fade Slow",description:"Fundido lento y contemplativo",recommendedDuration:"1000-2000ms",mood:"contemplativo"},{type:"zoomout",name:"Zoom Out",description:"Efecto de zoom alejándose (fallback a zoomin)",recommendedDuration:"400-800ms",mood:"dinámico"},{type:"vblur",name:"Vertical Blur",description:"Desenfoque vertical (fallback a hblur)",recommendedDuration:"500-1000ms",mood:"artístico"},{type:"slide",name:"Slide",description:"Deslizamiento genérico (fallback a slideleft)",recommendedDuration:"300-600ms",mood:"dinámico"},{type:"zoom",name:"Zoom",description:"Zoom genérico (fallback a zoomin)",recommendedDuration:"400-800ms",mood:"dinámico"}]}],b=R.map(P=>({...P,transitions:P.transitions.filter(S=>S.name.toLowerCase().includes(v.toLowerCase())||S.description.toLowerCase().includes(v.toLowerCase())||S.mood.toLowerCase().includes(v.toLowerCase()))})).filter(P=>P.transitions.length>0),_=R.flatMap(P=>P.transitions).find(P=>P.type===f),W=()=>{if(!j.current)return;const P=j.current,S=P.getContext("2d");if(!S)return;T(!0),S.clearRect(0,0,P.width,P.height),S.clearRect(0,0,P.width,P.height);const M=()=>{S.fillStyle="#2563eb",S.fillRect(0,0,P.width,P.height),S.fillStyle="#ffffff",S.font='bold 20px "Space Mono", monospace',S.textAlign="center",S.fillText("Frame A",P.width/2,P.height/2)},F=()=>{S.fillStyle="#22c55e",S.fillRect(0,0,P.width,P.height),S.fillStyle="#ffffff",S.font='bold 20px "Space Mono", monospace',S.textAlign="center",S.fillText("Frame B",P.width/2,P.height/2)};(J=>{const X=Date.now(),ae=()=>{const Z=Date.now()-X,K=Math.min(Z/2e3,1);switch(S.clearRect(0,0,P.width,P.height),S.globalAlpha=1,S.globalCompositeOperation="source-over",J){case"fade":M(),S.globalAlpha=K,F();break;case"slideleft":M(),S.save(),S.translate(P.width*(1-K),0),F(),S.restore();break;case"slideright":M(),S.save(),S.translate(-P.width*(1-K),0),F(),S.restore();break;case"slideup":M(),S.save(),S.translate(0,P.height*(1-K)),F(),S.restore();break;case"slidedown":M(),S.save(),S.translate(0,-P.height*(1-K)),F(),S.restore();break;case"wipeleft":M(),S.save(),S.beginPath(),S.rect(0,0,P.width*K,P.height),S.clip(),F(),S.restore();break;case"wiperight":M(),S.save(),S.beginPath(),S.rect(P.width*(1-K),0,P.width*K,P.height),S.clip(),F(),S.restore();break;case"wipeup":M(),S.save(),S.beginPath(),S.rect(0,P.height*(1-K),P.width,P.height*K),S.clip(),F(),S.restore();break;case"wipedown":M(),S.save(),S.beginPath(),S.rect(0,0,P.width,P.height*K),S.clip(),F(),S.restore();break;case"circleopen":M(),S.save(),S.beginPath();const fe=Math.sqrt(P.width*P.width+P.height*P.height)/2;S.arc(P.width/2,P.height/2,fe*K,0,2*Math.PI),S.clip(),F(),S.restore();break;case"circleclose":F(),S.save(),S.beginPath();const se=Math.sqrt(P.width*P.width+P.height*P.height)/2;S.arc(P.width/2,P.height/2,se*(1-K),0,2*Math.PI),S.clip(),M(),S.restore();break;case"zoomin":M(),S.save();const A=K;S.translate(P.width/2,P.height/2),S.scale(A,A),S.translate(-P.width/2,-P.height/2),F(),S.restore();break;case"pixelize":const O=Math.max(1,20*(1-K));M(),S.save(),S.imageSmoothingEnabled=!1;for(let U=0;U<P.width;U+=O)for(let re=0;re<P.height;re+=O)Math.random()<K&&(S.fillStyle="#22c55e",S.fillRect(U,re,O,O));K>.3&&(S.fillStyle="#ffffff",S.font='bold 20px "Space Mono", monospace',S.textAlign="center",S.fillText("Frame B",P.width/2,P.height/2)),S.restore();break;case"dissolve":M(),S.globalAlpha=K,F();const B=S.getImageData(0,0,P.width,P.height),E=B.data;for(let U=0;U<E.length;U+=4)Math.random()<(1-K)*.3&&(E[U]=37,E[U+1]=99,E[U+2]=235);S.putImageData(B,0,0);break;default:M(),S.globalAlpha=K,F();break}K<1?requestAnimationFrame(ae):setTimeout(()=>{S.clearRect(0,0,P.width,P.height),S.fillStyle="#1a1a1a",S.fillRect(0,0,P.width,P.height),S.fillStyle="#666666",S.font='bold 16px "Space Mono", monospace',S.textAlign="center",S.fillText("Click Play to preview",P.width/2,P.height/2),T(!1)},500)};ae()})(f)},V=()=>{i({type:f,duration:g}),l()};return o?s.jsx("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.8)",display:"flex",alignItems:"center",justifyContent:"center",zIndex:1e3},children:s.jsxs("div",{style:{backgroundColor:"#0f0f0f",border:"1px solid #2a2a2b",borderRadius:"8px",width:"1000px",maxHeight:"85vh",display:"flex",flexDirection:"column"},children:[s.jsxs("div",{style:{padding:"16px 20px",borderBottom:"1px solid #2a2a2b",display:"flex",justifyContent:"space-between",alignItems:"center",flexShrink:0},children:[s.jsxs("h3",{style:{margin:0,fontSize:"18px",color:"#ffffff",fontWeight:"bold",fontFamily:'"Space Mono", monospace'},children:["🎬 Transition Settings (Frame ",d,")"]}),s.jsx("button",{onClick:l,style:{background:"none",border:"none",color:"#999999",fontSize:"18px",cursor:"pointer",padding:"4px 8px",borderRadius:"4px",fontWeight:"normal"},onMouseEnter:P=>P.currentTarget.style.backgroundColor="#2a2a2b",onMouseLeave:P=>P.currentTarget.style.backgroundColor="transparent",children:"✕"})]}),s.jsxs("div",{style:{flex:1,display:"flex",minHeight:0},children:[s.jsxs("div",{style:{width:"400px",borderRight:"1px solid #2a2a2b",display:"flex",flexDirection:"column"},children:[s.jsx("div",{style:{padding:"16px 20px",borderBottom:"1px solid #2a2a2b"},children:s.jsx("input",{type:"text",placeholder:"🔍 Search transitions...",value:v,onChange:P=>w(P.target.value),style:{width:"100%",padding:"8px 12px",backgroundColor:"#1a1a1a",border:"1px solid #333333",borderRadius:"4px",color:"#ffffff",fontSize:"14px",fontFamily:'"Space Mono", monospace',outline:"none"}})}),s.jsx("div",{style:{flex:1,overflowY:"auto",padding:"16px 20px"},children:b.map(P=>s.jsxs("div",{style:{marginBottom:"16px"},children:[s.jsxs("div",{onClick:()=>{C(y===P.name?"":P.name)},style:{display:"flex",alignItems:"center",cursor:"pointer",padding:"8px 0",borderBottom:"1px solid #2a2a2b",marginBottom:"8px"},children:[s.jsx("span",{style:{marginRight:"8px",fontSize:"14px"},children:P.icon}),s.jsx("span",{style:{marginRight:"8px",fontSize:"12px",color:"#999999",transform:y===P.name?"rotate(90deg)":"rotate(0deg)",transition:"transform 0.2s ease"},children:"▶"}),s.jsx("span",{style:{fontSize:"14px",fontWeight:"bold",color:"#ffffff",fontFamily:'"Space Mono", monospace',flex:1},children:P.name}),s.jsxs("span",{style:{fontSize:"12px",color:"#666666",fontFamily:'"Space Mono", monospace'},children:["(",P.transitions.length,")"]})]}),y===P.name&&s.jsx("div",{style:{marginLeft:"16px"},children:P.transitions.map(S=>s.jsxs("div",{onClick:()=>p(S.type),style:{display:"flex",alignItems:"center",padding:"10px 12px",marginBottom:"4px",backgroundColor:f===S.type?"#1a1a1a":"transparent",border:f===S.type?"1px solid #333333":"1px solid transparent",borderRadius:"4px",cursor:"pointer",transition:"all 0.2s ease"},onMouseEnter:M=>{f!==S.type&&(M.currentTarget.style.backgroundColor="#161616")},onMouseLeave:M=>{f!==S.type&&(M.currentTarget.style.backgroundColor="transparent")},children:[s.jsx("div",{style:{width:"12px",height:"12px",borderRadius:"50%",backgroundColor:f===S.type?"#4CAF50":"#333333",marginRight:"12px",border:"2px solid",borderColor:f===S.type?"#4CAF50":"#555555"}}),s.jsxs("div",{style:{flex:1},children:[s.jsx("div",{style:{color:"#ffffff",fontSize:"14px",fontWeight:"bold",marginBottom:"2px",fontFamily:'"Space Mono", monospace'},children:S.name}),s.jsx("div",{style:{color:"#999999",fontSize:"12px",marginBottom:"4px",fontFamily:'"Space Mono", monospace'},children:S.description}),s.jsxs("div",{style:{color:"#666666",fontSize:"11px",fontFamily:'"Space Mono", monospace',fontStyle:"italic"},children:["🎭 ",S.mood," • ⏱️ ",S.recommendedDuration]})]})]},S.type))})]},P.name))})]}),s.jsxs("div",{style:{flex:1,padding:"20px",display:"flex",flexDirection:"column"},children:[s.jsxs("div",{style:{backgroundColor:"#1a1a1a",border:"1px solid #333333",borderRadius:"8px",padding:"20px",marginBottom:"20px",display:"flex",flexDirection:"column",alignItems:"center",minHeight:"300px"},children:[s.jsx("canvas",{ref:j,width:400,height:225,style:{border:"1px solid #555555",borderRadius:"8px",backgroundColor:"#0a0a0a",marginBottom:"20px",maxWidth:"100%",height:"auto"}}),s.jsx("button",{onClick:W,disabled:D,style:{padding:"12px 24px",backgroundColor:D?"#333333":"#4CAF50",color:"#ffffff",border:"none",borderRadius:"6px",cursor:D?"not-allowed":"pointer",fontSize:"16px",fontFamily:'"Space Mono", monospace',fontWeight:"bold",transition:"all 0.2s ease"},onMouseEnter:P=>{D||(P.currentTarget.style.backgroundColor="#45a049",P.currentTarget.style.transform="scale(1.02)")},onMouseLeave:P=>{D||(P.currentTarget.style.backgroundColor="#4CAF50",P.currentTarget.style.transform="scale(1)")},children:D?"⏸️ Playing...":"▶️ Play Preview"})]}),_&&s.jsxs("div",{style:{backgroundColor:"#1a1a1a",border:"1px solid #333333",borderRadius:"8px",padding:"16px",marginBottom:"20px"},children:[s.jsxs("h4",{style:{color:"#ffffff",fontSize:"16px",marginBottom:"12px",fontFamily:'"Space Mono", monospace'},children:["ℹ️ ",_.name]}),s.jsx("p",{style:{color:"#cccccc",fontSize:"14px",marginBottom:"8px",fontFamily:'"Space Mono", monospace'},children:_.description}),s.jsxs("div",{style:{display:"flex",gap:"16px",fontSize:"12px",color:"#999999",fontFamily:'"Space Mono", monospace'},children:[s.jsxs("span",{children:["🎭 ",_.mood]}),s.jsxs("span",{children:["⏱️ ",_.recommendedDuration]})]})]}),s.jsxs("div",{style:{backgroundColor:"#1a1a1a",border:"1px solid #333333",borderRadius:"8px",padding:"16px",marginBottom:"20px"},children:[s.jsx("h4",{style:{color:"#ffffff",fontSize:"16px",marginBottom:"12px",fontFamily:'"Space Mono", monospace'},children:"⏱️ Duration"}),s.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"12px"},children:[s.jsxs("span",{style:{color:"#cccccc",fontSize:"14px",fontFamily:'"Space Mono", monospace'},children:[g,"ms"]}),s.jsx("input",{type:"range",min:"100",max:"2000",value:g,onChange:P=>x(parseInt(P.target.value)),style:{flex:1,height:"4px",backgroundColor:"#333333",outline:"none",borderRadius:"2px"}})]}),s.jsx("div",{style:{display:"flex",gap:"8px",marginTop:"8px"},children:[250,500,750,1e3,1500].map(P=>s.jsxs("button",{onClick:()=>x(P),style:{padding:"4px 8px",backgroundColor:g===P?"#4CAF50":"#333333",color:"#ffffff",border:"none",borderRadius:"2px",fontSize:"12px",cursor:"pointer",fontFamily:'"Space Mono", monospace'},children:[P,"ms"]},P))})]})]})]}),s.jsxs("div",{style:{padding:"16px 20px",borderTop:"1px solid #2a2a2b",display:"flex",justifyContent:"flex-end",gap:"12px",flexShrink:0},children:[s.jsx("button",{onClick:l,style:{padding:"8px 16px",backgroundColor:"transparent",border:"1px solid #555555",borderRadius:"4px",color:"#cccccc",cursor:"pointer",fontSize:"14px",fontFamily:'"Space Mono", monospace'},onMouseEnter:P=>P.currentTarget.style.backgroundColor="#2a2a2b",onMouseLeave:P=>P.currentTarget.style.backgroundColor="transparent",children:"Cancel"}),s.jsx("button",{onClick:V,style:{padding:"8px 16px",backgroundColor:"#4CAF50",border:"none",borderRadius:"4px",color:"#ffffff",cursor:"pointer",fontSize:"14px",fontFamily:'"Space Mono", monospace',fontWeight:"bold"},onMouseEnter:P=>P.currentTarget.style.backgroundColor="#45a049",onMouseLeave:P=>P.currentTarget.style.backgroundColor="#4CAF50",children:"Apply Transition"})]})]})}):null},ye={NO_IMAGES:"NO_IMAGES",INVALID_DURATION:"INVALID_DURATION",INVALID_TRANSITION_DURATION:"INVALID_TRANSITION_DURATION",INVALID_AUDIO_CONFIG:"INVALID_AUDIO_CONFIG",SHORT_DURATION:"SHORT_DURATION",LONG_DURATION:"LONG_DURATION",NO_TRANSITIONS:"NO_TRANSITIONS",NO_AUDIO:"NO_AUDIO",OPTIMAL_CONFIG:"OPTIMAL_CONFIG",PERFORMANCE_TIP:"PERFORMANCE_TIP"},Rg={[ye.NO_IMAGES]:"Debes agregar al menos una imagen para crear el slideshow",[ye.INVALID_DURATION]:"La duración debe ser mayor a 0 segundos",[ye.INVALID_TRANSITION_DURATION]:"La duración de transición debe ser válida",[ye.INVALID_AUDIO_CONFIG]:"La configuración de audio no es válida",[ye.SHORT_DURATION]:"Duración muy corta, considera aumentarla",[ye.LONG_DURATION]:"Duración muy larga, puede afectar el rendimiento",[ye.NO_TRANSITIONS]:"Sin transiciones configuradas",[ye.NO_AUDIO]:"Sin audio configurado",[ye.OPTIMAL_CONFIG]:"Configuración óptima",[ye.PERFORMANCE_TIP]:"Tip: configuración optimizada para mejor rendimiento"},De=(o,l,i,c)=>({type:o,field:l,code:i,message:c||Rg[i]}),Ig=o=>({canExport:o.canExport,messages:o.messages.filter(l=>l.type==="error"||l.type==="warning").map(l=>({type:l.type,message:l.message}))}),Mg=o=>({isExporting:o.isExporting,isCompleted:o.isCompleted,progress:o.progress,status:o.isExporting?o.currentStep||"Exportando...":o.isCompleted?"Export completado":"Listo para exportar",error:o.error,lastResult:o.lastResult,currentStep:o.currentStep,downloadUrl:o.downloadUrl,filename:o.filename,exportUrl:o.downloadUrl}),Pg=({exportState:o,validation:l,currentFormat:i,onExport:c})=>{const d=Mg(o),f=Ig(l),p=()=>d.isExporting?"⏳ Exporting...":f.canExport?`🚀 Export ${i.toUpperCase()}`:"❌ Invalid",g=()=>f.canExport?`Export slideshow as ${i.toUpperCase()}`:`Invalid configuration: ${f.messages.filter(y=>y.type==="error").map(y=>y.message).join(", ")}`;return s.jsx("button",{onClick:c,disabled:d.isExporting||!f.canExport,className:`
        absolute bottom-4 right-4 z-20
        px-4 py-3 rounded-lg font-mono font-bold text-sm uppercase
        transition-all duration-200 shadow-lg
        min-w-[120px] max-w-[200px]
        ${d.isExporting||!f.canExport?"bg-dark-600 cursor-not-allowed opacity-70 text-dark-400":"bg-accent-pink hover:bg-accent-pink-dark text-white shadow-glow-pink hover:scale-105"}
      `,title:g(),style:{position:"absolute",bottom:"16px",right:"16px",zIndex:20},children:p()})},Da=o=>h.useMemo(()=>{const l=[];switch(o.format){case"gif":Dg(o,l);break;case"mp4":_g(o,l);break;case"webm":Lg(o,l);break;case"mov":zg(o,l);break}Fg(o,l);const i=l.some(d=>d.type==="error"),c=l.some(d=>d.type==="warning");return{isValid:!i,canExport:!i,messages:l,hasErrors:i,hasWarnings:c}},[o]);function Dg(o,l){var i;if(o.fps&&(o.fps>50?l.push(De("error","fps",ye.INVALID_DURATION,"FPS para GIF debe ser 50 o menor para mejor compatibilidad")):o.fps>30?l.push(De("warning","fps",ye.PERFORMANCE_TIP,"FPS mayor a 30 puede resultar en archivos GIF muy grandes")):o.fps<10&&l.push(De("warning","fps",ye.PERFORMANCE_TIP,"FPS menor a 10 puede resultar en animación entrecortada"))),o.resolution){const{width:c,height:d}=o.resolution,f=c*d;(c>1920||d>1080)&&l.push(De("warning","resolution",ye.PERFORMANCE_TIP,"Resolución alta puede resultar en archivos GIF muy grandes")),f>2073600&&l.push(De("warning","resolution",ye.PERFORMANCE_TIP,"Resolución muy alta puede causar problemas de rendimiento"))}(i=o.gif)!=null&&i.colors&&(o.gif.colors<16?l.push(De("warning","colors",ye.PERFORMANCE_TIP,"Menos de 16 colores puede resultar en calidad muy baja")):o.gif.colors>256&&l.push(De("error","colors",ye.INVALID_AUDIO_CONFIG,"GIF no puede tener más de 256 colores")))}function _g(o,l){if(o.fps&&(o.fps>60?l.push(De("error","fps",ye.INVALID_DURATION,"FPS para MP4 debe ser 60 o menor")):o.fps<1&&l.push(De("error","fps",ye.INVALID_DURATION,"FPS debe ser al menos 1"))),o.resolution){const{width:i,height:c}=o.resolution;(i<128||c<128)&&l.push(De("error","resolution",ye.INVALID_DURATION,"Resolución debe ser al menos 128x128")),(i>4096||c>4096)&&l.push(De("error","resolution",ye.INVALID_DURATION,"Resolución no puede exceder 4096x4096"))}}function Lg(o,l){if(o.fps&&(o.fps>60?l.push(De("error","fps",ye.INVALID_DURATION,"FPS para WebM debe ser 60 o menor")):o.fps<1&&l.push(De("error","fps",ye.INVALID_DURATION,"FPS debe ser al menos 1"))),o.resolution){const{width:i,height:c}=o.resolution;(i<128||c<128)&&l.push(De("error","resolution",ye.INVALID_DURATION,"Resolución debe ser al menos 128x128")),(i>4096||c>4096)&&l.push(De("error","resolution",ye.INVALID_DURATION,"Resolución no puede exceder 4096x4096"))}}function zg(o,l){if(o.fps&&(o.fps>120?l.push(De("error","fps",ye.INVALID_DURATION,"FPS para MOV debe ser 120 o menor")):o.fps<1&&l.push(De("error","fps",ye.INVALID_DURATION,"FPS debe ser al menos 1"))),o.resolution){const{width:i,height:c}=o.resolution;(i<128||c<128)&&l.push(De("error","resolution",ye.INVALID_DURATION,"Resolución debe ser al menos 128x128")),(i>7680||c>4320)&&l.push(De("warning","resolution",ye.PERFORMANCE_TIP,"Resolución muy alta (8K) puede causar problemas de rendimiento"))}}function Fg(o,l){if(o.quality&&["web","standard","high","premium","ultra"].indexOf(o.quality)===-1&&l.push(De("error","quality",ye.INVALID_AUDIO_CONFIG,"Calidad no válida")),o.resolution){const{width:i,height:c}=o.resolution;(i<=0||c<=0)&&l.push(De("error","resolution",ye.INVALID_DURATION,"Resolución debe ser mayor a 0")),(i<240||c<240)&&l.push(De("warning","resolution",ye.PERFORMANCE_TIP,"Resolución muy baja puede resultar en calidad pobre"))}}const xa={isOpen:!1,itemId:"",frameNumber:0},Og=()=>{var se;const{project:o,hasTimeline:l,updateTimelineItem:i,removeFromTimeline:c,reorderTimeline:d,export:f,exportSlideshow:p}=Kr(),[g,x]=h.useState(null),[y,C]=h.useState(null),[v,w]=h.useState(new Set),[D,T]=h.useState(xa),j=h.useCallback(A=>`${(A/1e3).toFixed(1)}s`,[]),R=h.useMemo(()=>o.timeline.reduce((A,O)=>{let B=O.duration;return O.transition&&O.transition.duration&&(B+=O.transition.duration),A+B},0),[o.timeline]);h.useEffect(()=>{const A=document.getElementById("timeline-duration");A&&(A.textContent=`${(R/1e3).toFixed(1)}s`),window.__timelineData=o.timeline.map(O=>{var E,U;const B=o.images.find(re=>re.id===O.imageId);return{file:B==null?void 0:B.file,uploadedFile:B==null?void 0:B.uploadedInfo,duration:O.duration,transition:{type:((E=O.transition)==null?void 0:E.type)||"none",duration:((U=O.transition)==null?void 0:U.duration)||0}}})},[R,o.timeline,o.images]);const b={format:o.exportSettings.format,fps:o.exportSettings.fps,quality:o.exportSettings.quality,resolution:o.exportSettings.resolution,gif:o.exportSettings.gif},_=Da(b),W=h.useMemo(()=>{const A=[];return o.timeline.forEach((O,B)=>{A.push({type:"image",id:O.id,data:O,index:B}),B<o.timeline.length-1&&A.push({type:"transition",id:`transition-${O.id}`,data:{id:`transition-${O.id}`,fromItemId:O.id,toItemId:o.timeline[B+1].id,config:O.transition},index:B})}),A},[o.timeline]),V=h.useCallback(A=>{const O=o.timeline.findIndex(B=>B.id===A);return{isDragged:g===A,isDraggedOver:y===O,isHovered:v.has(A)}},[g,y,v,o.timeline]),P=h.useCallback((A,O)=>{x(O),A.dataTransfer.effectAllowed="move",A.dataTransfer.setData("text/plain",O)},[]),S=h.useCallback(()=>{x(null),C(null)},[]),M=h.useCallback((A,O)=>{A.preventDefault(),A.dataTransfer.dropEffect="move",C(O)},[]),F=h.useCallback(()=>{C(null)},[]),H=h.useCallback((A,O)=>{A.preventDefault();const B=A.dataTransfer.getData("text/plain");if(!B)return;const E=o.timeline.findIndex(he=>he.id===B);if(E===-1||E===O)return;const U=[...o.timeline],[re]=U.splice(E,1);U.splice(O,0,re);const pe=U.map((he,ge)=>({...he,position:ge}));d(pe),x(null),C(null)},[g,o.timeline,d]),J=h.useCallback(A=>{w(O=>new Set(O).add(A))},[]),te=h.useCallback(A=>{w(O=>{const B=new Set(O);return B.delete(A),B})},[]),X=h.useCallback(A=>{c(A)},[c]),ae=h.useCallback((A,O)=>{i(A,{duration:O})},[i]),Z=h.useCallback(A=>{const O=o.timeline.findIndex(B=>B.id===A);T({isOpen:!0,itemId:A,frameNumber:O+1})},[o.timeline]),K=h.useCallback(()=>{if(!_.canExport){const A=_.messages.filter(O=>O.type==="error").map(O=>O.message).join(`
`);alert(`Export configuration invalid:
${A}`);return}p()},[_,p]),fe=h.useCallback((A,O)=>({drag:{onDragStart:re=>P(re,A.id),onDragEnd:S,onDragOver:re=>M(re,O),onDragLeave:F,onDrop:re=>H(re,O)},actions:{onRemove:()=>X(A.id),onDurationChange:re=>ae(A.id,re)},ui:{onMouseEnter:()=>J(A.id),onMouseLeave:()=>te(A.id)}}),[P,S,M,F,H,X,ae,J,te]);return l?s.jsxs("div",{className:"timeline-container",style:{position:"relative",display:"flex",flexDirection:"column",gap:"16px",padding:"16px",backgroundColor:"#0a0a0b",borderRadius:"8px",border:"1px solid #374151"},children:[s.jsx("div",{className:"timeline-track",style:{display:"flex",alignItems:"center",gap:"12px",overflowX:"auto",overflowY:"hidden",padding:"8px 0",minHeight:"140px"},children:W.map(A=>{if(A.type==="image"){const O=A.data,B=o.images.find(E=>E.id===O.imageId);return B?s.jsx(bg,{item:O,image:B,index:A.index,dragState:V(O.id),handlers:fe(O,A.index),utilities:{formatDuration:j}},A.id):null}else{const O=A.data,B=o.timeline.find(U=>U.id===O.fromItemId),E=o.timeline.find(U=>U.id===O.toItemId);return B&&E?s.jsx(Ng,{fromItem:B,toItem:E,index:A.index,handlers:{onEdit:Z}},A.id):null}})}),l&&s.jsx(Pg,{exportState:f,validation:_,currentFormat:o.exportSettings.format,onExport:K}),s.jsx(Tg,{isOpen:D.isOpen,onClose:()=>T(xa),onSave:A=>{i(D.itemId,{transition:A}),T(xa)},currentTransition:(se=o.timeline.find(A=>A.id===D.itemId))==null?void 0:se.transition,frameNumber:D.frameNumber})]}):s.jsx(bd,{mode:"slideshow"})},Ag=({isVisible:o,format:l,progress:i,error:c,isCompleted:d=!1,downloadUrl:f,currentStep:p,onCancel:g,onDownload:x})=>{const[y,C]=h.useState(0),[v,w]=h.useState(null),[D,T]=h.useState("Preparing...");h.useEffect(()=>{if(!o){C(0),w(null),T("Preparing...");return}const b=Date.now(),_=setInterval(()=>{const W=Math.floor((Date.now()-b)/1e3);if(C(W),i>0&&i<100){const V=W/i*100,P=Math.max(0,V-W);w(Math.floor(P))}p?T(p):i===0?T("Initializing export..."):i<40?T("Generating high-quality preview (1080p)..."):i<70?T("Converting to final format..."):i<95?T("Optimizing output..."):i<100?T("Finalizing..."):T("Complete!")},1e3);return()=>clearInterval(_)},[o,i]);const j=b=>{const _=Math.floor(b/60),W=b%60;return`${_}:${W.toString().padStart(2,"0")}`},R=()=>c?"#dc2626":i>=100?"#22c55e":"#ec4899";return o?s.jsx("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.8)",display:"flex",alignItems:"center",justifyContent:"center",zIndex:1e3,backdropFilter:"blur(4px)"},children:s.jsxs("div",{style:{backgroundColor:"#000000",borderRadius:"12px",padding:"32px",width:"400px",maxWidth:"90vw",border:"1px solid #333333",boxShadow:"0 20px 25px -5px rgba(0, 0, 0, 0.3)"},children:[s.jsxs("div",{style:{textAlign:"center",marginBottom:"24px"},children:[s.jsxs("div",{style:{fontSize:"20px",fontWeight:"bold",color:"#f3f4f6",marginBottom:"8px",fontFamily:'"Space Mono", monospace'},children:["Exporting ",l.toUpperCase()]}),s.jsx("div",{style:{fontSize:"14px",color:"#9ca3af",fontFamily:'"Space Mono", monospace'},children:c?"Export Failed":D})]}),s.jsx("div",{style:{display:"flex",justifyContent:"center",marginBottom:"24px"},children:s.jsxs("div",{style:{position:"relative",width:"120px",height:"120px"},children:[s.jsxs("svg",{width:"120",height:"120",style:{transform:"rotate(-90deg)"},children:[s.jsx("circle",{cx:"60",cy:"60",r:"54",stroke:"#333333",strokeWidth:"8",fill:"transparent"}),s.jsx("circle",{cx:"60",cy:"60",r:"54",stroke:R(),strokeWidth:"8",fill:"transparent",strokeDasharray:`${2*Math.PI*54}`,strokeDashoffset:`${2*Math.PI*54*(1-i/100)}`,style:{transition:"stroke-dashoffset 0.5s ease",strokeLinecap:"round"}})]}),s.jsxs("div",{style:{position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)",fontSize:"24px",fontWeight:"bold",color:"#f3f4f6",fontFamily:'"Space Mono", monospace'},children:[Math.round(i),"%"]})]})}),s.jsxs("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr",gap:"16px",marginBottom:"24px"},children:[s.jsxs("div",{style:{textAlign:"center",padding:"12px",backgroundColor:"#1a1a1a",borderRadius:"8px"},children:[s.jsx("div",{style:{fontSize:"10px",color:"#9ca3af",marginBottom:"4px",fontFamily:'"Space Mono", monospace'},children:"TIME ELAPSED"}),s.jsx("div",{style:{fontSize:"16px",color:"#f3f4f6",fontWeight:"bold",fontFamily:'"Space Mono", monospace'},children:j(y)})]}),s.jsxs("div",{style:{textAlign:"center",padding:"12px",backgroundColor:"#1a1a1a",borderRadius:"8px"},children:[s.jsx("div",{style:{fontSize:"10px",color:"#9ca3af",marginBottom:"4px",fontFamily:'"Space Mono", monospace'},children:"EST. REMAINING"}),s.jsx("div",{style:{fontSize:"16px",color:"#f3f4f6",fontWeight:"bold",fontFamily:'"Space Mono", monospace'},children:v!==null?j(v):"--:--"})]})]}),c&&s.jsxs("div",{style:{padding:"12px",backgroundColor:"#7f1d1d",border:"1px solid #dc2626",borderRadius:"8px",marginBottom:"16px"},children:[s.jsx("div",{style:{fontSize:"12px",color:"#fca5a5",fontFamily:'"Space Mono", monospace',marginBottom:"4px",fontWeight:"bold"},children:"Export Error:"}),s.jsx("div",{style:{fontSize:"11px",color:"#f87171",fontFamily:'"Space Mono", monospace'},children:c})]}),i<100&&!c&&!d&&g&&s.jsx("button",{onClick:g,style:{width:"100%",padding:"12px",backgroundColor:"#2a2a2a",border:"1px solid #555555",borderRadius:"8px",color:"#d1d5db",fontSize:"12px",fontWeight:"bold",cursor:"pointer",fontFamily:'"Space Mono", monospace',transition:"all 0.2s ease"},onMouseEnter:b=>{b.currentTarget.style.backgroundColor="#404040",b.currentTarget.style.color="#f3f4f6"},onMouseLeave:b=>{b.currentTarget.style.backgroundColor="#2a2a2a",b.currentTarget.style.color="#d1d5db"},children:"CANCEL EXPORT"}),(d||c)&&s.jsxs("div",{style:{display:"flex",gap:"12px",flexDirection:d&&f?"column":"row"},children:[d&&f&&x&&s.jsx("button",{onClick:x,style:{flex:1,padding:"12px",backgroundColor:"#22c55e",border:"none",borderRadius:"8px",color:"white",fontSize:"12px",fontWeight:"bold",cursor:"pointer",fontFamily:'"Space Mono", monospace',transition:"all 0.2s ease"},onMouseEnter:b=>{b.currentTarget.style.backgroundColor="#16a34a"},onMouseLeave:b=>{b.currentTarget.style.backgroundColor="#22c55e"},children:"🔽 DOWNLOAD FILE"}),s.jsx("button",{onClick:g,style:{flex:1,padding:"12px",backgroundColor:d?"#6b7280":"#dc2626",border:"none",borderRadius:"8px",color:"white",fontSize:"12px",fontWeight:"bold",cursor:"pointer",fontFamily:'"Space Mono", monospace',transition:"all 0.2s ease"},onMouseEnter:b=>{b.currentTarget.style.backgroundColor=d?"#4b5563":"#b91c1c"},onMouseLeave:b=>{b.currentTarget.style.backgroundColor=d?"#6b7280":"#dc2626"},children:d?"✅ CLOSE":"❌ CLOSE"})]})]})}):null};class Rd{createContainer(l,i,c="#ec4899"){return{marginBottom:"16px",padding:"12px",backgroundColor:"#0f172a",border:`1px solid ${c}`,borderRadius:"6px"}}createSectionTitle(l,i,c="#ec4899"){return{fontSize:"11px",color:c,fontWeight:"bold",marginBottom:"12px",fontFamily:'"Space Mono", monospace',textTransform:"uppercase"}}createLabel(){return{display:"block",fontSize:"10px",color:"#9ca3af",marginBottom:"6px",fontFamily:'"Space Mono", monospace'}}createInputStyle(){return{width:"100%",padding:"6px",backgroundColor:"#1f2937",border:"1px solid #374151",borderRadius:"4px",color:"white",fontSize:"10px",fontFamily:'"Space Mono", monospace'}}}class Gc extends Rd{renderControls(l,i){var c,d;return s.jsxs("div",{style:{display:"flex",flexDirection:"column",gap:"12px"},children:[s.jsx("div",{style:{fontSize:"12px",color:"#f3f4f6",fontWeight:"bold",fontFamily:'"Space Mono", monospace'},children:"🎨 GIF Settings"}),s.jsxs("div",{style:{marginBottom:"12px"},children:[s.jsx("label",{style:this.createLabel(),children:"Loop Behavior"}),s.jsxs("select",{value:l.loop?"infinite":"1",onChange:f=>i({loop:f.target.value==="infinite"}),style:this.createInputStyle(),children:[s.jsx("option",{value:"infinite",children:"🔄 Infinite Loop"}),s.jsx("option",{value:"1",children:"⏹️ Play Once"})]})]}),s.jsxs("div",{style:{marginBottom:"12px"},children:[s.jsx("label",{style:this.createLabel(),children:"Dithering Algorithm"}),s.jsxs("select",{value:((c=l.gif)==null?void 0:c.dither)||"floyd_steinberg",onChange:f=>i({gif:{...l.gif,dither:f.target.value}}),style:this.createInputStyle(),children:[s.jsx("option",{value:"floyd_steinberg",children:"Floyd-Steinberg (Best Quality)"}),s.jsx("option",{value:"bayer",children:"Bayer (Fast)"}),s.jsx("option",{value:"sierra2",children:"Sierra2 (Balanced)"}),s.jsx("option",{value:"sierra2_4a",children:"Sierra2-4A (Smooth)"}),s.jsx("option",{value:"none",children:"No Dithering"})]})]}),s.jsxs("div",{style:{marginBottom:"0"},children:[s.jsx("label",{style:this.createLabel(),children:"Color Palette Size"}),s.jsxs("select",{value:((d=l.gif)==null?void 0:d.colors)||256,onChange:f=>i({gif:{...l.gif,colors:parseInt(f.target.value)}}),style:this.createInputStyle(),children:[s.jsx("option",{value:256,children:"256 Colors (Max Quality)"}),s.jsx("option",{value:128,children:"128 Colors (Balanced)"}),s.jsx("option",{value:64,children:"64 Colors (Smaller Size)"}),s.jsx("option",{value:32,children:"32 Colors (Small Size)"}),s.jsx("option",{value:16,children:"16 Colors (Minimal)"})]})]})]})}getDefaults(){return{format:"gif",quality:"high",loop:!0,gif:{dither:"floyd_steinberg",colors:256}}}validate(l){const i=[];return l.gif&&(l.gif.colors&&![16,32,64,128,256].includes(l.gif.colors)&&i.push("Tamaño de paleta de colores inválido. Debe ser 16, 32, 64, 128 o 256."),l.gif.dither&&!["none","bayer","floyd_steinberg","sierra2","sierra2_4a"].includes(l.gif.dither)&&i.push("Algoritmo de dithering inválido.")),l.fps&&l.fps>50&&i.push("FPS para GIF debe ser 50 o menor para mejor compatibilidad."),{isValid:i.length===0,errors:i}}getFileExtension(){return"gif"}getDisplayName(){return"Animated GIF"}getDescription(){return"Compressed animated image format, perfect for web sharing and social media. Smaller file sizes but limited color palette."}getSupportedQualities(){return["low","medium","high"]}getRecommendedHint(){return"💡 For best results: Use 256 colors with Floyd-Steinberg dithering. Keep FPS at 24 or lower for smooth playback."}}class _a extends Rd{renderControls(l,i){return s.jsxs("div",{style:{display:"flex",flexDirection:"column",gap:"12px"},children:[s.jsxs("div",{style:{fontSize:"12px",color:"#f3f4f6",fontWeight:"bold",fontFamily:'"Space Mono", monospace'},children:["🎬 ",this.getDisplayName()," Settings"]}),s.jsxs("div",{style:{marginBottom:"12px"},children:[s.jsx("label",{style:this.createLabel(),children:"Video Codec"}),s.jsx("select",{value:this.getCodec(),onChange:()=>{},disabled:!0,style:{...this.createInputStyle(),opacity:.7,cursor:"not-allowed"},children:s.jsxs("option",{value:this.getCodec(),children:[this.getCodec()," (Optimized)"]})})]}),s.jsxs("div",{style:{marginBottom:"12px"},children:[s.jsx("label",{style:this.createLabel(),children:"Video Bitrate"}),s.jsxs("select",{value:this.getBitrateFromQuality(l.quality),onChange:c=>this.handleBitrateChange(c.target.value,i),style:this.createInputStyle(),children:[s.jsx("option",{value:"1M",children:"1 Mbps (Low Quality)"}),s.jsx("option",{value:"3M",children:"3 Mbps (Medium Quality)"}),s.jsx("option",{value:"5M",children:"5 Mbps (High Quality)"}),s.jsx("option",{value:"8M",children:"8 Mbps (Ultra Quality)"})]})]}),s.jsxs("div",{style:{marginBottom:"0"},children:[s.jsx("label",{style:this.createLabel(),children:"Quality Mode"}),s.jsxs("select",{value:l.quality,onChange:c=>i({quality:c.target.value}),style:this.createInputStyle(),children:[s.jsx("option",{value:"low",children:"Fast Encode (Lower Quality)"}),s.jsx("option",{value:"medium",children:"Balanced (Good Quality)"}),s.jsx("option",{value:"high",children:"High Quality (Slower)"}),s.jsx("option",{value:"ultra",children:"Ultra Quality (Slowest)"})]})]})]})}getBitrateFromQuality(l){switch(l){case"low":return"1M";case"medium":return"3M";case"high":return"5M";case"ultra":return"8M";default:return"3M"}}handleBitrateChange(l,i){let c="medium";switch(l){case"1M":c="low";break;case"3M":c="medium";break;case"5M":c="high";break;case"8M":c="ultra";break}i({quality:c})}validate(l){const i=[];return l.fps&&(l.fps<1||l.fps>120)&&i.push("FPS must be between 1 and 120 for video formats."),l.resolution&&((l.resolution.width<1||l.resolution.height<1)&&i.push("Resolution must be at least 1x1 pixels."),(l.resolution.width>7680||l.resolution.height>4320)&&i.push("Resolution cannot exceed 8K (7680x4320).")),{isValid:i.length===0,errors:i}}getSupportedQualities(){return["low","medium","high","ultra"]}}class Kc extends _a{getCodec(){return"H.264"}getDefaults(){return{format:"mp4",quality:"high",fps:30}}getFileExtension(){return"mp4"}getDisplayName(){return"MP4 Video"}getDescription(){return"Most compatible video format. Perfect for sharing, streaming, and playback on all devices and platforms."}getRecommendedHint(){return"💡 MP4 with H.264 codec offers the best compatibility across all devices and platforms."}}class qc extends _a{getCodec(){return"VP9"}getDefaults(){return{format:"webm",quality:"high",fps:30}}getFileExtension(){return"webm"}getDisplayName(){return"WebM Video"}getDescription(){return"Open-source video format optimized for web. Smaller file sizes with excellent quality, ideal for web applications."}getRecommendedHint(){return"💡 WebM offers better compression than MP4 but may have limited compatibility on older devices."}}class Yc extends _a{getCodec(){return"H.264"}getDefaults(){return{format:"mov",quality:"ultra",fps:30}}getFileExtension(){return"mov"}getDisplayName(){return"MOV Video"}getDescription(){return"Apple QuickTime format. Best for professional video editing and high-quality archival. Larger file sizes."}getRecommendedHint(){return"💡 MOV format is ideal for professional video editing workflows and Apple ecosystem."}}var wa;let $g=(wa=class{static create(l){if(this.strategies.has(l))return this.strategies.get(l);let i;switch(l.toLowerCase()){case"gif":i=new Gc;break;case"mp4":i=new Kc;break;case"webm":i=new qc;break;case"mov":i=new Yc;break;default:throw new Error(`Unsupported export format: ${l}`)}return this.strategies.set(l,i),i}static getSupportedFormats(){return["gif","mp4","webm","mov"]}static isFormatSupported(l){return this.getSupportedFormats().includes(l.toLowerCase())}static createFresh(l){switch(l.toLowerCase()){case"gif":return new Gc;case"mp4":return new Kc;case"webm":return new qc;case"mov":return new Yc;default:throw new Error(`Unsupported export format: ${l}`)}}static clearCache(){this.strategies.clear()}static getCachedStrategies(){return new Map(this.strategies)}static preloadAll(){this.getSupportedFormats().forEach(l=>{this.create(l)})}static getFormatDisplayNames(){return{gif:"Animated GIF",mp4:"MP4 Video",webm:"WebM Video",mov:"MOV Video"}}static getFormatDescriptions(){return{gif:"Perfect for web sharing and social media",mp4:"Most compatible format for all devices",webm:"Optimized for web with smaller file sizes",mov:"Professional quality for video editing"}}static getRecommendedFormat(l){switch(l){case"web":return"webm";case"social":return"gif";case"professional":return"mov";case"general":default:return"mp4"}}},gt(wa,"strategies",new Map),wa);const Id=({currentFormat:o,onFormatChange:l,supportedFormats:i=["gif","mp4","webm","mov"],mode:c="slideshow",className:d=""})=>{const f=c==="slideshow"?"#ff1493":"#00bfff",p=c==="slideshow"?"#dc143c":"#0080ff",g=c==="slideshow"?"0 0 25px rgba(255, 20, 147, 0.6)":"0 0 25px rgba(0, 191, 255, 0.6)";return s.jsxs("div",{className:`mb-4 ${d}`,children:[s.jsx("h3",{style:{color:"#9ca3af",fontFamily:'"Space Mono", monospace',fontSize:"14px",fontWeight:"bold",marginBottom:"12px",textTransform:"uppercase",letterSpacing:"0.05em",margin:"0 0 12px 0"},children:"📁 Export Format"}),s.jsx("div",{style:{display:"flex",gap:"8px",flexWrap:"nowrap"},children:i.map(x=>{const y=o===x,[C,v]=We.useState(!1),w={flex:1,padding:"8px 12px",borderRadius:"6px",fontSize:"12px",fontFamily:'"Space Mono", monospace',fontWeight:"bold",textTransform:"uppercase",letterSpacing:"0.05em",border:"1px solid",cursor:"pointer",transition:"all 200ms ease",backgroundColor:y?f:"#2d2d30",borderColor:y?p:"#5a5a5d",color:y?"white":C?"#e5e7eb":"#9ca3af",boxShadow:y?g:"none"};return!y&&C&&(w.backgroundColor="#3a3a3d",w.borderColor="#6a6a6d"),s.jsx("button",{onClick:()=>l(x),style:w,onMouseEnter:()=>v(!0),onMouseLeave:()=>v(!1),children:x},x)})})]})},Md=({currentQuality:o,onQualityChange:l,availableQualities:i,mode:c="slideshow",className:d=""})=>{const p=i||(c==="slideshow"?[{value:"low",label:"Low"},{value:"medium",label:"Medium"},{value:"high",label:"High"},{value:"ultra",label:"Ultra"}]:[{value:"web",label:"Web"},{value:"standard",label:"Standard"},{value:"high",label:"High"},{value:"max",label:"Maximum"}]),g=c==="slideshow"?"#ec4899":"#3b82f6",x=c==="slideshow"?"#db2777":"#2563eb",y=c==="slideshow"?"0 0 20px rgba(236, 72, 153, 0.3)":"0 0 20px rgba(59, 130, 246, 0.3)";return s.jsxs("div",{className:`mb-3 ${d}`,children:[s.jsx("h4",{style:{color:"#9ca3af",fontFamily:'"Space Mono", monospace',fontSize:"12px",fontWeight:"bold",textTransform:"uppercase",letterSpacing:"0.05em",margin:"0 0 8px 0"},children:"Quality"}),s.jsx("div",{style:{display:"flex",flexWrap:"wrap",gap:"8px"},children:p.map(C=>{const v=o===C.value,[w,D]=We.useState(!1),T={padding:"6px 12px",borderRadius:"6px",fontSize:"12px",fontFamily:'"Space Mono", monospace',fontWeight:"bold",border:"1px solid",cursor:"pointer",transition:"all 200ms ease",backgroundColor:v?g:"#2d2d30",borderColor:v?x:"#5a5a5d",color:v?"white":w?"#d1d5db":"#9ca3af",boxShadow:v?y:"none"};return!v&&w&&(T.backgroundColor="#3a3a3d",T.borderColor="#6a6a6d"),s.jsx("button",{onClick:()=>l(C.value),style:T,onMouseEnter:()=>D(!0),onMouseLeave:()=>D(!1),title:C.description,children:C.label},C.value)})})]})},Pd=({mode:o,className:l=""})=>{const c=o==="slideshow"?{title:"No Export Available",subtitle:"Add images to timeline to enable export"}:{title:"No Video Loaded",subtitle:"Export options will appear here"},d=s.jsx("svg",{className:"w-12 h-12",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})});return s.jsx(hl,{title:c.title,subtitle:c.subtitle,icon:d,titleColor:o,className:l,mode:o})},Ug=o=>{if(["gif","mp4","webm","mov"].includes(o))return o;throw new Error(`Invalid export format: ${o}`)},Bg=o=>["gif","mp4","webm","mov"].includes(o),Vg=o=>({low:"web",medium:"standard",high:"high",ultra:"ultra",web:"web",standard:"standard",max:"premium"})[o]||"standard",Wg=()=>window.showToast||null,Hg=o=>{const l=window;l.showToast=o},Qg=()=>{const o=window;delete o.showToast},Gg=({message:o,onRemove:l})=>{h.useEffect(()=>{const c=setTimeout(()=>{l(o.id)},4e3);return()=>clearTimeout(c)},[o.id,l]);const i=()=>({success:"bg-accent-green",error:"bg-accent-red",warning:"bg-orange-500",info:"bg-accent-blue"})[o.type];return s.jsx("div",{className:`fixed top-5 right-5 px-4 py-3 rounded-md text-white text-lg font-mono max-w-sm break-words shadow-lg cursor-pointer z-50 ${i()}`,onClick:()=>l(o.id),children:o.message})},Dd=()=>{const[o,l]=h.useState([]),i=(d,f="info")=>{const p=Date.now().toString();l(g=>[...g,{id:p,message:d,type:f}])},c=d=>{l(f=>f.filter(p=>p.id!==d))};return h.useEffect(()=>(Hg(i),()=>{Qg()}),[]),s.jsx(s.Fragment,{children:o.map((d,f)=>s.jsx("div",{style:{position:"fixed",top:`${20+f*80}px`,right:"20px",zIndex:1e3+f},children:s.jsx(Gg,{message:d,onRemove:c})},d.id))})},Ut=(o,l="info")=>{try{const i=Wg();i?i(o,l):console.warn("Toast system not initialized")}catch(i){console.error("Failed to show toast:",i),console.log(`Toast [${l}]: ${o}`)}};function Kg({mode:o,hasContent:l,exportSettings:i,exportState:c,onSettingsChange:d,onExport:f,onCancel:p,additionalControls:g,customValidation:x,progressContent:y,exportButtonText:C,exportButtonDisabledText:v,className:w=""}){const D={format:Bg(i.format)?Ug(i.format):"mp4",fps:i.fps,quality:Vg(i.quality),resolution:i.resolution,gif:i.gif?{...i.gif,loop:typeof i.gif.loop=="boolean"?i.gif.loop?"infinite":"once":i.gif.loop}:void 0},T=Da(D),j=x==null?void 0:x(i),R=T.canExport&&((j==null?void 0:j.canExport)??!0),b=[...T.messages.map(M=>M.message),...(j==null?void 0:j.messages)??[]],_=h.useCallback(M=>{d({...i,format:M})},[i,d]),W=h.useCallback(M=>{d({...i,quality:M})},[i,d]),V=h.useCallback(async()=>{if(!R){Ut("Export configuration is invalid","error");return}try{await f()}catch(M){console.error("Export failed:",M),Ut(`Export failed: ${M instanceof Error?M.message:"Unknown error"}`,"error")}},[R,f]);if(!l)return s.jsx(Pd,{mode:o});const P=()=>{const M="w-full p-4 rounded-lg font-mono font-bold text-lg uppercase transition-all duration-200",F="bg-dark-600 cursor-not-allowed opacity-60 text-dark-400";return c.isExporting||!R?`${M} ${F}`:o==="slideshow"?`${M} bg-pink-600 hover:bg-pink-700 text-white shadow-lg`:`${M} bg-blue-600 hover:bg-blue-700 text-white shadow-lg`},S=()=>c.isExporting?`⏳ Exporting ${i.format.toUpperCase()}...`:R?C||`🚀 Export ${i.format.toUpperCase()}`:v||"❌ Invalid Configuration";return s.jsxs("div",{className:`h-full flex flex-col gap-4 ${w}`,children:[c.isExporting&&s.jsx("div",{className:"p-3 bg-dark-900 rounded border border-dark-650",children:y||s.jsxs(s.Fragment,{children:[s.jsxs("div",{className:"text-sm font-mono text-accent-green mb-2",children:["Exporting... ",c.progress,"%"]}),s.jsx("div",{className:"w-full h-2 bg-dark-700 rounded-full overflow-hidden",children:s.jsx("div",{className:"h-full bg-accent-green transition-all duration-300 ease-out",style:{width:`${c.progress}%`}})})]})}),s.jsxs("div",{className:"space-y-3",children:[s.jsx(Id,{currentFormat:i.format,onFormatChange:_,mode:o}),s.jsx(Md,{currentQuality:i.quality,onQualityChange:W,mode:o}),g]}),!R&&b.length>0&&s.jsx("div",{className:"p-3 bg-red-900/20 border border-red-500/30 rounded",children:s.jsxs("div",{className:"text-red-400 text-sm font-mono",children:[s.jsx("div",{className:"font-bold mb-1",children:"Configuration Issues:"}),s.jsx("ul",{className:"list-disc list-inside space-y-1",children:b.map((M,F)=>s.jsx("li",{children:M},F))})]})}),s.jsx("button",{onClick:V,disabled:c.isExporting||!R,className:P(),title:R?void 0:`Invalid configuration: ${b.join(", ")}`,children:S()})]})}const qg=({resolution:o,onResolutionPresetChange:l,onCustomResolutionChange:i})=>{const[c,d]=h.useState(!1),f=["1080p","720p","custom"],p=()=>{o.preset==="custom"?d(!0):l("custom")};return s.jsxs("div",{className:"mb-3",children:[s.jsx("h4",{className:"text-dark-400 font-mono text-xs mb-2 uppercase tracking-wider",children:"Resolution"}),s.jsx("div",{className:"flex flex-wrap gap-1.5",children:f.map(g=>s.jsx("button",{onClick:()=>g==="custom"?p():l(g),className:`px-2 py-1 rounded text-xs font-mono transition-all ${(o.preset||"1080p")===g?"bg-accent-pink text-white border border-accent-pink-dark":"bg-dark-800 text-dark-400 border border-dark-650 hover:bg-dark-750 hover:text-dark-300"}`,children:g==="custom"?"Custom":g.toUpperCase()},g))}),c&&s.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50",children:s.jsxs("div",{className:"bg-dark-900 border border-dark-600 rounded-lg p-4 w-80",children:[s.jsx("h3",{className:"text-accent-pink font-mono text-sm font-bold mb-3 uppercase tracking-wider",children:"Custom Resolution"}),s.jsxs("div",{className:"space-y-3",children:[s.jsxs("div",{children:[s.jsx("h4",{className:"text-dark-400 font-mono text-xs mb-2",children:"Quick Presets"}),s.jsx("div",{className:"grid grid-cols-2 gap-2",children:[{label:"Square",width:1080,height:1080},{label:"Portrait",width:1080,height:1920},{label:"Ultrawide",width:2560,height:1080},{label:"Classic 4:3",width:1440,height:1080}].map(g=>s.jsxs("button",{onClick:()=>{i({preset:"custom",width:g.width,height:g.height}),d(!1)},className:"px-2 py-1.5 bg-dark-700 hover:bg-dark-600 border border-dark-600 rounded text-xs font-mono transition-colors text-dark-300 hover:text-white",children:[s.jsx("div",{className:"font-bold",children:g.label}),s.jsxs("div",{className:"text-xs opacity-75",children:[g.width,"×",g.height]})]},g.label))})]}),s.jsxs("div",{children:[s.jsx("h4",{className:"text-dark-400 font-mono text-xs mb-2",children:"Manual Entry"}),s.jsxs("div",{className:"flex items-center gap-2 justify-center",children:[s.jsxs("div",{className:"flex flex-col items-center",children:[s.jsx("label",{className:"text-xs text-dark-400 mb-1",children:"Width"}),s.jsx("input",{type:"number",value:o.width||1920,onChange:g=>i({width:parseInt(g.target.value)||1920,preset:"custom"}),className:"w-20 px-2 py-1 bg-dark-700 border border-dark-600 rounded text-white text-xs font-mono text-center",min:"320",max:"4096"})]}),s.jsx("span",{className:"text-dark-500 font-mono text-lg mt-4",children:"×"}),s.jsxs("div",{className:"flex flex-col items-center",children:[s.jsx("label",{className:"text-xs text-dark-400 mb-1",children:"Height"}),s.jsx("input",{type:"number",value:o.height||1080,onChange:g=>i({height:parseInt(g.target.value)||1080,preset:"custom"}),className:"w-20 px-2 py-1 bg-dark-700 border border-dark-600 rounded text-white text-xs font-mono text-center",min:"240",max:"2160"})]})]})]})]}),s.jsx("div",{className:"flex gap-2 mt-4",children:s.jsx("button",{onClick:()=>d(!1),className:"flex-1 px-3 py-2 bg-dark-700 hover:bg-dark-600 border border-dark-600 rounded text-xs font-mono text-dark-300 hover:text-white transition-colors",children:"Close"})})]})})]})},Yg=({strategy:o,exportSettings:l,updateExportSettings:i,fpsHandlers:c})=>{const[d,f]=h.useState(!1);return s.jsxs("div",{className:"mb-3",children:[s.jsxs("button",{onClick:()=>f(!d),className:"w-full flex items-center justify-between p-2 bg-dark-800 hover:bg-dark-750 border border-dark-650 rounded text-xs font-mono transition-all",children:[s.jsx("span",{className:"text-dark-400 uppercase tracking-wider",children:"Advanced Settings"}),s.jsx("span",{className:`text-dark-500 transition-transform ${d?"rotate-180":""}`,children:"▼"})]}),d&&s.jsxs("div",{className:"mt-2 p-3 bg-dark-800/50 border border-dark-650/50 rounded space-y-3",children:[s.jsxs("div",{children:[s.jsx("h5",{className:"text-dark-400 font-mono text-xs mb-2 uppercase tracking-wider",children:"Frame Rate"}),s.jsx("div",{className:"flex flex-wrap gap-1.5",children:[60,30,24,15].map(p=>s.jsxs("button",{onClick:()=>c.onFpsChange(p),className:`px-2 py-1 rounded text-xs font-mono transition-all ${(c.currentFps||30)===p?"bg-accent-pink text-white border border-accent-pink-dark":"bg-dark-700 text-dark-400 border border-dark-600 hover:bg-dark-650 hover:text-dark-300"}`,children:[p,"fps"]},p))})]}),s.jsxs("div",{children:[s.jsxs("h5",{className:"text-dark-400 font-mono text-xs mb-2 uppercase tracking-wider",children:[l.format.toUpperCase()," Settings"]}),s.jsx("div",{className:"text-xs",children:o.renderControls(l,i)})]})]})]})},Xg=()=>{const{project:o,export:l,hasTimeline:i,exportSlideshow:c,updateExportSettings:d}=Kr(),{exportSettings:f}=o,p=$g.create(f.format),g={format:f.format,quality:f.quality,fps:f.fps,resolution:f.resolution,gif:f.gif},x={isExporting:l.isExporting,progress:l.progress,error:l.error,isCompleted:l.isCompleted,downloadUrl:l.downloadUrl},y=v=>{d({format:v.format,quality:v.quality,fps:v.fps,resolution:v.resolution,gif:v.gif})},C=s.jsxs(s.Fragment,{children:[s.jsx(qg,{resolution:f.resolution,onResolutionPresetChange:v=>{d({resolution:{...f.resolution,preset:v}})},onCustomResolutionChange:v=>{d({resolution:{...f.resolution,...v,preset:"custom"}})}}),s.jsx(Yg,{strategy:p,exportSettings:f,updateExportSettings:d,fpsHandlers:{currentFps:f.fps||30,onFpsChange:v=>d({fps:v})}})]});return s.jsxs(s.Fragment,{children:[s.jsx(Kg,{mode:"slideshow",hasContent:i,exportSettings:g,exportState:x,onSettingsChange:y,onExport:c,additionalControls:C,exportButtonText:"🚀 Export Slideshow",exportButtonDisabledText:"❌ Invalid Configuration"}),s.jsx(Ag,{isVisible:l.isExporting||l.isCompleted,progress:l.progress,error:l.error,isCompleted:l.isCompleted,downloadUrl:l.downloadUrl,currentStep:l.currentStep,onCancel:()=>{},format:f.format,onDownload:()=>{if(l.downloadUrl){const v=document.createElement("a");v.href=`${window.location.hostname==="localhost"?"http://localhost:3001":window.location.origin}${l.downloadUrl}`,v.download="",v.style.display="none",document.body.appendChild(v),v.click(),document.body.removeChild(v)}}})]})},Jg=({isOpen:o,onClose:l})=>{const[i,c]=h.useState({name:"",email:"",purpose:""}),[d,f]=h.useState(!1),[p,g]=h.useState(null),[x,y]=h.useState(!1),C=T=>{const{name:j,value:R}=T.target;c(b=>({...b,[j]:R}))},v=async T=>{if(T.preventDefault(),!i.name.trim()||!i.email.trim()){g({success:!1,error:"Nombre y email son requeridos"}),y(!0);return}f(!0),g(null),y(!1);try{const R=await(await fetch("/api/auth/generate-key",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(i)})).json();g(R),y(!0),R.success&&c({name:"",email:"",purpose:""})}catch{g({success:!1,error:"Error de conexión. Inténtalo de nuevo."}),y(!0)}finally{f(!1)}},w=async T=>{try{await navigator.clipboard.writeText(T),console.log("✅ API key copied to clipboard")}catch(j){console.error("❌ Failed to copy to clipboard:",j);const R=document.createElement("textarea");R.value=T,document.body.appendChild(R),R.select(),document.execCommand("copy"),document.body.removeChild(R)}},D=()=>{g(null),y(!1),c({name:"",email:"",purpose:""}),l()};return o?s.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:s.jsxs("div",{className:"bg-dark-800 rounded-lg shadow-2xl w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto",children:[s.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-dark-700",children:[s.jsx("h2",{className:"text-xl font-bold text-pink-500",children:"🔑 Generar API Key"}),s.jsx("button",{onClick:D,className:"text-gray-400 hover:text-white transition-colors",children:s.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),s.jsxs("div",{className:"p-6",children:[s.jsx("div",{className:"bg-dark-700 border-l-4 border-pink-500 p-4 mb-6 rounded",children:s.jsxs("div",{className:"text-sm text-gray-300",children:[s.jsx("strong",{className:"text-white",children:"Para usar el plugin de Figma:"}),s.jsx("br",{}),"1. Genera tu API key aquí",s.jsx("br",{}),"2. Copia la key generada",s.jsx("br",{}),"3. Pégala en el plugin de Figma AnimaGen Exporter"]})}),s.jsxs("form",{onSubmit:v,className:"space-y-4",children:[s.jsxs("div",{children:[s.jsx("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-300 mb-2",children:"Nombre completo:"}),s.jsx("input",{type:"text",id:"name",name:"name",value:i.name,onChange:C,required:!0,placeholder:"Tu nombre",className:"w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"})]}),s.jsxs("div",{children:[s.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-300 mb-2",children:"Email:"}),s.jsx("input",{type:"email",id:"email",name:"email",value:i.email,onChange:C,required:!0,placeholder:"<EMAIL>",className:"w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"})]}),s.jsxs("div",{children:[s.jsx("label",{htmlFor:"purpose",className:"block text-sm font-medium text-gray-300 mb-2",children:"Propósito (opcional):"}),s.jsx("input",{type:"text",id:"purpose",name:"purpose",value:i.purpose,onChange:C,placeholder:"Ej: Plugin de Figma para presentaciones",className:"w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"})]}),s.jsx("button",{type:"submit",disabled:d,className:"w-full py-3 px-4 bg-pink-500 text-white rounded-lg font-medium hover:bg-pink-600 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:ring-offset-2 focus:ring-offset-dark-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:d?s.jsxs("div",{className:"flex items-center justify-center",children:[s.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Generando..."]}):"Generar API Key"})]}),x&&p&&s.jsx("div",{className:`mt-6 p-4 rounded-lg ${p.success?"bg-green-900 border border-green-700 text-green-100":"bg-red-900 border border-red-700 text-red-100"}`,children:p.success?s.jsxs("div",{children:[s.jsx("div",{className:"font-medium mb-3",children:"✅ API Key generada exitosamente!"}),s.jsxs("div",{className:"mb-3",children:[s.jsx("strong",{children:"Tu API Key:"}),s.jsx("div",{className:"bg-dark-700 p-3 rounded mt-2 font-mono text-sm break-all border border-dark-600",children:p.apiKey}),s.jsx("button",{onClick:()=>w(p.apiKey),className:"mt-2 px-3 py-1 bg-green-600 text-white text-xs rounded hover:bg-green-700 transition-colors",children:"📋 Copiar"})]}),s.jsxs("div",{className:"text-sm",children:[s.jsx("strong",{children:"⚠️ Importante:"})," Guarda esta key en un lugar seguro. No la compartas con nadie."]})]}):s.jsxs("div",{children:[s.jsx("strong",{children:"❌ Error:"})," ",p.error]})})]})]})}):null},Zg=()=>{const o=pn(),{loadImagesFromSession:l}=Kr(),[i,c]=h.useState(!1),f=new URLSearchParams(o.search).get("sessionId");return h.useEffect(()=>{f&&(console.log("🎬 Figma plugin session detected:",f),l(f).then(p=>{console.log(p?"✅ Successfully loaded images from session":"❌ Failed to load images from session")}).catch(p=>{console.error("❌ Error loading images from session:",p)}))},[f,l]),s.jsxs(s.Fragment,{children:[s.jsxs("div",{className:"app-container custom-scrollbar",children:[s.jsx(Ma,{currentMode:"slideshow",onOpenAPIKeyModal:()=>c(!0)}),s.jsxs("div",{className:"flex flex-col flex-1 min-h-0",children:[s.jsxs("div",{className:"flex flex-1 min-h-0",style:{gap:"4px"},children:[s.jsx(yd,{children:s.jsx(ng,{})}),s.jsx(wd,{children:s.jsx(wg,{})}),s.jsx(Sd,{children:s.jsx(Xg,{})})]}),s.jsx(kd,{children:s.jsx("div",{className:"h-full overflow-x-auto overflow-y-hidden",children:s.jsx(Og,{})})})]})]}),s.jsx(Jg,{isOpen:i,onClose:()=>c(!1)}),s.jsx(Dd,{})]})},e0=()=>s.jsx(Rh,{children:s.jsx(Zg,{})}),t0=async(o,l)=>{const i=new FormData;i.append("video",o);const c=window.location.hostname==="localhost"?"http://localhost:3001":window.location.origin,d=l||`session_${Date.now()}`,f=await fetch(`${c}/video-editor/upload?sessionId=${d}`,{method:"POST",body:i});if(!f.ok)throw new Error(`Video upload failed: ${f.statusText}`);return f.json()},n0=(o,l)=>{const[i,c]=h.useState(!1),d=h.useCallback(async(p,g=8)=>new Promise((x,y)=>{console.log("🎬 Starting thumbnail generation for",g,"thumbnails, duration:",p.duration);const C=[],v=document.createElement("canvas"),w=v.getContext("2d");if(!w){console.error("❌ Canvas context not available"),x([]);return}if(p.readyState<3){console.warn("⚠️ Video not ready, waiting..."),p.addEventListener("canplay",()=>{d(p,g).then(x).catch(y)},{once:!0});return}const D=160,T=90,j=p.videoWidth/p.videoHeight;j>D/T?(v.width=D,v.height=D/j):(v.height=T,v.width=T*j);let R=0,b=!1;const _=async()=>{if(!(b||R>=g)){b=!0;try{const S=p.duration/(g-1)*R;if(console.log(`🔄 Seeking to ${S.toFixed(2)}s for thumbnail ${R+1}/${g}`),p.currentTime=Math.min(S,p.duration-.1),await new Promise(M=>setTimeout(M,150)),p.readyState>=2){w.drawImage(p,0,0,v.width,v.height);const M=v.toDataURL("image/jpeg",.4);C.push(M),console.log(`📸 Generated thumbnail ${R+1}/${g} at ${S.toFixed(2)}s`)}else console.warn(`⚠️ Video not ready for thumbnail ${R+1}/${g}`);R++,b=!1,R<g?setTimeout(_,200):(console.log("✅ All thumbnails generated:",C.length),x(C))}catch(S){console.error("❌ Error generating thumbnail:",S),R++,b=!1,R<g?setTimeout(_,200):x(C)}}},W=p.paused;W||p.pause();const V=setTimeout(()=>{console.warn("⚠️ Thumbnail generation timeout, resolving with partial thumbnails"),x(C)},2e4),P=x;x=S=>{clearTimeout(V),W||p.play().catch(()=>{}),P(S)},console.log("🚀 Starting thumbnail generation process"),_()}),[l]);return{uploadVideo:h.useCallback(async p=>{c(!0);try{const g=document.createElement("video");g.src=URL.createObjectURL(p),await new Promise(y=>{g.onloadedmetadata=y});const x={file:p,id:`video_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,name:p.name,size:p.size,type:"video",duration:g.duration,width:g.videoWidth,height:g.videoHeight,thumbnails:[],addedAt:new Date,createdAt:new Date,updatedAt:new Date,videoUrl:URL.createObjectURL(p)};d(g,8).then(y=>{console.log("🎥 Generated thumbnails:",y.length,"URLs:",y.slice(0,2)),x.thumbnails=y,l(C=>({...C,video:{...C.video,thumbnails:y}}))}).catch(y=>{console.error("❌ Thumbnail generation failed:",y)});try{const y=await t0(p,o);console.log("✅ Video uploaded successfully:",y),x.uploadedInfo=y.video}catch(y){console.warn("⚠️ Backend upload failed, using local file:",y)}return URL.revokeObjectURL(g.src),x}catch(g){return console.error("❌ Video upload failed:",g),null}finally{c(!1)}},[o,d]),generateThumbnails:d,isUploading:i}},r0=()=>{const[o,l]=h.useState(0),[i,c]=h.useState(!1),[d,f]=h.useState(0),p=h.useRef(null),g=h.useCallback(b=>{p.current=b,b&&f(b.duration||0)},[]),x=h.useCallback(()=>{const b=p.current;b&&(i?(b.pause(),c(!1)):(b.play(),c(!0)))},[i]),y=h.useCallback(b=>{const _=p.current;if(!_)return;const W=Math.max(0,Math.min(b,_.duration||0));_.currentTime=W,l(W)},[]),C=h.useCallback(()=>{y(o-1)},[o,y]),v=h.useCallback(()=>{y(o+1)},[o,y]),w=h.useCallback(()=>{y(o-10)},[o,y]),D=h.useCallback(()=>{y(o+10)},[o,y]),T=h.useCallback(b=>{l(b)},[]),j=h.useCallback(()=>{c(!1),l(0)},[]),R=h.useCallback(b=>{f(b),l(0),c(!1)},[]);return{currentTime:o,isPlaying:i,duration:d,videoElement:p.current,setVideoElement:g,togglePlayback:x,seekTo:y,stepBackward:C,stepForward:v,jumpBackward:w,jumpForward:D,updateCurrentTime:T,handleVideoEnded:j,handleVideoLoaded:R}},o0=()=>{const[o,l]=h.useState([]),i=h.useCallback((C,v,w)=>{const D=new Date,T=Math.max(0,C),j=Math.max(T+.1,v),R={id:`segment_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,startTime:T,endTime:j,videoId:w,position:o.length,duration:j-T,createdAt:D,updatedAt:D};return l(b=>[...b,R]),R},[]),c=h.useCallback((C,v)=>{l(w=>w.map(D=>D.id===C?{...D,...v,startTime:Math.max(0,v.startTime??D.startTime),endTime:Math.max((v.startTime??D.startTime)+.1,v.endTime??D.endTime)}:D))},[]),d=h.useCallback(C=>{l(v=>v.filter(w=>w.id!==C))},[]),f=h.useCallback(()=>{l([])},[]),p=h.useCallback(()=>o.reduce((C,v)=>C+(v.endTime-v.startTime),0),[o]),g=h.useCallback((C,v,w)=>{const D=new Date,T=Math.max(0,C),j=Math.max(T+.1,v),R={id:`trim_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,startTime:T,endTime:j,videoId:w,position:0,duration:j-T,createdAt:D,updatedAt:D};return l([R]),R},[]),x=h.useCallback(C=>{l(C)},[]),y=h.useCallback(()=>[...o].sort((C,v)=>C.startTime-v.startTime),[o]);return{segments:o,totalDuration:p(),sortedSegments:y(),addSegment:i,updateSegment:c,removeSegment:d,clearSegments:f,trimVideo:g,reorderSegments:x,getTotalDuration:p,getSortedSegments:y}},l0=()=>{const o=()=>{const j=Date.now(),R={format:"mp4",quality:"standard",fps:30,resolution:{width:1920,height:1080,preset:"original"}};return{id:`video_project_${j}`,name:"Untitled Video Project",timeline:[],exportSettings:R,createdAt:new Date(j),updatedAt:new Date(j),library:{videos:[],selectedVideoId:null},sequence:{items:[],totalDuration:0},video:null,segments:[],effects:[],sessionId:`session_${j}_${Math.random().toString(36).substr(2,9)}`}},[l,i]=h.useState(o()),[c]=h.useState(null),d=n0(l.sessionId||"",i),f=r0(),p=o0(),g=l.video!==null,x=h.useCallback(async j=>{const R=await d.uploadVideo(j);return R&&(i(b=>({...b,video:R,segments:[]})),p.clearSegments(),f.videoElement&&f.handleVideoLoaded(R.duration)),R},[d,p,f]),y=h.useCallback(async j=>{await x(j)},[x]),C=h.useCallback((j,R)=>(i(b=>b.video?(p.addSegment(j,R,b.video.id),{...b,segments:[...p.segments]}):b),p.segments[p.segments.length-1]||null),[p]),v=h.useCallback((j,R)=>{p.updateSegment(j,R),i(b=>({...b,segments:[...p.segments]}))},[p]),w=h.useCallback(j=>{p.removeSegment(j),i(R=>({...R,segments:[...p.segments]}))},[p]),D=h.useCallback((j,R)=>{let b=null;return i(_=>_.video?(b=p.trimVideo(j,R,_.video.id),{..._,segments:[b]}):_),b},[p]),T=h.useCallback(()=>{i(()=>o()),p.clearSegments(),f.handleVideoEnded()},[p,f]);return{project:{...l,segments:p.segments},hasVideo:g,error:c,uploadVideo:y,isUploading:d.isUploading,currentTime:f.currentTime,isPlaying:f.isPlaying,duration:f.duration,videoElement:f.videoElement,setVideoElement:f.setVideoElement,togglePlayback:f.togglePlayback,seekTo:f.seekTo,stepBackward:f.stepBackward,stepForward:f.stepForward,jumpBackward:f.jumpBackward,jumpForward:f.jumpForward,updateCurrentTime:f.updateCurrentTime,handleVideoEnded:f.handleVideoEnded,handleVideoLoaded:f.handleVideoLoaded,segments:p.segments,totalDuration:p.totalDuration,addSegment:C,updateSegment:v,removeSegment:w,trimVideo:D,clearSegments:p.clearSegments,clearProject:T,generateThumbnails:d.generateThumbnails}},_d=h.createContext(null),i0=({children:o})=>{var b,_,W,V,P;const l=l0(),[i,c]=We.useState(null),[d,f]=We.useState(0),[p,g]=We.useState(!1),x=We.useCallback(S=>{c(S)},[]),y=We.useCallback(()=>{i!=null&&i.current?p?(console.log("⏸️ Pausing video"),i.current.pause(),g(!1)):(console.log("▶️ Playing video"),i.current.play(),g(!0)):console.log("❌ No video ref available for playback")},[i,p]),C=We.useCallback(S=>{Math.abs(d-S)>1&&console.log(`🕐 Setting time to: ${S.toFixed(3)}s`),f(S),i!=null&&i.current&&Math.abs(i.current.currentTime-S)>.1&&(i.current.currentTime=S)},[i,d]),v=We.useCallback(()=>{var S;if(i!=null&&i.current&&l.hasVideo){const F=1/(((S=l.project.video)==null?void 0:S.fps)||30),H=Math.max(0,d-F);console.log(`⬅️ Step backward: ${d.toFixed(3)}s → ${H.toFixed(3)}s (${F.toFixed(3)}s frame)`),C(H)}},[i,l.hasVideo,(b=l.project.video)==null?void 0:b.fps,d,C]),w=We.useCallback(()=>{var S,M;if(i!=null&&i.current&&l.hasVideo){const H=1/(((S=l.project.video)==null?void 0:S.fps)||30),J=((M=l.project.video)==null?void 0:M.duration)||0,te=Math.min(J,d+H);console.log(`➡️ Step forward: ${d.toFixed(3)}s → ${te.toFixed(3)}s (${H.toFixed(3)}s frame)`),C(te)}},[i,l.hasVideo,(_=l.project.video)==null?void 0:_.fps,(W=l.project.video)==null?void 0:W.duration,d,C]),D=We.useCallback(()=>{if(i!=null&&i.current&&l.hasVideo){const S=Math.max(0,d-5);C(S)}},[i,l.hasVideo,d,C]),T=We.useCallback(()=>{var S;if(i!=null&&i.current&&l.hasVideo){const M=((S=l.project.video)==null?void 0:S.duration)||0,F=Math.min(M,d+5);C(F)}},[i,l.hasVideo,(V=l.project.video)==null?void 0:V.duration,d,C]),j=We.useCallback(S=>{var M;if(i!=null&&i.current&&l.hasVideo){const F=((M=l.project.video)==null?void 0:M.duration)||0,H=Math.max(0,Math.min(S,F));C(H)}},[i,l.hasVideo,(P=l.project.video)==null?void 0:P.duration,C]);We.useEffect(()=>{if(i!=null&&i.current){const S=i.current,M=()=>{Math.abs(d-S.currentTime)>.05&&f(S.currentTime)},F=()=>g(!0),H=()=>g(!1),J=()=>g(!1);return S.addEventListener("timeupdate",M),S.addEventListener("play",F),S.addEventListener("pause",H),S.addEventListener("ended",J),()=>{S.removeEventListener("timeupdate",M),S.removeEventListener("play",F),S.removeEventListener("pause",H),S.removeEventListener("ended",J)}}},[i,d]);const R=We.useMemo(()=>({...l,videoRef:i,videoElement:(i==null?void 0:i.current)||null,currentTime:d,videoDuration:l.hasVideo?l.project.video.duration:0,isPlaying:p,setCurrentTime:C,togglePlayback:y,setVideoRef:x,stepBackward:v,stepForward:w,jumpBackward:D,jumpForward:T,seekTo:j}),[l,i,d,p,C,y,x,v,w,D,T,j]);return s.jsx(_d.Provider,{value:R,children:o})},qr=()=>{const o=h.useContext(_d);if(!o)throw new Error("useVideoEditorContext must be used within a VideoEditorProvider");return o},Jn=(o,l="info")=>{window.showToast?window.showToast(o,l):alert(o)},a0=()=>{const{uploadVideo:o,isUploading:l,project:i}=qr(),c={accept:[".mp4",".mov",".webm",".avi",".mkv","video/*"],multiple:!1,maxSize:500*1024*1024,autoUpload:!1},d=x=>["video/mp4","video/quicktime","video/webm","video/x-msvideo","video/x-matroska"].includes(x.type)||/\.(mp4|mov|webm|avi|mkv)$/i.test(x.name)?!0:"Please select a valid video file (MP4, MOV, WebM, AVI, MKV)",{uploadFiles:f}=Nd({config:c,validators:[d],onSuccess:async x=>{x.length>0&&await o(x[0].file)},onError:x=>{x.forEach(y=>{Jn(y.error,"warning")})}}),p=h.useMemo(()=>{var x;return i.video?{id:"current-video",file:i.video.file,name:i.video.file.name,type:"video",size:i.video.file.size,duration:i.video.duration,dimensions:{width:i.video.width,height:i.video.height},thumbnails:i.video.thumbnails||[],preview:((x=i.video.thumbnails)==null?void 0:x[0])||"",createdAt:new Date,updatedAt:new Date}:null},[i.video]),g={onUpload:async x=>{await f(x)}};return s.jsx("div",{className:"h-full flex flex-col",children:i.video?s.jsxs("div",{className:"panel",style:{padding:"8px"},children:[s.jsxs("div",{className:"flex items-start gap-4 mb-4",children:[s.jsx("div",{className:"flex-shrink-0",children:s.jsx(vd,{item:p,size:"large",showDuration:!0,showType:!0})}),s.jsxs("div",{className:"flex-1 min-w-0",children:[s.jsx("p",{className:"m-0 text-sm text-dark-300 font-bold mb-1 font-mono truncate",children:i.video.file.name}),s.jsxs("div",{className:"text-xs text-dark-400 font-mono space-y-1",children:[s.jsxs("div",{children:["Duration: ",i.video.duration.toFixed(1),"s"]}),s.jsxs("div",{children:["Resolution: ",i.video.width,"×",i.video.height]}),s.jsxs("div",{children:["Size: ",(i.video.file.size/(1024*1024)).toFixed(1),"MB"]}),i.video.fps&&s.jsxs("div",{children:["FPS: ",i.video.fps]})]})]})]}),s.jsx(jd,{config:c,handlers:g,loading:l,className:"h-12",style:{minHeight:"48px",backgroundColor:"rgba(34, 197, 94, 0.1)",borderColor:"#22c55e"}})]}):s.jsx(Ed,{mode:"video-editor",config:c,handlers:g,loading:l,children:s.jsx(Pa,{mode:"video-editor"})})})},s0=()=>{var x;const{project:o,hasVideo:l,setVideoRef:i}=qr(),c=h.useRef(null);h.useEffect(()=>{c.current&&i(c)},[l,i]);const d=h.useCallback(()=>{},[]),f=h.useCallback(y=>{console.error("❌ Video preview error:",y)},[]),p=h.useMemo(()=>{var y;return(y=o.video)!=null&&y.file?URL.createObjectURL(o.video.file):""},[(x=o.video)==null?void 0:x.file]);if(h.useEffect(()=>()=>{p&&URL.revokeObjectURL(p)},[p]),!l)return s.jsx(Cd,{mode:"video-editor"});const g=o.video?s.jsxs("div",{className:"absolute top-2.5 left-2.5 bg-black/80 px-3 py-2 rounded text-xs font-mono",children:[s.jsxs("div",{className:"text-accent-green mb-0.5",children:[o.video.width," × ",o.video.height]}),s.jsxs("div",{className:"text-dark-400",children:[o.video.duration.toFixed(1),"s • ",(o.video.file.size/1024/1024).toFixed(1),"MB"]})]}):null;return s.jsxs("div",{className:"h-full",children:[s.jsx(Td,{videoSrc:p,mode:"video-editor",onVideoLoaded:d,onVideoError:f,showControls:!0,showOverlay:!0,overlayContent:g,containerClassName:"h-full",className:"w-full h-auto max-h-[90%] object-contain rounded"}),s.jsx("video",{ref:c,src:p,style:{display:"none"},onLoadedMetadata:d})]})},Ld=["shift","alt","meta","mod","ctrl","control"],u0={esc:"escape",return:"enter",left:"arrowleft",right:"arrowright",up:"arrowup",down:"arrowdown",ShiftLeft:"shift",ShiftRight:"shift",AltLeft:"alt",AltRight:"alt",MetaLeft:"meta",MetaRight:"meta",OSLeft:"meta",OSRight:"meta",ControlLeft:"ctrl",ControlRight:"ctrl"};function fn(o){return(u0[o.trim()]||o.trim()).toLowerCase().replace(/key|digit|numpad/,"")}function zd(o){return Ld.includes(o)}function va(o,l=","){return o.toLowerCase().split(l)}function ya(o,l="+",i=">",c=!1,d){let f=[],p=!1;o.includes(i)?(p=!0,f=o.toLocaleLowerCase().split(i).map(y=>fn(y))):f=o.toLocaleLowerCase().split(l).map(y=>fn(y));const g={alt:f.includes("alt"),ctrl:f.includes("ctrl")||f.includes("control"),shift:f.includes("shift"),meta:f.includes("meta"),mod:f.includes("mod"),useKey:c},x=f.filter(y=>!Ld.includes(y));return{...g,keys:x,description:d,isSequence:p}}typeof document<"u"&&(document.addEventListener("keydown",o=>{o.code!==void 0&&Fd([fn(o.code)])}),document.addEventListener("keyup",o=>{o.code!==void 0&&Od([fn(o.code)])})),typeof window<"u"&&(window.addEventListener("blur",()=>{Bt.clear()}),window.addEventListener("contextmenu",()=>{setTimeout(()=>{Bt.clear()},0)}));const Bt=new Set;function La(o){return Array.isArray(o)}function c0(o,l=","){return(La(o)?o:o.split(l)).every(i=>Bt.has(i.trim().toLowerCase()))}function Fd(o){const l=Array.isArray(o)?o:[o];Bt.has("meta")&&Bt.forEach(i=>!zd(i)&&Bt.delete(i.toLowerCase())),l.forEach(i=>Bt.add(i.toLowerCase()))}function Od(o){const l=Array.isArray(o)?o:[o];o==="meta"?Bt.clear():l.forEach(i=>Bt.delete(i.toLowerCase()))}function d0(o,l,i){(typeof i=="function"&&i(o,l)||i===!0)&&o.preventDefault()}function f0(o,l,i){return typeof i=="function"?i(o,l):i===!0||i===void 0}function p0(o){return Ad(o,["input","textarea","select"])}function Ad(o,l=!1){const{target:i,composed:c}=o;let d;return m0(i)&&c?d=o.composedPath()[0]&&o.composedPath()[0].tagName:d=i&&i.tagName,La(l)?!!(d&&l&&l.some(f=>f.toLowerCase()===d.toLowerCase())):!!(d&&l&&l)}function m0(o){return!!o.tagName&&!o.tagName.startsWith("-")&&o.tagName.includes("-")}function h0(o,l){return o.length===0&&l?(console.warn('A hotkey has the "scopes" option set, however no active scopes were found. If you want to use the global scopes feature, you need to wrap your app in a <HotkeysProvider>'),!0):l?o.some(i=>l.includes(i))||o.includes("*"):!0}const g0=(o,l,i=!1)=>{const{alt:c,meta:d,mod:f,shift:p,ctrl:g,keys:x,useKey:y}=l,{code:C,key:v,ctrlKey:w,metaKey:D,shiftKey:T,altKey:j}=o,R=fn(C);if(y&&(x==null?void 0:x.length)===1&&x.includes(v))return!0;if(!(x!=null&&x.includes(R))&&!["ctrl","control","unknown","meta","alt","shift","os"].includes(R))return!1;if(!i){if(c!==j&&R!=="alt"||p!==T&&R!=="shift")return!1;if(f){if(!D&&!w)return!1}else if(d!==D&&R!=="meta"&&R!=="os"||g!==w&&R!=="ctrl"&&R!=="control")return!1}return x&&x.length===1&&x.includes(R)?!0:x?c0(x):!x},x0=h.createContext(void 0),v0=()=>h.useContext(x0);function $d(o,l){return o&&l&&typeof o=="object"&&typeof l=="object"?Object.keys(o).length===Object.keys(l).length&&Object.keys(o).reduce((i,c)=>i&&$d(o[c],l[c]),!0):o===l}const y0=h.createContext({hotkeys:[],activeScopes:[],toggleScope:()=>{},enableScope:()=>{},disableScope:()=>{}}),w0=()=>h.useContext(y0);function S0(o){const l=h.useRef(void 0);return $d(l.current,o)||(l.current=o),l.current}const Xc=o=>{o.stopPropagation(),o.preventDefault(),o.stopImmediatePropagation()},k0=typeof window<"u"?h.useLayoutEffect:h.useEffect;function Te(o,l,i,c){const d=h.useRef(null),f=h.useRef(!1),p=i instanceof Array?c instanceof Array?void 0:c:i,g=La(o)?o.join(p==null?void 0:p.delimiter):o,x=i instanceof Array?i:c instanceof Array?c:void 0,y=h.useCallback(l,x??[]),C=h.useRef(y);x?C.current=y:C.current=l;const v=S0(p),{activeScopes:w}=w0(),D=v0();return k0(()=>{if((v==null?void 0:v.enabled)===!1||!h0(w,v==null?void 0:v.scopes))return;let T=[],j;const R=(V,P=!1)=>{var S;if(!(p0(V)&&!Ad(V,v==null?void 0:v.enableOnFormTags))){if(d.current!==null){const M=d.current.getRootNode();if((M instanceof Document||M instanceof ShadowRoot)&&M.activeElement!==d.current&&!d.current.contains(M.activeElement)){Xc(V);return}}(S=V.target)!=null&&S.isContentEditable&&!(v!=null&&v.enableOnContentEditable)||va(g,v==null?void 0:v.delimiter).forEach(M=>{var F,H,J,te;if(M.includes((v==null?void 0:v.splitKey)??"+")&&M.includes((v==null?void 0:v.sequenceSplitKey)??">")){console.warn(`Hotkey ${M} contains both ${(v==null?void 0:v.splitKey)??"+"} and ${(v==null?void 0:v.sequenceSplitKey)??">"} which is not supported.`);return}const X=ya(M,v==null?void 0:v.splitKey,v==null?void 0:v.sequenceSplitKey,v==null?void 0:v.useKey,v==null?void 0:v.description);if(X.isSequence){j=setTimeout(()=>{T=[]},(v==null?void 0:v.sequenceTimeoutMs)??1e3);const ae=X.useKey?V.key:fn(V.code);if(zd(ae.toLowerCase()))return;T.push(ae);const Z=(F=X.keys)==null?void 0:F[T.length-1];if(ae!==Z){T=[],j&&clearTimeout(j);return}T.length===((H=X.keys)==null?void 0:H.length)&&(C.current(V,X),j&&clearTimeout(j),T=[])}else if(g0(V,X,v==null?void 0:v.ignoreModifiers)||(J=X.keys)!=null&&J.includes("*")){if((te=v==null?void 0:v.ignoreEventWhen)!=null&&te.call(v,V)||P&&f.current)return;if(d0(V,X,v==null?void 0:v.preventDefault),!f0(V,X,v==null?void 0:v.enabled)){Xc(V);return}C.current(V,X),P||(f.current=!0)}})}},b=V=>{V.code!==void 0&&(Fd(fn(V.code)),((v==null?void 0:v.keydown)===void 0&&(v==null?void 0:v.keyup)!==!0||v!=null&&v.keydown)&&R(V))},_=V=>{V.code!==void 0&&(Od(fn(V.code)),f.current=!1,v!=null&&v.keyup&&R(V,!0))},W=d.current||(p==null?void 0:p.document)||document;return W.addEventListener("keyup",_,p==null?void 0:p.eventListenerOptions),W.addEventListener("keydown",b,p==null?void 0:p.eventListenerOptions),D&&va(g,v==null?void 0:v.delimiter).forEach(V=>D.addHotkey(ya(V,v==null?void 0:v.splitKey,v==null?void 0:v.sequenceSplitKey,v==null?void 0:v.useKey,v==null?void 0:v.description))),()=>{W.removeEventListener("keyup",_,p==null?void 0:p.eventListenerOptions),W.removeEventListener("keydown",b,p==null?void 0:p.eventListenerOptions),D&&va(g,v==null?void 0:v.delimiter).forEach(V=>D.removeHotkey(ya(V,v==null?void 0:v.splitKey,v==null?void 0:v.sequenceSplitKey,v==null?void 0:v.useKey,v==null?void 0:v.description))),T=[],j&&clearTimeout(j)}},[g,v,w]),d}const C0=({onCreateSegment:o,onClearSegments:l,stepBackward:i,stepForward:c,jumpBackward:d,jumpForward:f,setZoom:p,zoom:g,onToggleHelp:x})=>{const{togglePlayback:y,seekTo:C,videoDuration:v}=qr();return Te("space",w=>{w.preventDefault(),y()},{enableOnFormTags:!1}),Te("ArrowLeft",w=>{w.preventDefault(),i()},{enableOnFormTags:!1}),Te("ArrowRight",w=>{w.preventDefault(),c()},{enableOnFormTags:!1}),Te("shift+ArrowLeft",w=>{w.preventDefault(),d()},{enableOnFormTags:!1}),Te("shift+ArrowRight",w=>{w.preventDefault(),f()},{enableOnFormTags:!1}),Te("Home",w=>{w.preventDefault(),C(0)},{enableOnFormTags:!1}),Te("End",w=>{w.preventDefault(),C(v)},{enableOnFormTags:!1}),Te("s",w=>{w.preventDefault(),o()},{enableOnFormTags:!1}),Te("shift+s",w=>{w.preventDefault(),l()},{enableOnFormTags:!1}),Te("=",w=>{w.preventDefault(),p(Math.min(4,g+.25))},{enableOnFormTags:!1}),Te("-",w=>{w.preventDefault(),p(Math.max(.25,g-.25))},{enableOnFormTags:!1}),Te("0",w=>{w.preventDefault(),p(1)},{enableOnFormTags:!1}),Te("1",w=>{w.preventDefault(),C(v*.1)},{enableOnFormTags:!1}),Te("2",w=>{w.preventDefault(),C(v*.2)},{enableOnFormTags:!1}),Te("3",w=>{w.preventDefault(),C(v*.3)},{enableOnFormTags:!1}),Te("4",w=>{w.preventDefault(),C(v*.4)},{enableOnFormTags:!1}),Te("5",w=>{w.preventDefault(),C(v*.5)},{enableOnFormTags:!1}),Te("6",w=>{w.preventDefault(),C(v*.6)},{enableOnFormTags:!1}),Te("7",w=>{w.preventDefault(),C(v*.7)},{enableOnFormTags:!1}),Te("8",w=>{w.preventDefault(),C(v*.8)},{enableOnFormTags:!1}),Te("9",w=>{w.preventDefault(),C(v*.9)},{enableOnFormTags:!1}),Te("ctrl+/",w=>{w.preventDefault(),x==null||x()},{enableOnFormTags:!1}),Te("?",w=>{w.preventDefault(),x==null||x()},{enableOnFormTags:!1}),{hotkeys:{playback:[{key:"Space",action:"Play/Pause"},{key:"←/→",action:"Step frame"},{key:"Shift+←/→",action:"Jump 5 seconds"},{key:"Home/End",action:"Go to start/end"},{key:"1-9",action:"Jump to 10%-90%"}],segments:[{key:"S",action:"Create segment"},{key:"Shift+S",action:"Clear segments"}],zoom:[{key:"+/-",action:"Zoom in/out"},{key:"0",action:"Reset zoom"}],help:[{key:"?",action:"Show shortcuts"},{key:"Ctrl+/",action:"Show shortcuts"}]}}},b0=({isVisible:o,timestamp:l,mousePosition:i,videoDuration:c,videoThumbnails:d})=>{const[f,p]=h.useState("");return h.useEffect(()=>{if(!o||d.length===0){p("");return}const g=Math.floor(l/c*d.length),x=Math.max(0,Math.min(g,d.length-1)),y=d[x];p(y)},[l,o,c,d]),o?s.jsx("div",{className:"fixed z-50 bg-dark-900 border border-dark-700 rounded-md p-2 shadow-lg pointer-events-none",style:{left:i.x+10,top:i.y-120},children:s.jsx("div",{className:"w-40 h-24 bg-dark-950 rounded overflow-hidden mb-1.5 flex items-center justify-center",children:f?s.jsx("img",{src:f,alt:"Frame preview",className:"w-full h-full object-cover"}):null})}):null},j0=({zoom:o,setZoom:l,isPlaying:i,togglePlayback:c,stepBackward:d,stepForward:f,jumpBackward:p,jumpForward:g,formatTime:x,currentTime:y,videoDuration:C,onCreateSegment:v,hasSegments:w,onClearSegments:D})=>s.jsxs("div",{className:"flex justify-between items-center mb-5 px-2.5",children:[s.jsxs("div",{className:"font-mono text-xs text-gray-400",children:[x(y)," / ",x(C)]}),s.jsxs("div",{className:"flex gap-2 items-center",children:[s.jsx("button",{onClick:p,className:"bg-gray-700 border-none text-gray-200 px-2.5 py-1.5 rounded text-xs font-mono hover:bg-gray-600 transition-colors",children:"-5s"}),s.jsx("button",{onClick:d,className:"bg-gray-700 border-none text-gray-200 px-2.5 py-1.5 rounded text-xs font-mono hover:bg-gray-600 transition-colors",children:"-1f"}),s.jsx("button",{onClick:c,className:"bg-pink-500 border-none text-white px-4 py-2 rounded text-xs font-bold font-mono min-w-[60px] hover:bg-pink-600 transition-colors",children:i?"⏸️":"▶️"}),s.jsx("button",{onClick:f,className:"bg-gray-700 border-none text-gray-200 px-2.5 py-1.5 rounded text-xs font-mono hover:bg-gray-600 transition-colors",children:"+1f"}),s.jsx("button",{onClick:g,className:"bg-gray-700 border-none text-gray-200 px-2.5 py-1.5 rounded text-xs font-mono hover:bg-gray-600 transition-colors",children:"+5s"}),s.jsx("button",{onClick:w?D:v,className:`border-none text-white px-3 py-1.5 rounded text-xs font-mono font-bold ml-3 transition-colors ${w?"bg-red-600 hover:bg-red-700":"bg-pink-500 hover:bg-pink-600"}`,children:w?"Clear Segment":"Create Segment"})]}),s.jsxs("div",{className:"flex gap-2 items-center",children:[s.jsx("button",{onClick:()=>l(Math.max(.25,o-.25)),className:"bg-gray-700 border-none text-gray-200 px-2.5 py-1.5 rounded text-xs font-mono hover:bg-gray-600 transition-colors",children:"-"}),s.jsxs("span",{className:"font-mono text-xs text-gray-400 min-w-[50px] text-center",children:[Math.round(o*100),"%"]}),s.jsx("button",{onClick:()=>l(Math.min(4,o+.25)),className:"bg-gray-700 border-none text-gray-200 px-2.5 py-1.5 rounded text-xs font-mono hover:bg-gray-600 transition-colors",children:"+"})]})]}),E0=({videoDuration:o,timelineWidth:l,pixelsPerSecond:i,isDragging:c,setIsDragging:d,onTimeChange:f,onTrimUpdate:p,onHoverChange:g,segments:x,children:y})=>{const C=h.useRef(null),v=h.useCallback(b=>Math.max(0,Math.min(o,b/i)),[i,o]),w=h.useCallback(b=>{if(!C.current||c)return;const _=C.current.getBoundingClientRect(),W=b.clientX-_.left,V=v(W);f(V)},[v,c,f]),D=h.useCallback(b=>{if(!C.current)return;const _=C.current.getBoundingClientRect(),W=b.clientX-_.left,V=Math.max(0,Math.min(o,v(W)));c||g(!0,V,{x:b.clientX,y:b.clientY})},[c,v,o,g]),T=h.useCallback(b=>{if(!c||!C.current)return;const _=C.current.getBoundingClientRect(),W=b.clientX-_.left,V=Math.max(0,Math.min(o,v(W)));if(c.type==="playhead")f(V);else if(c.type==="trim-start"&&c.segmentId){const P=x.find(S=>S.id===c.segmentId);P&&V<P.endTime&&p(c.segmentId,"start",V)}else if(c.type==="trim-end"&&c.segmentId){const P=x.find(S=>S.id===c.segmentId);P&&V>P.startTime&&p(c.segmentId,"end",V)}},[c,v,o,x,f,p]),j=h.useCallback(()=>{d(null)},[d]),R=h.useCallback(()=>{c||g(!1,0,{x:0,y:0})},[c,g]);return h.useEffect(()=>{if(c)return document.addEventListener("mousemove",T),document.addEventListener("mouseup",j),()=>{document.removeEventListener("mousemove",T),document.removeEventListener("mouseup",j)}},[c,T,j]),s.jsx("div",{ref:C,onClick:w,onMouseMove:D,onMouseLeave:R,className:`relative h-32 bg-dark-900 border border-dark-700 rounded overflow-hidden ${c?"cursor-grabbing":"cursor-pointer"}`,style:{width:l},children:y})},N0=({currentTime:o,videoDuration:l,timelineWidth:i,onMouseDown:c})=>{const d=Math.min(i,o/l*i);return s.jsxs(s.Fragment,{children:[s.jsx("div",{className:"absolute top-0 bottom-0 w-0.5 bg-accent-pink z-30 pointer-events-none",style:{left:d}}),s.jsx("div",{onMouseDown:f=>c(f,"playhead"),className:"absolute -top-1 w-3 h-3 bg-accent-pink rounded-full cursor-grab z-31 border-2 border-white shadow-lg",style:{left:d-6}}),s.jsxs("div",{className:"absolute -top-6 bg-accent-pink text-white px-1.5 py-0.5 rounded text-xs font-mono font-bold z-32 whitespace-nowrap",style:{left:Math.max(0,Math.min(i-80,d-40))},children:[Math.floor(o/60),":",Math.floor(o%60).toString().padStart(2,"0"),".",Math.floor(o%1*100).toString().padStart(2,"0")]})]})},T0=({segments:o,videoDuration:l,timelineWidth:i,onMouseDown:c,onTrimSegment:d})=>s.jsx(s.Fragment,{children:o.map(f=>{const p=f.startTime/l*i,g=f.endTime/l*i,x=g-p;return s.jsxs("div",{children:[s.jsx("div",{className:"absolute top-0 h-full bg-accent-pink/30 border-2 border-accent-pink rounded z-20 pointer-events-none",style:{left:p,width:x}}),s.jsx("div",{onMouseDown:y=>c(y,"trim-start",f.id),className:"absolute top-0 w-2 h-full bg-accent-pink cursor-ew-resize z-25 rounded-l border border-white shadow-lg flex items-center justify-center",style:{left:p-4},children:s.jsx("div",{className:"w-0.5 h-3/5 bg-white rounded-sm"})}),s.jsx("div",{onMouseDown:y=>c(y,"trim-end",f.id),className:"absolute top-0 w-2 h-full bg-accent-pink cursor-ew-resize z-25 rounded-r border border-white shadow-lg flex items-center justify-center",style:{left:g-4},children:s.jsx("div",{className:"w-0.5 h-3/5 bg-white rounded-sm"})}),s.jsxs("div",{className:"absolute top-2 text-white text-xs font-mono font-bold z-26 pointer-events-none",style:{left:p+8,textShadow:"1px 1px 2px rgba(0,0,0,0.8)"},children:[Math.floor((f.endTime-f.startTime)*10)/10,"s"]}),s.jsx("button",{onClick:()=>d(f.id,f.startTime,f.endTime),className:"absolute top-2 bg-accent-green border-none text-white px-2 py-1 rounded cursor-pointer text-xs font-mono font-bold z-26 hover:bg-green-600 transition-colors",style:{right:i-g+8},children:"TRIM"})]},f.id)})}),R0=({thumbnails:o,timelineWidth:l,zoom:i})=>{const c=h.useMemo(()=>o,[o]),d=l/Math.max(1,c.length);return console.log("🖼️ TimelineThumbnails render:",{totalThumbnails:o.length,displayThumbnails:c.length,thumbnailWidth:d,zoom:i,timelineWidth:l}),s.jsx("div",{className:"absolute inset-0 flex z-10",children:c.length>0?c.map((f,p)=>s.jsx("div",{className:"h-full bg-cover bg-center opacity-80",style:{width:d,backgroundImage:`url(${f})`,borderRight:p<c.length-1?"1px solid rgba(255,255,255,0.1)":"none"}},p)):s.jsx("div",{className:"w-full h-full bg-dark-800 flex items-center justify-center font-mono text-xs text-dark-500",children:"No thumbnails available"})})},I0=({hotkeys:o,isOpen:l,onToggle:i})=>(Te("Escape",()=>{l&&i()},{enabled:l}),s.jsxs(s.Fragment,{children:[s.jsx("button",{onClick:i,className:"fixed top-4 right-4 z-50 bg-gray-700 hover:bg-gray-600 text-gray-200 p-2 rounded border border-gray-600 transition-colors",title:"Keyboard Shortcuts (?)",children:s.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),l&&s.jsx("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75",children:s.jsxs("div",{className:"bg-gray-800 border border-gray-600 rounded-lg p-6 max-w-md w-full mx-4",children:[s.jsxs("div",{className:"flex justify-between items-center mb-4",children:[s.jsx("h2",{className:"text-lg font-mono font-bold text-pink-500",children:"Keyboard Shortcuts"}),s.jsx("button",{onClick:i,className:"text-gray-400 hover:text-gray-200 transition-colors",children:s.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{children:[s.jsx("h3",{className:"text-sm font-mono font-bold text-gray-300 mb-2",children:"Playback"}),s.jsx("div",{className:"space-y-1",children:o.playback.map((c,d)=>s.jsxs("div",{className:"flex justify-between items-center",children:[s.jsx("kbd",{className:"px-2 py-1 bg-gray-700 border border-gray-600 rounded text-xs font-mono text-gray-200",children:c.key}),s.jsx("span",{className:"text-xs text-gray-400",children:c.action})]},d))})]}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-sm font-mono font-bold text-gray-300 mb-2",children:"Segments"}),s.jsx("div",{className:"space-y-1",children:o.segments.map((c,d)=>s.jsxs("div",{className:"flex justify-between items-center",children:[s.jsx("kbd",{className:"px-2 py-1 bg-gray-700 border border-gray-600 rounded text-xs font-mono text-gray-200",children:c.key}),s.jsx("span",{className:"text-xs text-gray-400",children:c.action})]},d))})]}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-sm font-mono font-bold text-gray-300 mb-2",children:"Zoom"}),s.jsx("div",{className:"space-y-1",children:o.zoom.map((c,d)=>s.jsxs("div",{className:"flex justify-between items-center",children:[s.jsx("kbd",{className:"px-2 py-1 bg-gray-700 border border-gray-600 rounded text-xs font-mono text-gray-200",children:c.key}),s.jsx("span",{className:"text-xs text-gray-400",children:c.action})]},d))})]}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-sm font-mono font-bold text-gray-300 mb-2",children:"Help"}),s.jsx("div",{className:"space-y-1",children:o.help.map((c,d)=>s.jsxs("div",{className:"flex justify-between items-center",children:[s.jsx("kbd",{className:"px-2 py-1 bg-gray-700 border border-gray-600 rounded text-xs font-mono text-gray-200",children:c.key}),s.jsx("span",{className:"text-xs text-gray-400",children:c.action})]},d))})]})]}),s.jsx("div",{className:"mt-4 pt-4 border-t border-gray-700",children:s.jsxs("p",{className:"text-xs text-gray-500 font-mono",children:["Press ",s.jsx("kbd",{className:"px-1 bg-gray-700 border border-gray-600 rounded",children:"Esc"})," to close"]})})]})})]})),M0=()=>{const{project:o,hasVideo:l,addSegment:i,updateSegment:c,removeSegment:d,trimVideo:f,currentTime:p,isPlaying:g,setCurrentTime:x,togglePlayback:y,stepBackward:C,stepForward:v,jumpBackward:w,jumpForward:D,videoElement:T}=qr(),[j,R]=h.useState(1),[b,_]=h.useState(!1),[W,V]=h.useState(null),[P,S]=h.useState({visible:!1,timestamp:0,mousePos:{x:0,y:0}}),M=l?o.video.duration:1,F=h.useMemo(()=>800*j,[j]),H=F/M,J=h.useCallback(O=>{const B=Math.floor(O/60),E=Math.floor(O%60),U=Math.floor(O%1*100);return`${B}:${E.toString().padStart(2,"0")}.${U.toString().padStart(2,"0")}`},[]),te=h.useCallback((O,B,E)=>{O.preventDefault(),V({type:B,segmentId:E})},[]),X=h.useCallback(O=>{x(O)},[x]),ae=h.useCallback((O,B,E)=>{c(O,B==="start"?{startTime:E}:{endTime:E})},[c]),Z=h.useCallback((O,B,E)=>{S({visible:O,timestamp:B,mousePos:E})},[]),K=h.useCallback(async(O,B,E)=>{try{const U=f(B,E);console.log("✅ Segment trimmed successfully (UI-only):",U),Jn("Segment trimmed! Will be processed during export.","success")}catch(U){console.error("❌ Trim failed:",U),Jn(`Trim failed: ${U instanceof Error?U.message:"Unknown error"}`,"error")}},[f]),fe=h.useCallback(()=>{if(o.segments.length>0){Jn("Segment already exists! Drag the trim handles to adjust.","info");return}const O=Math.min(M,10),B=i(0,O);console.log("✅ Created initial segment for trimming:",B),Jn("Segment created! Drag the pink handles to trim, then click Export.","success")},[i,M,o.segments.length]),se=h.useCallback(()=>{o.segments.forEach(O=>d(O.id)),Jn("Segments cleared. Click Create Segment to start again.","info")},[o.segments,d]),{hotkeys:A}=C0({onCreateSegment:fe,onClearSegments:se,stepBackward:C,stepForward:v,jumpBackward:w,jumpForward:D,setZoom:R,zoom:j,onToggleHelp:()=>_(!b)});return l?s.jsxs("div",{className:"h-full flex flex-col bg-dark-950",style:{padding:"8px"},children:[s.jsx(j0,{zoom:j,setZoom:R,isPlaying:g,togglePlayback:y,stepBackward:C,stepForward:v,jumpBackward:w,jumpForward:D,formatTime:J,currentTime:p,videoDuration:M,onCreateSegment:fe,hasSegments:o.segments.length>0,onClearSegments:se}),s.jsx("div",{className:"flex justify-center items-center flex-1 overflow-x-auto overflow-y-hidden py-5",children:s.jsxs(E0,{videoDuration:M,timelineWidth:F,pixelsPerSecond:H,isDragging:W,setIsDragging:V,onTimeChange:X,onTrimUpdate:ae,onHoverChange:Z,segments:o.segments,children:[l&&o.video.thumbnails&&s.jsx(R0,{thumbnails:o.video.thumbnails,timelineWidth:F,zoom:j}),s.jsx(T0,{segments:o.segments,videoDuration:M,timelineWidth:F,onMouseDown:te,onTrimSegment:K}),s.jsx(N0,{currentTime:p,videoDuration:M,timelineWidth:F,onMouseDown:te})]})}),P.visible&&T&&l&&s.jsx(b0,{videoElement:T,isVisible:P.visible,timestamp:P.timestamp,mousePosition:P.mousePos,videoDuration:M,videoThumbnails:o.video.thumbnails||[]}),s.jsx(I0,{hotkeys:A,isOpen:b,onToggle:()=>_(!b)})]}):s.jsx(bd,{mode:"video-editor"})};class P0{constructor(){gt(this,"format","mp4");gt(this,"displayName","MP4");gt(this,"defaultSettings",{format:"mp4",quality:"standard",fps:30,resolution:{width:1920,height:1080,preset:"original"}})}validateSettings(l){return l.fps&&(l.fps<1||l.fps>60)?"FPS must be between 1 and 60":l.resolution.width<128||l.resolution.height<128?"Resolution must be at least 128x128":l.resolution.width>4096||l.resolution.height>4096?"Resolution cannot exceed 4096x4096":null}estimateFileSize(l,i){const c=i.quality==="web"?.5:i.quality==="standard"?1.2:i.quality==="high"?2.5:4;return(l*c).toFixed(1)}async execute(l){const i=window.location.hostname==="localhost"?"http://localhost:3001":window.location.origin,c={videoPath:l.videoPath,startTime:l.startTime,endTime:l.endTime,format:this.format,quality:l.settings.quality,resolution:l.settings.resolution,fps:l.settings.fps},d=await fetch(`${i}/video-simple`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(c)});if(!d.ok)return{success:!1,error:`Export failed: ${d.statusText}`};const f=await d.json();return f.success&&f.downloadUrl?{success:!0,downloadUrl:f.downloadUrl,filename:f.filename}:{success:!1,error:"Export failed: No download URL received"}}}class D0{constructor(){gt(this,"format","gif");gt(this,"displayName","GIF");gt(this,"defaultSettings",{format:"gif",quality:"standard",fps:15,resolution:{width:640,height:360,preset:"medium"},gif:{loop:"infinite",colors:256,dither:!0}})}validateSettings(l){var i;return l.fps&&(l.fps<1||l.fps>30)?"GIF FPS should be between 1 and 30 for optimal file size":l.resolution.width>1920||l.resolution.height>1080?"GIF resolution should not exceed 1920x1080 for reasonable file size":(i=l.gif)!=null&&i.colors&&(l.gif.colors<2||l.gif.colors>256)?"GIF colors must be between 2 and 256":null}estimateFileSize(l,i){var g;const c=i.quality==="web"?1:i.quality==="standard"?2:i.quality==="high"?4:6,f=i.resolution.width*i.resolution.height/(640*360),p=(((g=i.gif)==null?void 0:g.colors)||256)/256;return(l*c*f*p).toFixed(1)}async execute(l){const i=window.location.hostname==="localhost"?"http://localhost:3001":window.location.origin,c={videoPath:l.videoPath,startTime:l.startTime,endTime:l.endTime,format:this.format,quality:l.settings.quality,resolution:l.settings.resolution,fps:l.settings.fps,gif:l.settings.gif},d=await fetch(`${i}/export/${this.format}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(c)});if(!d.ok)return{success:!1,error:`GIF export failed: ${d.statusText}`};const f=await d.json();return f.success&&f.downloadUrl?{success:!0,downloadUrl:f.downloadUrl,filename:f.filename}:{success:!1,error:"GIF export failed: No download URL received"}}}class _0{constructor(){gt(this,"format","webm");gt(this,"displayName","WebM");gt(this,"defaultSettings",{format:"webm",quality:"standard",fps:30,resolution:{width:1920,height:1080,preset:"original"}})}validateSettings(l){return l.fps&&(l.fps<1||l.fps>60)?"FPS must be between 1 and 60":l.resolution.width<128||l.resolution.height<128?"Resolution must be at least 128x128":l.resolution.width>4096||l.resolution.height>4096?"Resolution cannot exceed 4096x4096":null}estimateFileSize(l,i){const c=i.quality==="web"?.4:i.quality==="standard"?1:i.quality==="high"?2:3.5;return(l*c).toFixed(1)}async execute(l){const i=window.location.hostname==="localhost"?"http://localhost:3001":window.location.origin,c={videoPath:l.videoPath,startTime:l.startTime,endTime:l.endTime,format:this.format,quality:l.settings.quality,resolution:l.settings.resolution,fps:l.settings.fps},d=await fetch(`${i}/export/${this.format}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(c)});if(!d.ok)return{success:!1,error:`WebM export failed: ${d.statusText}`};const f=await d.json();return f.success&&f.downloadUrl?{success:!0,downloadUrl:f.downloadUrl,filename:f.filename}:{success:!1,error:"WebM export failed: No download URL received"}}}class ba{static getStrategy(l){return this.strategies.get(l.toLowerCase())||null}static getAllStrategies(){return Array.from(this.strategies.values())}static getSupportedFormats(){return Array.from(this.strategies.keys())}static getDefaultSettingsForFormat(l){const i=this.getStrategy(l);return(i==null?void 0:i.defaultSettings)||{}}}gt(ba,"strategies",new Map([["mp4",new P0],["gif",new D0],["webm",new _0]]));const L0=({settings:o,onSettingsChange:l,originalWidth:i,originalHeight:c})=>{const d=[{value:"original",label:"Original",width:i,height:c},{value:"large",label:"Full HD",width:1920,height:1080},{value:"medium",label:"HD",width:1280,height:720},{value:"small",label:"Small",width:640,height:360},{value:"custom",label:"Custom",width:o.resolution.width,height:o.resolution.height}],f=g=>{const x=d.find(y=>y.value===g);x&&l({...o,resolution:{preset:g,width:x.width,height:x.height}})},p=(g,x)=>{l({...o,resolution:{...o.resolution,preset:"custom",[g]:x}})};return s.jsxs("div",{className:"mb-4",children:[s.jsx("h3",{className:"text-mono-upper text-pink-500 mb-2",children:"Resolution"}),s.jsx("div",{className:"flex gap-1.5 flex-wrap mb-2",children:d.map(g=>s.jsx("button",{onClick:()=>f(g.value),className:`px-3 py-1.5 rounded border text-xs font-mono transition-all ${o.resolution.preset===g.value?"border-pink-500 bg-pink-500/20 text-pink-500 font-bold":"border-gray-600 bg-gray-700 text-gray-200 hover:bg-gray-600"}`,children:g.label},g.value))}),o.resolution.preset==="custom"&&s.jsxs("div",{className:"flex gap-1.5 items-center mt-1.5",children:[s.jsx("input",{type:"number",min:"128",max:"4096",value:o.resolution.width,onChange:g=>p("width",parseInt(g.target.value)||1920),placeholder:"Width",className:"w-[70px] px-1.5 py-1 bg-gray-700 border border-gray-600 rounded text-gray-200 text-xs font-mono"}),s.jsx("span",{className:"text-gray-400 text-xs font-mono",children:"×"}),s.jsx("input",{type:"number",min:"128",max:"4096",value:o.resolution.height,onChange:g=>p("height",parseInt(g.target.value)||1080),placeholder:"Height",className:"w-[70px] px-1.5 py-1 bg-gray-700 border border-gray-600 rounded text-gray-200 text-xs font-mono"})]})]})},z0=({settings:o,onSettingsChange:l})=>{var f;if(o.format!=="gif")return null;const i=[{value:"infinite",label:"Loop Forever"},{value:"once",label:"Play Once"},{value:"3",label:"Loop 3 Times"},{value:"5",label:"Loop 5 Times"}],c=[{value:256,label:"256 Colors (Best Quality)"},{value:128,label:"128 Colors (Good Quality)"},{value:64,label:"64 Colors (Small Size)"},{value:32,label:"32 Colors (Smallest)"}],d=(p,g)=>{l({...o,gif:{loop:"infinite",colors:256,dither:!0,...o.gif,[p]:g}})};return s.jsxs("div",{className:"mb-4",children:[s.jsx("h3",{className:"text-mono-upper text-pink-500 mb-2",children:"GIF Options"}),s.jsxs("div",{className:"mb-4",children:[s.jsx("label",{className:"block text-xs font-mono font-bold text-gray-200 mb-2",children:"Loop Behavior"}),s.jsx("div",{className:"flex flex-col gap-1",children:i.map(p=>{var g;return s.jsxs("label",{className:"flex items-center gap-2 cursor-pointer py-1",children:[s.jsx("input",{type:"radio",name:"gifLoop",value:p.value,checked:((g=o.gif)==null?void 0:g.loop)===p.value,onChange:x=>d("loop",x.target.value),className:"accent-pink-500"}),s.jsx("span",{className:"text-xs font-mono text-gray-200",children:p.label})]},p.value)})})]}),s.jsxs("div",{className:"mb-4",children:[s.jsx("label",{className:"block text-xs font-mono font-bold text-gray-200 mb-2",children:"Color Palette"}),s.jsx("div",{className:"flex flex-col gap-1",children:c.map(p=>{var g;return s.jsxs("label",{className:"flex items-center gap-2 cursor-pointer py-1",children:[s.jsx("input",{type:"radio",name:"gifColors",value:p.value,checked:((g=o.gif)==null?void 0:g.colors)===p.value,onChange:x=>d("colors",parseInt(x.target.value)),className:"accent-pink-500"}),s.jsx("span",{className:"text-xs font-mono text-gray-200",children:p.label})]},p.value)})})]}),s.jsx("div",{children:s.jsxs("label",{className:"flex items-center gap-2 cursor-pointer",children:[s.jsx("input",{type:"checkbox",checked:((f=o.gif)==null?void 0:f.dither)||!1,onChange:p=>d("dither",p.target.checked),className:"accent-pink-500"}),s.jsxs("div",{children:[s.jsx("div",{className:"text-xs font-mono font-bold text-gray-200",children:"Enable Dithering"}),s.jsx("div",{className:"text-[10px] font-mono text-gray-400",children:"Improves color gradients but increases file size"})]})]})})]})},F0=({validation:o,className:l="",showOnlyErrors:i=!1,showOnlyWarnings:c=!1,maxMessages:d,compact:f=!1})=>{let p=o.messages;return i?p=p.filter(g=>g.type==="error"):c&&(p=p.filter(g=>g.type==="warning")),d&&d>0&&(p=p.slice(0,d)),p.length===0?null:s.jsx("div",{className:`validation-messages ${l}`,children:p.map((g,x)=>s.jsx(O0,{message:g,compact:f},`${g.code}-${x}`))})},O0=({message:o,compact:l=!1})=>{const i=()=>{const f=l?"flex items-center gap-1.5 px-2 py-1 rounded text-xs font-mono":"flex items-start gap-2 px-3 py-2 rounded-md text-sm font-mono";switch(o.type){case"error":return`${f} bg-red-900/20 border border-red-700/30 text-red-300`;case"warning":return`${f} bg-yellow-900/20 border border-yellow-700/30 text-yellow-300`;case"info":return`${f} bg-blue-900/20 border border-blue-700/30 text-blue-300`;default:return`${f} bg-gray-900/20 border border-gray-700/30 text-gray-300`}},c=()=>{const p=`${l?"w-3 h-3":"w-4 h-4"} flex-shrink-0 ${l?"mt-0":"mt-0.5"}`;switch(o.type){case"error":return s.jsx("svg",{className:`${p} text-red-400`,fill:"currentColor",viewBox:"0 0 20 20",children:s.jsx("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})});case"warning":return s.jsx("svg",{className:`${p} text-yellow-400`,fill:"currentColor",viewBox:"0 0 20 20",children:s.jsx("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})});case"info":return s.jsx("svg",{className:`${p} text-blue-400`,fill:"currentColor",viewBox:"0 0 20 20",children:s.jsx("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})});default:return null}},d=f=>({fps:"FPS",resolution:"Resolución",quality:"Calidad",colors:"Colores",format:"Formato"})[f]||f;return s.jsxs("div",{className:i(),children:[c(),s.jsxs("div",{className:"flex-1 min-w-0",children:[!l&&s.jsx("div",{className:"text-xs opacity-75 mb-0.5",children:d(o.field)}),s.jsx("div",{className:l?"truncate":"",children:o.message})]})]})},A0=({validation:o,className:l=""})=>{if(o.messages.length===0)return s.jsxs("div",{className:`flex items-center gap-2 px-3 py-2 rounded-md bg-green-900/20 border border-green-700/30 text-green-300 text-sm font-mono ${l}`,children:[s.jsx("svg",{className:"w-4 h-4 text-green-400",fill:"currentColor",viewBox:"0 0 20 20",children:s.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})}),s.jsx("span",{children:"Configuración válida"})]});const i=o.messages.filter(d=>d.type==="error").length,c=o.messages.filter(d=>d.type==="warning").length;return s.jsxs("div",{className:`validation-summary ${l}`,children:[i>0&&s.jsxs("div",{className:"flex items-center gap-2 px-3 py-2 rounded-md bg-red-900/20 border border-red-700/30 text-red-300 text-sm font-mono",children:[s.jsx("svg",{className:"w-4 h-4 text-red-400",fill:"currentColor",viewBox:"0 0 20 20",children:s.jsx("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})}),s.jsxs("span",{children:[i," error",i!==1?"es":""," - Exportación bloqueada"]})]}),c>0&&i===0&&s.jsxs("div",{className:"flex items-center gap-2 px-3 py-2 rounded-md bg-yellow-900/20 border border-yellow-700/30 text-yellow-300 text-sm font-mono",children:[s.jsx("svg",{className:"w-4 h-4 text-yellow-400",fill:"currentColor",viewBox:"0 0 20 20",children:s.jsx("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})}),s.jsxs("span",{children:[c," advertencia",c!==1?"s":""," - Exportación disponible"]})]})]})},$0=()=>{var D,T,j,R;const{project:o,hasVideo:l}=qr(),[i,c]=h.useState(!1),[d,f]=h.useState(0),[p,g]=h.useState({format:"mp4",quality:"standard",resolution:{width:((D=o.video)==null?void 0:D.width)||1920,height:((T=o.video)==null?void 0:T.height)||1080,preset:"original"},fps:30,gif:{loop:"infinite",colors:256,dither:!0}}),x={format:p.format,fps:p.fps,quality:p.quality,resolution:p.resolution,gif:p.gif},y=Da(x),C=h.useCallback(b=>{const _=ba.getStrategy(b);_&&g(W=>({...W,..._.defaultSettings,format:b}))},[]);h.useEffect(()=>{l&&o.video&&g(b=>({...b,resolution:{...b.resolution,width:o.video.width,height:o.video.height}}))},[l,o.video]);const v=o.segments.reduce((b,_)=>b+(_.endTime-_.startTime),0),w=async()=>{var b,_;c(!0),f(0);try{if(!y.canExport){const F=y.messages.filter(H=>H.type==="error").map(H=>H.message).join(`
`);Ut(`Configuración inválida: ${F}`,"error"),c(!1);return}if(o.segments.length===0){Ut("No segments found. Please create segments first.","warning"),c(!1);return}if(!((_=(b=o.video)==null?void 0:b.uploadedInfo)!=null&&_.path)){Ut("Original video path not available. Please re-upload video.","warning"),c(!1);return}const W=ba.getStrategy(p.format);if(!W){Ut(`Unsupported export format: ${p.format}`,"error"),c(!1);return}const V=W.validateSettings(p);if(V){Ut(`Settings error: ${V}`,"error"),c(!1);return}console.log("🎬 Starting export with strategy:",{format:p.format,segments:o.segments,settings:p,totalDuration:v});const P=o.segments[0],S={videoPath:o.video.uploadedInfo.path,startTime:P.startTime,endTime:P.endTime,settings:p},M=await W.execute(S);if(M.success&&M.downloadUrl){const F=window.location.hostname==="localhost"?"http://localhost:3001":window.location.origin,H=document.createElement("a");H.href=`${F}${M.downloadUrl}`,H.download=M.filename||`exported_video.${p.format}`,H.setAttribute("target","_blank"),H.style.display="none",document.body.appendChild(H),H.click(),setTimeout(()=>{document.body.removeChild(H)},100),console.log("✅ Export completed and downloaded:",M.filename),Ut(`Export completed! Downloaded: ${M.filename}`,"success"),f(100)}else throw new Error(M.error||"Export failed");c(!1)}catch(W){console.error("❌ Export failed:",W),Ut(`Export failed: ${W instanceof Error?W.message:"Unknown error"}`,"error"),c(!1),f(0)}};return l?s.jsxs("div",{className:"h-full flex flex-col gap-4",children:[i&&s.jsxs("div",{className:"p-3 bg-dark-900 rounded border border-dark-650",children:[s.jsxs("div",{className:"text-sm font-mono text-accent-green mb-2",children:["Exporting... ",d,"%"]}),s.jsx("div",{className:"w-full h-2 bg-dark-700 rounded-full overflow-hidden",children:s.jsx("div",{className:"h-full bg-accent-green transition-all duration-300 ease-out",style:{width:`${d}%`}})})]}),s.jsxs("div",{className:"space-y-3",children:[s.jsx(Id,{currentFormat:p.format,onFormatChange:b=>C(b),mode:"video-editor"}),s.jsx(Md,{currentQuality:p.quality,onQualityChange:b=>g(_=>({..._,quality:b})),mode:"video-editor"}),s.jsx(L0,{settings:p,onSettingsChange:g,originalWidth:((j=o.video)==null?void 0:j.width)||1920,originalHeight:((R=o.video)==null?void 0:R.height)||1080}),s.jsx(z0,{settings:p,onSettingsChange:g}),y.messages.length>0&&s.jsx("div",{className:"mt-4",children:s.jsx(F0,{validation:y,className:"space-y-2",maxMessages:3})})]}),s.jsx(A0,{validation:y,className:"mb-3"}),s.jsx("button",{onClick:w,disabled:i||o.segments.length===0||!y.canExport,className:`mt-auto w-full p-4 rounded-lg font-mono font-bold text-lg uppercase transition-all duration-200 ${i||o.segments.length===0||!y.canExport?"bg-dark-600 cursor-not-allowed opacity-60 text-dark-400":"bg-accent-green hover:bg-green-600 text-white shadow-lg"}`,title:y.canExport?void 0:`Configuración inválida: ${y.messages.filter(b=>b.type==="error").map(b=>b.message).join(", ")}`,children:i?`⏳ Exporting ${p.format.toUpperCase()}...`:y.canExport?`🚀 Export ${p.format.toUpperCase()}`:"❌ Configuración Inválida"})]}):s.jsx(Pd,{mode:"video-editor"})},U0=()=>s.jsxs(i0,{children:[s.jsxs("div",{className:"app-container custom-scrollbar",children:[s.jsx(Ma,{currentMode:"video-editor"}),s.jsxs("div",{className:"flex-1 flex flex-col min-h-0",children:[s.jsxs("div",{className:"flex-1 flex min-h-0",style:{gap:"4px"},children:[s.jsx(yd,{children:s.jsx(a0,{})}),s.jsx(wd,{children:s.jsx(s0,{})}),s.jsx(Sd,{children:s.jsx($0,{})})]}),s.jsx(kd,{children:s.jsx(M0,{})})]})]}),s.jsx(Dd,{})]}),B0=()=>s.jsx("div",{style:{height:"100vh",backgroundColor:"#0a0a0b",color:"white",display:"flex",flexDirection:"column",fontFamily:'"Space Mono", monospace',overflow:"hidden"},children:s.jsx(e0,{})}),V0=()=>s.jsx("div",{style:{height:"100vh",backgroundColor:"#0a0a0b",color:"white",display:"flex",flexDirection:"column",fontFamily:'"Space Mono", monospace',overflow:"hidden"},children:s.jsx(U0,{})}),W0=()=>s.jsx(ch,{children:s.jsxs($m,{children:[s.jsx(ul,{path:"/",element:s.jsx(wh,{})}),s.jsx(ul,{path:"/slideshow",element:s.jsx(B0,{})}),s.jsx(ul,{path:"/video-editor",element:s.jsx(V0,{})})]})}),H0=h.createContext(void 0),Q0=({children:o})=>{const[l,i]=h.useState([]),[c,d]=h.useState([]),[f,p]=h.useState(null),g=h.useCallback(R=>{i(b=>[...b,R])},[]),x=h.useCallback(R=>{i(b=>b.filter(_=>_.id!==R)),d(b=>b.filter(_=>_.mediaId!==R))},[]),y=h.useCallback(R=>{const b=l.find(W=>W.id===R);if(!b)return;const _={id:`frame_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,mediaId:R,duration:b.duration*1e3,transition:{type:"none",duration:500}};d(W=>[...W,_])},[l]),C=h.useCallback(R=>{d(b=>b.filter(_=>_.id!==R))},[]),v=h.useCallback((R,b)=>{d(_=>_.map(W=>W.id===R?{...W,...b}:W))},[]),w=h.useCallback((R,b)=>{d(_=>{const W=Array.from(_),[V]=W.splice(R,1);return W.splice(b,0,V),W})},[]),D=h.useCallback(()=>{d([])},[]),T=h.useCallback(()=>{i([]),d([]),p(null)},[]),j={mediaItems:l,timelineFrames:c,sessionId:f,setSessionId:p,addMedia:g,removeMedia:x,addToTimeline:y,removeFromTimeline:C,updateFrame:v,reorderTimeline:w,clearTimeline:D,clearAll:T};return s.jsx(H0.Provider,{value:j,children:o})};qp.createRoot(document.getElementById("root")).render(s.jsx(h.StrictMode,{children:s.jsx(Q0,{children:s.jsx(W0,{})})}));
