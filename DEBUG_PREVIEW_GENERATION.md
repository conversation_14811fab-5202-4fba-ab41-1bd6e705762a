# Problema de Generación de Vista Previa - Documento de Debug

## Descripción del Problema

**Fecha:** 2024-07-16  
**Contexto:** Al añadir imágenes individualmente a la línea de tiempo, la vista previa no se generaba automáticamente, a diferencia de cuando se usaba el botón "ALL TO TIMELINE".

## Análisis del Problema

### Comportamiento Observado
- Al hacer clic en "+" para añadir una imagen individualmente, la vista previa no se generaba
- Al usar "ALL TO TIMELINE", la vista previa se generaba correctamente
- La función `generatePreview` estaba siendo llamada en ambos casos

### Causa Raíz Identificada

El problema estaba en el componente `Preview.tsx`, específicamente en el `useEffect` que controla la generación automática de la vista previa:

```typescript
// Preview.tsx - Línea problemática
useEffect(() => {
  if (hasTimeline && !preview.url && !preview.isGenerating && !preview.error) {
    generatePreview();
  }
}, [hasTimeline]); // Solo dependía de hasTimeline
```

Este efecto solo se activaba cuando `hasTimeline` cambiaba de `false` a `true`. Al añadir imágenes individualmente, `hasTimeline` ya era `true`, por lo que el efecto no se ejecutaba.

## Solución Implementada

### 1. Modificación en Preview.tsx
Se añadió un nuevo `useEffect` que se activa cuando `preview.url` se establece a `null` (indicando que la línea de tiempo ha cambiado):

```typescript
useEffect(() => {
  if (preview.url === null && hasTimeline && !preview.isGenerating && !preview.error) {
    generatePreview();
  }
}, [preview.url, hasTimeline, preview.isGenerating, preview.error, generatePreview]);
```

### 2. Mejoras en Logging

#### usePreviewGeneration.ts
Se añadieron logs detallados para facilitar futuras depuraciones:

```typescript
console.log(`[PreviewGeneration] Buscando imagen para imageId: ${item.imageId}`);
console.log(`[PreviewGeneration] Imagen encontrada: ${image ? 'Sí' : 'No'}`);
console.log(`[PreviewGeneration] Filename: ${filename}`);
console.log('[PreviewGeneration] Imágenes disponibles:', projectImages.map(img => ({
  id: img.id,
  name: img.name,
  hasUploadedInfo: !!img.uploadedInfo
})));
```

#### useSlideshow.ts
Se mejoró la función `addToTimeline` con verificaciones y logs:

```typescript
const timeoutId = setTimeout(() => {
  if (!sessionId) {
    console.warn('No sessionId available for preview generation');
    return;
  }
  
  console.log('Generating preview after adding to timeline:', {
    timelineLength: timeline.length,
    imagesCount: project.images.length,
    sessionId
  });
  
  previewGeneration.generatePreview();
}, 300);
```

### 3. Ajuste de Timing
Se redujo el timeout de 1000ms a 300ms para una respuesta más rápida.

## Archivos Modificados

1. **frontend/src/slideshow/components/Preview.tsx** - Añadido nuevo useEffect para generación automática
2. **frontend/src/slideshow/hooks/usePreviewGeneration.ts** - Mejorado logging
3. **frontend/src/slideshow/hooks/useSlideshow.ts** - Optimizado timeout y añadido logging

## Verificación de la Solución

Para verificar que la solución funciona:
1. Añadir una imagen individualmente con el botón "+"
2. Observar que la vista previa se genera automáticamente
3. Verificar en la consola los logs de generación

## Notas para Futuras Depuraciones

- Si el problema persiste, verificar que `sessionId` esté disponible
- Revisar que las imágenes tengan `uploadedInfo.filename` correcto
- Monitorear los logs en la consola del navegador
- Verificar que el backend responda correctamente a las peticiones de preview

## Referencias

- Issue original: Vista previa no se genera al añadir imágenes individualmente
- Solución: Activar generación cuando `preview.url === null`
- Componentes involucrados: Preview.tsx, usePreviewGeneration.ts, useSlideshow.ts